using SpecFlowProject.Hooks;
using SpecFlowProject.Pom.Pages;
using TechTalk.SpecFlow;
using SpecFlowProject.BusinessObjects;
using System.Collections;


namespace SpecFlowProject.Steps
{
    [Binding]
    public class CommomSteps
    {
        private ScenarioContext _scenarioContext;
        readonly Context _context;
        readonly Common common;
        readonly CreatePoPage createPoPage;

        public CommomSteps(Context context,ScenarioContext scenarioContext)
        {
            _context = context;
            common = new Common(_context.Page!);
            createPoPage = new CreatePoPage(_context.Page!);
            _scenarioContext = scenarioContext;
        }

        [Given(@"Open the Web url")]
        public async Task OpenApplication()
        {
            await common.OpenApplicationUrl();
            _scenarioContext["commonContext"] = new CommonContext();
        }

        [Then(@"Login to site with username and password")]
        public async Task LoginIntoApplication()
        {
            await common.Login();
        }

        [Then(@"Login to site with username and password have blind receive off")]
        public async Task LoginIntoApplicationWithBlindReceiveOff()
        {
            await common.LoginWithBlindReceiveOff();
        }

        [Then(@"Login to site with Vice President Credentials")]
        public async Task LoginIntoApplicationWithVicePresident()
        {
            await common.LoginWithVicePresidentCredentials();
        }

        [Then(@"Login to site with calendar year Credentials")]
        public async Task LoginIntoApplicationWithCalendarYear()
        {
            await common.LoginWithCalendarYearCredentials();
        }
        [Then(@"Login to site with management username and password")]
        public async Task LoginIntoApplicationWithManagementUser()
        {
            await common.LoginIntoApplicationWithManagementUser();
        }
        [Then(@"Login to site with Receiving Off Credentials")]
        public async Task LoginIntoApplicationWithReceivingOff()
        {
            await common.LoginWithReceivingOffCredentials();
        }
        [Then(@"Click Home")]
        public async Task LoadHome()
        {
            await common.LoadHome();
        }
       [Then(@"HomePage Loaded")]
       public async Task NotNowpopup()
       {
           await common.HomePageLoaded();
        }

        

        [Then(@"logoff")]
        public async Task logout()
        {
            await common.LogOffClick();
        }

        [Then(@"proxylogoff")]
        public async Task proxylogout()
        {
            await common.LogOffProxyUser();
        }

        [Then(@"Create Po for supplier")]
        public async Task CreatePoFromSupplier()
        {
            CommonContext commonContext = (CommonContext)_scenarioContext["commonContext"];
            if(commonContext.Roles!=null && commonContext.Roles.Count>0 && (commonContext.Roles.FirstOrDefault(stringToCheck => stringToCheck.Contains("CanCreateOfflinePo"))==null)){
                Assert.Fail("User does not have permission to create offline po perm 680");
            }
            await createPoPage.OpenCreatePoPage();
            await createPoPage.CreatePoForFirstSearchResultSupplier((CommonContext)_scenarioContext["commonContext"]);
        }

        [Then(@"Create Po for supplier with transmission type log only")]
        public async Task  ThenCreatePoForSupplierWithTransmissionTypeLogOnly()
        {
            CommonContext commonContext = (CommonContext)_scenarioContext["commonContext"];
            if(commonContext.Roles!=null && commonContext.Roles.Count>0 && (commonContext.Roles.FirstOrDefault(stringToCheck => stringToCheck.Contains("CanCreateOfflinePo"))==null)){
                Assert.Fail("User does not have permission to create offline po perm 680");
            }
            await createPoPage.OpenCreatePoPage();
            await createPoPage.SearchForSupplier("A COUNTRY ROSE");
            Thread.Sleep(2000);
            await createPoPage.CreatePoForFirstSearchResultSupplier((CommonContext)_scenarioContext["commonContext"]);
        }


        [Then(@"Expand Offline")]
        public async Task ExpandOffLine()
        {
            CommonContext commonContext = (CommonContext)_scenarioContext["commonContext"];
            if(commonContext.Roles!=null && commonContext.Roles.Count>0 && (commonContext.Roles.FirstOrDefault(stringToCheck => stringToCheck.Contains("CanCreateOfflinePo"))==null)){
                Assert.Fail("User does not have permission to create offline po perm 680");
            }
            await common.ExpandOffLine();
        }

        [Then(@"Select Company")]
        public async Task SelectCompany()
        {
            await common.SelectACompany();
        }
        private async Task VerifyExcel(Table table){
            ArrayList myList = new ArrayList(); 
            foreach(TableRow row in table.Rows){
               string column = row["columns"];
               myList.Add(column);
            }
            await common.DownLoadExcel(myList);
        }
        [Then(@"Download Excel and Verify Checkbook excel grid fields")]
        public async Task DownLoadExcel(Table table)
        {
            await VerifyExcel(table);
        }

        [Then(@"Change user in work area with user (.*)")]
        public async Task ChangeUser(string user)
        {
            CommonContext commonContext =(CommonContext)_scenarioContext["commonContext"];
            commonContext.ChangeUser = user;
            await common.ChangeUser(user);
        }
        [Then(@"Change user in work area to parent")]
        public async Task ChangeUserToParent()
        {
            await common.ChangeUser(string.Empty, true);
        }
        [Then(@"Download Excel and Verify Department excel grid fields")]
        public async Task DownLoadExcelDepart(Table table)
        {
            await VerifyExcel(table);
        }
        [Then(@"Validate and filter fields")]
        public async Task FilterList(Table table)
        {
            Dictionary<string,Dictionary<string,string>> filterList = new Dictionary<string, Dictionary<string,string>>();
            Dictionary<string,string> filterList1 = new Dictionary<string,string>();  
            foreach(TableRow row in table.Rows){
               string column = row["columns"];
               string filter = row["filterValues"];
               string indexValues = row["indexValues"];
               filterList1.Add(column,filter);
               filterList.Add(indexValues,filterList1);
            }
            await common.FilterList(filterList);
        }

        [Then(@"Recall the PO")]
        public async Task RecallThePO()
        {
            await common.RecallPO();
        }

        [Then(@"Decline the PO")]
        public async Task DeclineThePO()
        {
            await common.DeclinedPO((CommonContext)_scenarioContext["commonContext"]);
        }

        [Then(@"Reopen the PO")]
        public async Task ReopenThePO()
        {
            await common.ReOpenPO((CommonContext)_scenarioContext["commonContext"]);
        }
        [Then(@"Login to site with superadmin username and password")]
        public async Task  Exportfunwithsuperadmin()
        {
            await common.LoginWithSuperadminCredentials();
        }
    }
}