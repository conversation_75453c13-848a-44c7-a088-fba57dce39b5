using Microsoft.Playwright;
using Newtonsoft.Json.Linq;
using SpecFlowProject.Utils;
using SpecFlowProject.BusinessObjects;
using System;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace SpecFlowProject.Pom.Pages;
public class Favorites: Base
{
    private ILocator _favoritesProductsLink => Page.Locator("h2:has-text('Favorite Products')");
    private ILocator _ManageFolders => Page.Locator("a:has-text('Manage Folders')");
    private ILocator _NewFolder => Page.Locator("#newButton");
    private ILocator _FolderName => Page.Locator("#favMaintName");
    private ILocator _SelectGLCode => Page.Locator("#favoriteFolderSelectGLCode li:nth-child(1)");
    private ILocator _SaveFolder => Page.Locator("#saveButton");
    private ILocator _shareFolder => Page.Locator("#shareButton");
    private ILocator _ApplyButton => Page.Locator("button:has-text('Apply')");
    private ILocator _closeButton => Page.Locator("//html//body//div[24]//div[11]//div//button[2]");
    private ILocator _SharedFolderName => Page.Locator("#favoriteFolderSelectCopy").GetByText("Shared Folder", new() { Exact = true });
    private ILocator _OwnedFolderName => Page.Locator("#favoriteFolderSelectCopy li:has-text('Owned Folder')");
    private ILocator _companiesList => Page.Locator("#dialog-operation-copyshare #treegridCompanies tbody tr");
    private ILocator _CompaniesUser => Page.Locator("#dialog-operation-copyshare #gview_treegridUsers tbody tr");
    private ILocator _successToaster => Page.Locator(".toast.toast-success");
    private ILocator _selectLineItem => Page.Locator(".checkboxContainer input");
    private ILocator _addProducts => Page.Locator("#btnFavoriteFolderAdd");
    private ILocator _FolderSelect => Page.Locator("#favoriteFoldersListBox");
    private ILocator _AddFavoritesBtn => Page.Locator("button:has-text('Add to Favorites')");
    private ILocator _OpenOwnedFolder => Page.Locator("a:has-text('Owned Folder')");
    private ILocator _OpenSharedFolder => Page.GetByRole(AriaRole.Link, new() { Name = "Shared Folder", Exact = true });
    private ILocator _filterItem => Page.Locator("#filterTextbox");
    private ILocator _supplierName => Page.Locator("#favoriteSummaryGrid tbody tr:nth-child(2) td:nth-child(7)");
    private ILocator _summaryViewButton => Page.Locator("#summaryButton");

    // Locators for the new line item fields
    private ILocator _currentDocQtyField => _iframe.Locator(".k-grid-content tr:last-child td:nth-child(6) input");
    private ILocator _itemNumberField => _iframe.Locator(".k-grid-content tr:last-child td:nth-child(8) input");
    private ILocator _descriptionField => _iframe.Locator(".k-grid-content tr:last-child td:nth-child(9) input");
    private ILocator _glCodeDropdown => _iframe.Locator(".k-grid-content tr:last-child td:nth-child(10) span.k-dropdownlist span span");
    private ILocator _glCodeList => _iframe.Locator(".k-list ul li:nth-child(1)");
    private ILocator _uomField => _iframe.Locator(".k-grid-content tr:last-child td:nth-child(11) input");
    private ILocator _priceField => _iframe.Locator(".k-grid-content tr:last-child td:nth-child(12) input");
    private ILocator Supplier =>_iframe.Locator(".slide-pane__content .k-dropdownlist");
    private ILocator CreateButton => _iframe.Locator(".flex.mt-2 div:nth-child(1) button");
    private ILocator _dropdownListItem => Page.FrameLocator("#v4Container").Locator("ul[role='listbox']");
    private ILocator _confirmCreate => _iframe.GetByRole(AriaRole.Button, new() { Name = "Confirm", Exact = true });
    private ILocator _invPopupBtn => _iframe.Locator(".k-window-actions.k-dialog-actions button");
    private ILocator _ownGLCode => Page.Locator("#favoriteFolderSelectGLCode li");
    private ILocator _currentDocQty => _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child(1) td:nth-child(6) input");
    private ILocator _changePObtn => _iframe.Locator("button:has-text('Change PO Assignment')");
    private ILocator _searchPO => _iframe.Locator(".k-window-content div:nth-child(2) div div input").Nth(0);
    private ILocator _nextBtn => _iframe.Locator(".k-window-content div nav button");
    private ILocator _applyToThisPO => _iframe.Locator(".k-window-content div nav button").Nth(0);
    private ILocator _POTextField => _iframe.Locator("div:nth-child(3) > div.relative > span > span input");
    private ILocator _acceptedPO => Page.Locator(".btnActionGreen");

    public Favorites(IPage page) : base(page)
    {
    }

    public async Task OpenFavorites(CommonContext commonContext)
    {
        _logger.Debug("Opening Favorites page");
        await _favoritesProductsLink.ClickAsync();
        _logger.Debug("Favorites page opened successfully");
    }

    public async Task AddNewOwnedFolder(CommonContext commonContext)
    {
        _logger.Debug("Adding new owned folder");
        await _ManageFolders.ClickAsync();
        bool isFolderPresent = await _OwnedFolderName.IsVisibleAsync();
        
        if (isFolderPresent)
        {
            _logger.Debug("Owned folder already exists, clicking on it");
            await _OwnedFolderName.ClickAsync();
            commonContext.OwnedGLCode = await _ownGLCode.TextContentAsync();
            commonContext.OwnedGLCode = ExtractGLCode(commonContext.OwnedGLCode);
            commonContext.OwnedGLCode = NormalizeGLCode(commonContext.OwnedGLCode);
            _logger.Information("Using existing owned folder with GL Code: {GLCode}", commonContext.OwnedGLCode);
        }
        else
        {
            _logger.Debug("Creating new owned folder: {FolderName}", commonContext.OwnedFolderName);
            await _NewFolder.ClickAsync();
            await _FolderName.FillAsync(commonContext.OwnedFolderName);
            await _SelectGLCode.ClickAsync();
            await _SaveFolder.ClickAsync();
            commonContext.OwnedGLCode = await _ownGLCode.TextContentAsync();
            commonContext.OwnedGLCode = ExtractGLCode(commonContext.OwnedGLCode);
            commonContext.OwnedGLCode = NormalizeGLCode(commonContext.OwnedGLCode);
            _logger.Information("Created new owned folder with GL Code: {GLCode}", commonContext.OwnedGLCode);
        }
    }

    public async Task AddNewSharedFolder(CommonContext commonContext)
    {
        _logger.Debug("Adding new shared folder");
        await _ManageFolders.ClickAsync();
        bool isFolderPresent = await _SharedFolderName.IsVisibleAsync();
        
        if (isFolderPresent)
        {
            _logger.Debug("Shared folder already exists, clicking on it");
            await _SharedFolderName.ClickAsync();
            commonContext.OwnedGLCode = await _ownGLCode.TextContentAsync();
            commonContext.OwnedGLCode = ExtractGLCode(commonContext.OwnedGLCode);
            commonContext.OwnedGLCode = NormalizeGLCode(commonContext.OwnedGLCode);
            _logger.Information("Using existing shared folder with GL Code: {GLCode}", commonContext.OwnedGLCode);
        }
        else
        {
            _logger.Debug("Creating new shared folder: {FolderName}", commonContext.SharedFolderName);
            await _NewFolder.ClickAsync();
            await _FolderName.FillAsync(commonContext.SharedFolderName);
            await _SelectGLCode.ClickAsync();
            await _SaveFolder.ClickAsync();
            
            _logger.Debug("Sharing folder with other users");
            await _shareFolder.ClickAsync();
            Task.Delay(3000).Wait();
            
            _logger.Debug("Selecting company for sharing");
            var companies = await Page.Locator("#dialog-operation-copyshare #treegridCompanies tbody tr td:nth-child(2) span").AllAsync();
            foreach (var company in companies)
            {
                string companyText = await company.TextContentAsync();
                if (companyText == "Avendra Demo Independent")
                {
                    _logger.Debug("Found company: {Company}", companyText);
                    await company.ClickAsync();
                    break;
                }
            }
            
            Task.Delay(5000).Wait();
            
            _logger.Debug("Selecting user for sharing");
            var users = await Page.Locator("#dialog-operation-copyshare #gview_treegridUsers tbody tr td:nth-child(2) span").AllAsync();
            foreach (var user in users)
            {
                string userText = await user.TextContentAsync();
                if (userText == "Jason Storch - Buyer")
                {
                    _logger.Debug("Found user: {User}", userText);
                    await user.ClickAsync();
                    break;
                }
            }
            
            _logger.Debug("Applying sharing settings");
            await _ApplyButton.ClickAsync();
            //await _successToaster.WaitForAsync(new LocatorWaitForOptions { State = WaitForSelectorState.Visible });
            Task.Delay(9000).Wait();
            await _closeButton.ClickAsync();
            
            commonContext.OwnedGLCode = await _ownGLCode.TextContentAsync();
            commonContext.OwnedGLCode = ExtractGLCode(commonContext.OwnedGLCode);
            commonContext.OwnedGLCode = NormalizeGLCode(commonContext.OwnedGLCode);
            _logger.Information("Created and shared folder with GL Code: {GLCode}", commonContext.OwnedGLCode);
        }
    }

    public async Task AddProductsToOwnedFolder(CommonContext commonContext)
    {
        _logger.Debug("Adding products to owned folder");
        await _selectLineItem.ClickAsync();
        await _addProducts.ClickAsync();
        
        _logger.Debug("Selecting 'Owned Folder' from dropdown");
        await _FolderSelect.SelectOptionAsync(new SelectOptionValue { Label = "Owned Folder" });
        await _AddFavoritesBtn.ClickAsync();
        
        _logger.Debug("Navigating to owned folder");
        await _favoritesProductsLink.ClickAsync();
        await _OpenOwnedFolder.ClickAsync();
        await _summaryViewButton.ClickAsync();
        
        _logger.Debug("Filtering for item: {ItemNumber}", commonContext.ItemNumbers[0]);
        await _filterItem.TypeAsync(commonContext.ItemNumbers[0], new() { Delay = 100 });
        
        string supplierName = await _supplierName.TextContentAsync();
        commonContext.SupplierName = supplierName;
        _logger.Debug("Found supplier: {SupplierName}", supplierName);

        // Set the flag to indicate products were added to the owned folder
        commonContext.AddedProductsToOwnedFolder = true;
        _logger.Information("Products successfully added to Owned Folder - Flag set to true");
    }
    public async Task AddProductsToSharedFolder(CommonContext commonContext)
    {
        _logger.Debug("Adding products to shared folder");
        await _selectLineItem.ClickAsync();
        await _addProducts.ClickAsync();
        
        _logger.Debug("Selecting 'Shared Folder' from dropdown");
        await _FolderSelect.SelectOptionAsync(new SelectOptionValue { Label = "Shared Folder" });
        await _AddFavoritesBtn.ClickAsync();
        
        _logger.Debug("Navigating to shared folder");
        await _favoritesProductsLink.ClickAsync();
        await _OpenSharedFolder.ClickAsync();
        await _summaryViewButton.ClickAsync();
        
        _logger.Debug("Filtering for item: {ItemNumber}", commonContext.ItemNumbers[0]);
        await _filterItem.TypeAsync(commonContext.ItemNumbers[0], new() { Delay = 100 });
        
        string supplierName = await _supplierName.TextContentAsync();
        commonContext.SupplierName = supplierName;
        _logger.Debug("Found supplier: {SupplierName}", supplierName);
        
        commonContext.AddProductsToSharedFolder = true;
        _logger.Information("Products successfully added to Shared Folder - Flag set to true");
    }

    /// <summary>
    /// Verifies the GL code from favorites and optionally verifies quantity
    /// </summary>
    /// <param name="commonContext">The CommonContext containing data</param>
    /// <param name="verifyQuantity">Flag indicating whether to verify quantity (default: false)</param>
    /// <returns>Task representing the asynchronous operation</returns>

    public async Task EnterPONumber(CommonContext commonContext)
    {
        Task.Delay(5000).Wait();
        await _POTextField.FillAsync(commonContext.PoNumber); 
    }

    public async Task SelectSupplier(CommonContext commonContext)
    {
        await Supplier.ClickAsync();
        await Task.Delay(1000);
        _logger.Debug("Dropdown list is visible");
        var countryRoseSelector = Page.FrameLocator("#v4Container").Locator($"span.k-list-item:has-text('{commonContext.SupplierName}')");
        await countryRoseSelector.ClickAsync();
        _logger.Debug("Selected supplier: {SupplierName}", commonContext.SupplierName);
        // Log the supplier name we're looking for
        _logger.Debug("Starting GL code verification from favorites");
        _logger.Debug("Looking for supplier: {SupplierName}", commonContext.SupplierName);
    }
    public async Task VerifyGLCodeFromFavorites(CommonContext commonContext, bool verifyQuantity = false, bool verifyUOM = false)
    {
        try
        {
            // Wait for the Create button to be visible
            await CreateButton.WaitForAsync(new LocatorWaitForOptions
            {
                State = WaitForSelectorState.Visible,
                Timeout = 5000
            });
            _logger.Debug("Create button is visible");

            _logger.Debug("Creating new document");
            var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
            {
                await CreateButton.ClickAsync();
                Task.Delay(2000);
                if (await _confirmCreate.IsVisibleAsync())
                {
                    await _confirmCreate.EvaluateAsync("node=>node.click()");
                }
                else
                {
                    _logger.Debug("Confirm create button is not visible, skipping click");
                }
            });

            // Wait for the new page to load
            _logger.Debug("New page opened, bringing to front");
            BringSecondPageToFront(newPage, commonContext);
            await _invPopupBtn.ClickAsync();
            _logger.Debug("Closed popup on new page");

            // Verify quantity if requested
            if (verifyQuantity)
            {
                _logger.Debug("Verifying quantity as requested by 'Then Verify Quantity' step");
                await VerifyQuantity(commonContext);
            }

            //Verify UOM if requested
            if (verifyUOM)
            {
                _logger.Debug("Verifying UOM as requested by 'Then Verify UOM' step");
                await VerifyUOM(commonContext);
            }

            String ExpectedGLCode = await _glCodeDropdown.TextContentAsync();
            string TrimmedGLCode = ExtractGLCode(ExpectedGLCode);
            // Normalize the GL code by removing hyphens, periods, and extra spaces
            TrimmedGLCode = NormalizeGLCode(TrimmedGLCode);
            _logger.Debug("Expected GL Code: {GLCode}", TrimmedGLCode);

            // Debug information to understand which branch is being executed
            _logger.Debug("Debug - SpecificGL: '{SpecificGL}'", commonContext.SpecificGL ?? "null");
            _logger.Debug("Debug - OwnedGLCode: '{OwnedGLCode}'", commonContext.OwnedGLCode ?? "null");
            _logger.Debug("Debug - TrimmedGLCode: '{TrimmedGLCode}'", TrimmedGLCode);
            _logger.Debug("Debug - AddedProductsToOwnedFolder flag: {Flag}", commonContext.AddedProductsToOwnedFolder);

            // Check if products were added to the owned folder
            if (commonContext.AddedProductsToOwnedFolder)
            {
                _logger.Debug("Using OwnedGLCode for comparison because products were added to the Owned Folder");
                Assert.That(TrimmedGLCode, Is.EqualTo(commonContext.OwnedGLCode), "GL Codes do not match!");
                _logger.Information("GL Code verification successful for Owned Folder");
            }
            else if (commonContext.AddProductsToSharedFolder)
            {
                _logger.Debug("Using OwnedGLCode for comparison because products were added to the Shared Folder");
                Assert.That(TrimmedGLCode, Is.EqualTo(commonContext.OwnedGLCode), "GL Codes do not match!");
                _logger.Information("GL Code verification successful for Shared Folder");
            }
            // Otherwise, use SpecificGL if available
            else if (!string.IsNullOrWhiteSpace(commonContext.SpecificGL))
            {
                _logger.Debug("Using SpecificGL for comparison");
                Assert.That(TrimmedGLCode, Is.EqualTo(commonContext.SpecificGL), "GL Codes do not match!");
                _logger.Information("GL Code verification successful using SpecificGL");
            }
            // Fallback to OwnedGLCode if SpecificGL is not available
            else
            {
                _logger.Debug("Falling back to OwnedGLCode for comparison");
                Assert.That(TrimmedGLCode, Is.EqualTo(commonContext.OwnedGLCode), "GL Codes do not match!");
                _logger.Information("GL Code verification successful using fallback to OwnedGLCode");
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error in VerifyGLCodeFromFavorites: {ErrorMessage}", ex.Message);
            throw;
        }
    }
    private static string ExtractGLCode(string glCode)
    {
        // Split the GL code by '-' and take only the first two parts (e.g., "0001-03")
        var parts = glCode.Split('-');
        if (parts.Length >= 2)
        {
            // Combine the first two parts and trim any extra spaces
            string extractedCode = $"{parts[0]}-{parts[1]}".Trim();

            // If there is additional text after the GL code, remove it
            return extractedCode.Split(' ')[0]; // Take only the first part before any spaces
        }
        return glCode.Trim(); // If splitting fails, return the original trimmed GL code
    }
    private static string NormalizeGLCode(string glCode)
    {
        return glCode
        .Replace(".", "") // Remove periods
        .Replace(" ", "") // Remove extra spaces
        .Trim();          // Trim leading and trailing whitespace
    }

    /// <summary>
    /// Verifies that the CommonContext total quantity matches the value from the _currentDocQty locator.
    /// </summary>
    /// <param name="commonContext">The CommonContext containing the QuantityTotal</param>
    /// <returns>Task representing the asynchronous operation</returns>
    public async Task VerifyQuantity(CommonContext commonContext)
    {
        // Log the start of verification
        _logger.Debug("Starting verification of quantity against UI value");

        // Get the expected total from CommonContext
        float? expectedTotal = commonContext.QuantityTotal;

        // Check if QuantityTotal is null
        if (!expectedTotal.HasValue)
        {
            _logger.Error("QuantityTotal is not set in CommonContext. Cannot verify against UI");
            throw new InvalidOperationException("QuantityTotal is not set in CommonContext. Cannot verify against UI.");
        }

        // Get the current document quantity from the UI
        string currentDocQtyText = await _currentDocQty.InputValueAsync();

        // Parse the value to a float
        if (!float.TryParse(currentDocQtyText, out float currentDocQty))
        {
            _logger.Error("Could not parse current document quantity '{CurrentDocQty}' to a number", currentDocQtyText);
            throw new InvalidOperationException($"Could not parse current document quantity '{currentDocQtyText}' to a number.");
        }

        _logger.Debug("Current document quantity from UI: {CurrentDocQty}", currentDocQty);
        _logger.Debug("Expected total quantity from CommonContext: {ExpectedTotal}", expectedTotal);

        // Verify that the CommonContext total matches the current document quantity from the UI
        // Using a small epsilon for float comparison to handle potential floating-point precision issues
        const float epsilon = 0.0001f;
        bool isMatchWithUI = Math.Abs(expectedTotal.Value - currentDocQty) < epsilon;

        // Assert that the CommonContext total matches the current document quantity
        Assert.That(isMatchWithUI, Is.True,
            $"The expected total in CommonContext ({expectedTotal}) does not match the current document quantity in the UI ({currentDocQty}).");

        _logger.Information("UI quantity verification completed successfully");
    }
    public async Task VerifyUOM(CommonContext commonContext)
    {
        // Log the start of verification
        _logger.Debug("Starting verification of UOM against UI value");
        string UOMText = await _uomField.InputValueAsync();
        _logger.Debug("UOM value from UI: {UOMValue}", UOMText);
        Assert.That(UOMText, Is.EqualTo("EA"), "UOM value is not 'EA'");
        _logger.Information("UOM verification completed successfully");
    }
    public async Task VerifyLineItem(CommonContext commonContext)
    {
        _logger.Debug("Starting line item verification");
        
        // Get expected values from the current form
        var expectedCurrentDocQty = await _currentDocQty.InputValueAsync();
        var expectedItemNumber = await _itemNumberField.InputValueAsync();
        var expectedDescription = await _descriptionField.InputValueAsync();
        var expectedGLCode = await _glCodeDropdown.TextContentAsync();
        var expectedUOM = await _uomField.InputValueAsync();
        
        _logger.Debug("Expected values - DocQty: {DocQty}, ItemNumber: {ItemNumber}, Description: {Description}, GLCode: {GLCode}, UOM: {UOM}", 
            expectedCurrentDocQty, expectedItemNumber, expectedDescription, expectedGLCode, expectedUOM);
        
        // Change PO assignment
        _logger.Debug("Changing PO assignment");
        await _changePObtn.ClickAsync();
        await _searchPO.FillAsync(commonContext.PoNumbers[1]);
        _logger.Debug("Searching for PO: {PONumber}", commonContext.PoNumbers[1]);
        await _nextBtn.ClickAsync();
        
        _logger.Debug("Applying to selected PO");
        var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await _applyToThisPO.ClickAsync();
        });
        
        BringSecondPageToFront(newPage, commonContext);
        await _invPopupBtn.ClickAsync();
        _logger.Debug("Closed popup on new page");
        
        // Validate line items
        bool validationPassed = false;
        var frameLocator = Page.FrameLocator("#v4Container");
        var rows = await frameLocator.Locator("#invoiceLineItemGrid table tbody tr").AllAsync();
        var rowCount = rows.Count;
        
        _logger.Debug("Found {RowCount} rows to validate", rowCount);
        
        for (int i = 0; i < rowCount; i++)
        {
            var row = frameLocator.Locator("#invoiceLineItemGrid table tbody tr").Nth(i);

            // Extract column values for the current row
            var currentDocQty = await row.Locator("td").Nth(4).InnerTextAsync();
            var itemNumber = await row.Locator("td").Nth(0).InnerTextAsync();
            var description = await row.Locator("td").Nth(1).InnerTextAsync();
            var GLCode = await row.Locator("td").Nth(2).InnerTextAsync();
            var uom = await row.Locator("td").Nth(3).InnerTextAsync();

            _logger.Debug("Row {RowNumber} values - DocQty: {DocQty}, ItemNumber: {ItemNumber}, Description: {Description}, GLCode: {GLCode}, UOM: {UOM}",
                i + 1, currentDocQty, itemNumber, description, GLCode, uom);

            // Validate values
            if (currentDocQty == expectedCurrentDocQty &&
                itemNumber == expectedItemNumber &&
                description == expectedDescription &&
                GLCode == expectedGLCode &&
                uom == expectedUOM)
            {
                _logger.Information("Validation passed for row {RowNumber}", i + 1);
                validationPassed = true;
                break;
            }
        }
        
        if (!validationPassed)
        {
            _logger.Warning("No matching line items found in the grid");
        }
    }
}
