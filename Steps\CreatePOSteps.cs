using TechTalk.SpecFlow;
using TechTalk.SpecFlow.Assist;
using SpecFlowProject.Hooks;
using SpecFlowProject.Pom.Pages;
using SpecFlowProject.BusinessObjects;
using TechTalk.SpecFlow.Infrastructure;
using SpecFlowProject.Utils;
namespace SpecFlowProject.Steps
{
    [Binding]
    public class CreatePOSteps
    {
        readonly Context _context;
        readonly PoDetailsPage poDetailsPage;
        // readonly EnterReceiptsPage enterReceiptsPage;
        private ScenarioContext _scenarioContext;
        private readonly ISpecFlowOutputHelper _specFlowOutputHelper;
        private readonly LoggerService _logger;
        
        public CreatePOSteps(Context context,ScenarioContext scenarioContext,ISpecFlowOutputHelper specFlowOutputHelper)
        {
            _context = context;
            poDetailsPage = new PoDetailsPage(_context.Page!);
            _scenarioContext = scenarioContext;
            _specFlowOutputHelper = specFlowOutputHelper;
            _logger = LoggerService.Instance;
            _logger.Debug("CreatePOSteps initialized");
        }

        [Then(@"Copy PONumber")]
        public async Task CopyPoNum()
        {
            _logger.Information("Copying PO Number");
            await poDetailsPage.CopyPONumber((CommonContext)_scenarioContext["commonContext"],_specFlowOutputHelper);
            _logger.Debug("PO Number copied successfully");
        }

        [Then(@"Enter  quantity,itemnumber,description and amount. Dynamically choose GL Code from GL code dropdown.")]
        public async Task EnterQuantitiesAnAmountsPO(Table table)
        {
            _logger.Information("Entering quantities and amounts for PO with dynamic GL Code selection");
            var index=0;
            Dictionary<int,PoDetais> poDetails= new Dictionary<int, PoDetais>();
            foreach(var row in table.Rows){
                poDetails.Add(index,new PoDetais(row["quantities"],row["amounts"],row["UOM"],row["weights"]));
                _logger.Debug("Added line item {Index} with quantity: {Quantity}, amount: {Amount}", index, row["quantities"], row["amounts"]);
                index++;
            }
            _logger.Debug("Creating {Count} line items with dynamic GL Code selection", poDetails.Count);
            await poDetailsPage.CreateLineItems(poDetails,(CommonContext)_scenarioContext["commonContext"],false,_specFlowOutputHelper);
            _logger.Debug("Saving PO details");
            await poDetailsPage.SavePoDetails((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("PO details saved successfully");
        }

        [Then(@"enter taxes, shipping and Discount")]
        public async Task UpdateTaxesAndDiscounts(Table table)
        {
            _logger.Information("Updating taxes, shipping, and discount");
            TableRow tr= table.Rows[0];
            _logger.Debug("Tax: {Tax}, Shipping: {Shipping}, Discount: {Discount}", tr["tax"], tr["shipping"], tr["discount"]);
            await poDetailsPage.AddTaxDiscountShipping(new PoTaxDetails(tr["tax"],tr["shipping"], tr["discount"],tr["taxType"],tr["shippingType"],tr["discounttype"]),(CommonContext)_scenarioContext["commonContext"]);
            _logger.Debug("Taxes, shipping, and discount updated successfully");
        }

        [Then(@"Enter  quantity,itemnumber,description and amount. Dynamically choose GL Code from GL code dropdown usesame.")]
        public async Task EnterQuantitiesAnAmountsPOWithSameGL(Table table)
        {
            _logger.Information("Entering quantities and amounts for PO with same GL Code");
            var index=0;
            Dictionary<int,PoDetais> poDetails= new Dictionary<int, PoDetais>();
            foreach(var row in table.Rows){
                poDetails.Add(index,new PoDetais(row["quantities"],row["amounts"],row["UOM"],row["weights"]));
                if(row.ContainsKey("itemnumber") && row?["itemnumber"]!=null) {
                    poDetails[index].ItemNumber=row["itemnumber"];
                    _logger.Debug("Added line item {Index} with item number: {ItemNumber}", index, row["itemnumber"]);
                }
                else {
                    _logger.Debug("Added line item {Index} without item number", index);
                }
                index++;
            }
            _logger.Debug("Creating {Count} line items with same GL Code", poDetails.Count);
            await poDetailsPage.CreateLineItems(poDetails,(CommonContext)_scenarioContext["commonContext"],true,_specFlowOutputHelper);
            _logger.Debug("Saving PO details");
            await poDetailsPage.SavePoDetails((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("PO details saved successfully");
        }
        [Then(@"Validate PO GL Data")]
        public async Task ValidatePOGLData()
        {
            _logger.Information("Validating PO GL Data");
            await poDetailsPage.POdetailsGLData((CommonContext)_scenarioContext["commonContext"]);
            _logger.Debug("PO GL Data validation completed");
        }

        [Then(@"Enter  quantity,itemnumber,description and amount choosen GL Code from GL code dropdown usesame")]
        public async Task ThenEnterQuantityItemnumberDescriptionAndAmountChoosenGLCodeFromGLCodeDropdownUsesame(Table table)
        {
            _logger.Information("Entering quantities and amounts with chosen GL Code");
            var index = 0;
            Dictionary<int, PoDetais> poDetails = new Dictionary<int, PoDetais>();
            foreach (var row in table.Rows)
            {
                poDetails.Add(index, new PoDetais(row["quantities"], row["amounts"], row["UOM"], row["weights"], row["glType"]));
                _logger.Debug("Added line item {Index} with GL Type: {GLType}", index, row["glType"]);
                index++;
            }
            _logger.Debug("Creating {Count} line items with chosen GL Code", poDetails.Count);
            await poDetailsPage.CreateLineItems(poDetails, (CommonContext)_scenarioContext["commonContext"], true, _specFlowOutputHelper);
            _logger.Debug("Saving PO details");
            await poDetailsPage.SavePoDetails((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("PO details saved successfully");
        }

        [Then(@"Enter  quantity,itemnumber,description and amount specific GL Code from GL code")]
        public async Task ThenEnterQuantityItemnumberDescriptionAndAmountSpecificGLCodeFromGLCode(Table table)
        {
            _logger.Information("Entering quantities and amounts with specific GL Code");
            var index = 0;
            Dictionary<int, PoDetais> poDetails = new Dictionary<int, PoDetais>();
            
            // Get CommonContext
            CommonContext commonContext = (CommonContext)_scenarioContext["commonContext"];
            _logger.Debug("Using CommonContext to get specific GL Code");
            
            // We don't want to add a default GL code anymore
            // Instead, we'll select the first GL code available in the dropdown
            _logger.Debug("Will select first GL code from dropdown");
            
            foreach (var row in table.Rows)
            {
                poDetails.Add(index, new PoDetais(row["quantities"], row["amounts"], row["UOM"], row["weights"]));
                _logger.Debug("Added line item {Index}", index);
                index++;
            }
            
            // Pass true for the specificGL parameter to select the first GL code from dropdown
            _logger.Debug("Creating {Count} line items with specific GL Code", poDetails.Count);
            await poDetailsPage.CreateLineItems(poDetails, commonContext, false, _specFlowOutputHelper, true);
            _logger.Debug("Saving PO details");
            await poDetailsPage.SavePoDetails(commonContext);
            _logger.Information("PO details saved successfully");
        }

        [Then(@"Create Po for supplier with specific GL Code")]
        public async Task CreatePoFromSupplierWithSpecificGLCode()
        {
            _logger.Information("Creating PO for supplier with specific GL Code");
            CommonContext commonContext = (CommonContext)_scenarioContext["commonContext"];
            
            if(commonContext.Roles!=null && commonContext.Roles.Count>0 && (commonContext.Roles.FirstOrDefault(stringToCheck => stringToCheck.Contains("CanCreateOfflinePo"))==null)){
                _logger.Warning("User does not have permission to create offline PO (perm 680)");
                Assert.Fail("User does not have permission to create offline po perm 680");
            }
            
            // Check if we have a specific GL code captured
            if (string.IsNullOrEmpty(commonContext.SpecificGL))
            {
                _logger.Warning("No specific GL code found in context");
                _specFlowOutputHelper.WriteLine("Warning: No specific GL code found in context. You may need to run 'Open checkbook and filter for GL code where status is \"Required\"' first.");
            }
            else
            {
                _logger.Debug("Using specific GL code: {GLCode}", commonContext.SpecificGL);
                _specFlowOutputHelper.WriteLine($"Using specific GL code: {commonContext.SpecificGL}");
            }
            
            // Create a new instance of CreatePoPage
            _logger.Debug("Creating new instance of CreatePoPage");
            CreatePoPage createPoPage = new CreatePoPage(_context.Page!);
            
            // Open the Create PO page
            _logger.Debug("Opening Create PO page");
            await createPoPage.OpenCreatePoPage();
            
            // Create PO for the first search result supplier
            _logger.Debug("Creating PO for first search result supplier");
            await createPoPage.CreatePoForFirstSearchResultSupplier(commonContext);
            _logger.Information("PO created successfully for supplier with specific GL Code");
        }

        [Then(@"Copy PO Url")]
        public async Task CopyPOUrl(){
            _logger.Information("Copying PO URL");
            await poDetailsPage.CopyPOUrl((CommonContext)_scenarioContext["commonContext"]);
            _logger.Debug("PO URL copied successfully");
        }
        

        [Then(@"Check if po approved")]
        public async Task CheckIfPOApproved()
        {
            _logger.Information("Checking if PO is approved");
            await poDetailsPage.CheckIfPOApproved((CommonContext)_scenarioContext["commonContext"]);
            _logger.Debug("PO approval check completed");
        }

        [Then(@"Submit and send for approval")]
        public async Task SubmitAndSendForApprovalPO(){
            _logger.Information("Submitting PO and sending for approval");
            await poDetailsPage.SubmitAndLogPo((CommonContext)_scenarioContext["commonContext"],true,true);
            _logger.Debug("PO submitted");
            await poDetailsPage.AcceptPo((CommonContext)_scenarioContext["commonContext"],false);
            _logger.Information("PO sent for approval successfully");
        }
        
        [Then(@"Submit and send for approval to Jason-Stackular Manager")]
        public async Task SubmitAndSendForApprovalToJasonStackularManager(){
            _logger.Information("Submitting PO for approval to Jason-Stackular Manager");
            var commonContext = (CommonContext)_scenarioContext["commonContext"];

            // Use the dedicated method to submit a PO for approval to Jason-Stackular Manager
            _logger.Debug("Using dedicated method for Jason-Stackular Manager approval");
            await poDetailsPage.SubmitPOForJasonStackularManagerApproval(commonContext);

            // Accept the PO
            _logger.Debug("Accepting the PO");
            await poDetailsPage.AcceptPo(commonContext, false);
            _logger.Information("PO submitted for Jason-Stackular Manager approval successfully");
        }

        [Then(@"Submit and send for approval to VicePresident")]
        public async Task SubmitAndSendForApprovalToVicePresident(){
            _logger.Information("Submitting PO for approval to Vice President");
            var commonContext = (CommonContext)_scenarioContext["commonContext"];

            // Submit PO and log it
            _logger.Debug("Submitting and logging PO");
            await poDetailsPage.SubmitAndLogPo(commonContext, true, true);

            // Accept the PO for Vice President approval
            _logger.Debug("Accepting PO for Vice President approval");
            await poDetailsPage.AcceptPo(commonContext, false);
            _logger.Information("PO submitted for Vice President approval successfully");
        }

        [Then(@"Delete row and approve PO")]
        public async Task DeleteAndApprovaPO(){
            _logger.Information("Deleting row and approving PO");
            await poDetailsPage.DeleteAndApprovaPO((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Row deleted and PO approved successfully");
        }

         [Then(@"approve PO")]
        public async Task ClickApprovaPO(){
            _logger.Information("Approving PO");
            await poDetailsPage.ApprovaPO((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("PO approved successfully");
        }

        [Then("click on Edit Periods button")]
        public async Task ClickEditPeriods()
        {
            _logger.Information("Clicking on Edit Periods button");
            await poDetailsPage.ClickEditPeriods();
            _logger.Debug("Edit Periods button clicked");
        }
        [Then("Save and Submit PO")]
        public async Task SaveAndSubmit()
        {
            _logger.Information("Saving and submitting PO");
            await poDetailsPage.SavePoDetails((CommonContext)_scenarioContext["commonContext"]);
            _logger.Debug("PO details saved");
            await poDetailsPage.SubmitAndLogPo((CommonContext)_scenarioContext["commonContext"],true);
            _logger.Information("PO submitted successfully");
            // Check the approval checkbox if it's visible after SubmitAndLogPo
            //await poDetailsPage.CheckApprovalCheckboxIfVisible();
        }

        [Then("Save and Approve period PO")]
        [Then(@"Submit and Approve PO.")]
        public async Task SaveAndApprovePO()
        {
            _logger.Information("Saving and approving period PO");
            await poDetailsPage.SavePoDetails((CommonContext)_scenarioContext["commonContext"]);
            _logger.Debug("PO details saved");
            await poDetailsPage.SubmitAndLogPo((CommonContext)_scenarioContext["commonContext"],true);
            _logger.Debug("PO submitted");
            await poDetailsPage.AcceptPo((CommonContext)_scenarioContext["commonContext"],true);
            _logger.Information("PO approved successfully");
        }

        [Then(@"Copy and Approve PO.")]
        public async Task CopyPO()
        {
            _logger.Information("Copying and approving PO");
            await poDetailsPage.CopyPO((CommonContext)_scenarioContext["commonContext"],_specFlowOutputHelper);
            _logger.Information("PO copied and approved successfully");
        }

        [Then(@"Copy PO.")]
        public async Task CopyPONoApproval()
        {
            _logger.Information("Copying PO without approval");
            await poDetailsPage.CopyPO((CommonContext)_scenarioContext["commonContext"],_specFlowOutputHelper,false);
            _logger.Information("PO copied successfully without approval");
        }
        // [Then(@"Verify icon visible ""(.*)""")]
        // public async Task VerifyIcons(string iconName)
        // {
        //     await poDetailsPage.VerifyIconsVisiblity(iconName);
        // }
        [Then(@"Validate PO Data in the Action Required Page")]
        public async Task ValidatePODataInActionRequiredPage()
        {
            _logger.Information("Validating PO data in the Action Required page");
            await poDetailsPage.ValidateActionReqGLData((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("PO data validation completed in Action Required page");
        }
        [Then(@"Accept Approval PO")]
        public async Task AcceptApprovalPOs()
        {
            _logger.Information("Accepting approval PO");
            await poDetailsPage.AcceptApprovalPO();
            _logger.Information("Approval PO accepted successfully");
        }
        [Then(@"Accept Multiple POs in the Action Required grid")]
        public async Task AcceptMultiplePO()
        {
            _logger.Information("Accepting multiple POs in the Action Required grid");
            await poDetailsPage.AcceptmultiplePOs((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Multiple POs accepted successfully");
        }
        [Then(@"Validate Over Spending Limit icon")]
        public async Task ValidateOverSpendingLimitIcon()
        {
            _logger.Information("Validating Over Spending Limit icon");
            await poDetailsPage.OverSpendingLimitIcon();
            _logger.Information("Over Spending Limit icon validation completed");
        }
        [Then(@"Validate Over Budget Icon")]
        public async Task ValidateOverBudgetIcon()
        {
            _logger.Information("Validating Over Budget icon");
            await poDetailsPage.OverBudgetIconDisplay();
            _logger.Information("Over Budget icon validation completed");
        }
        [Then(@"Select Future Period")]
        public async Task SelectFuturePeriod()
        {
            _logger.Information("Selecting future period");
            await poDetailsPage.NotAValidBudgetPeriod();
            _logger.Information("Future period selected");
        }
        [Then(@"Verify Over Budget Icon in Action Required Page")]
        public async Task VerifyOverBudgetIcon()
        {
            _logger.Information("Verifying Over Budget icon in Action Required page");
            await poDetailsPage.ValidateOverBudgetIconAsync(isPermissionEnabled: true);
            _logger.Information("Over Budget icon verification completed");
        }
        [Then(@"Verify Over Spending Limit Icon in Action Required Page")]
        public async Task VerifyOverSpendingLimitIcon()
        {
            _logger.Information("Verifying Over Spending Limit icon in Action Required page");
            await poDetailsPage.ValidateOverSpendingIconAsync(isPermissionEnabled: true);
            _logger.Information("Over Spending Limit icon verification completed");
        }
        [Then(@"Verify Not in a Current Budget Period Icon in Action Required Page")]
        public async Task VerifyNotInCurrentBudgetPeriodIcon()
        {
            _logger.Information("Verifying Not in a Current Budget Period icon in Action Required page");
            await poDetailsPage.ValidateNotInCurrentPeriodIconAsync(isPermissionEnabled: true);
            _logger.Information("Not in a Current Budget Period icon verification completed");
        }
        [Then(@"Verify Not in a Valid Budget Period Icon in Action Required Page")]
        public async Task VerifyNotInValidBudgetPeriodIcon()
        {
            _logger.Information("Verifying Not in a Valid Budget Period icon in Action Required page");
            await poDetailsPage.ValidateNotInAValidPeriodIconAsync(isPermissionEnabled: true);
            _logger.Information("Not in a Valid Budget Period icon verification completed");
        }
            }
        }
