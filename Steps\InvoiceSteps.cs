using TechTalk.SpecFlow;
using SpecFlowProject.Pom.Pages;
using SpecFlowProject.Hooks;
using System.Collections;
using SpecFlowProject.BusinessObjects;
using SpecFlowProject.Utils;

namespace SpecFlowProject.Steps
{
    [Binding]
    public class InvoiceSteps
    {
        readonly Context _context;
       // readonly EnterReceiptsPage enterReceiptsPage;

        private ScenarioContext _scenarioContext;
        readonly InvoicePage invoicePage;
        private readonly LoggerService _logger;
        
        public InvoiceSteps(Context context, ScenarioContext scenarioContext)
        {
            _context = context;
            invoicePage = new InvoicePage(_context.Page!);
            _scenarioContext = scenarioContext;
            _logger = LoggerService.Instance;
            _logger.Information("InvoiceSteps initialized");
        }

        //Use an existing PO to create a new PO
        
        [Then(@"Expand Invoice Manager")]
        public async Task ExpandInvoiceManager()
        {
            _logger.Information("Expanding Invoice Manager");
            await invoicePage.ExpandInvoicePage();
            _logger.Information("Invoice Manager expanded successfully");
        }

        [Then(@"Expand Invoice Manager And Enter Invoice")]
        public async Task ExpandInvoiceManagerAndEnter()
        {
            _logger.Information("Expanding Invoice Manager and entering invoice page");
            await invoicePage.ExpandInvoicePage();
            await invoicePage.EnterInvoicePage();
            _logger.Information("Invoice Manager expanded and entered invoice page successfully");
        }

        [Then(@"Click on Enter Invoices")]
        public async Task EnterInvoicePage()
        {
            _logger.Information("Clicking on Enter Invoices");
            await invoicePage.EnterInvoicePage();
            _logger.Information("Clicked on Enter Invoices successfully");
        }

        [Then(@"Search invoice and update periods")]
        public async Task UpdateInvoice()
        {
            _logger.Information("Searching invoice and updating periods");
            await invoicePage.SearchInvoice((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Invoice searched and periods updated successfully");
        }

        [Then(@"Enter Document Number")]
        public async Task EnterInvoiceNumber()
        {
            _logger.Information("Entering document number");
            await invoicePage.EnterDocumentNumber();
            _logger.Information("Document number entered successfully");
        }

        [Then(@"Enter Document Number with next")]
        public async Task EnterInvoiceNumberwithNext()
        {
            _logger.Information("Entering document number with next");
            await invoicePage.EnterDocumentNumberWithNext();
            _logger.Information("Document number with next entered successfully");
        }

        [Then(@"Verify Enter Invoice Table columns")]
        public async Task VerifyEnterInvoicesGrid(Table table)
        {
            _logger.Information("Verifying Enter Invoice Table columns");
            await invoicePage.ClickFirstNextButton();
            foreach(TableRow row in table.Rows){
               string column = row["columns"];
               _logger.Debug("Verifying column: {Column}", column);
               await invoicePage.VerifyEnterInvocieTableGridColumns(column);
            }
            _logger.Information("Enter Invoice Table columns verified successfully");
        }

        [Then(@"Verify PO number in the Enter invoice PO grid")]
        public async Task VerifyPONumber()
        {
            _logger.Information("Verifying PO number in the Enter invoice PO grid");
            await invoicePage.VerifyPOInGrid((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("PO number in the Enter invoice PO grid verified successfully");
        }

        [Then(@"Verify PO Table grid columns titles")]
        public async Task VerifyPOGridColumns(Table table)
        {
            _logger.Information("Verifying PO Table grid columns titles");
            foreach(TableRow row in table.Rows){
               string column = row["columns"];
               _logger.Debug("Verifying column title: {Column}", column);
               await invoicePage.POGridColumnsTitles(column);
            }
            _logger.Information("PO Table grid columns titles verified successfully");
        }

        [Then(@"Create invoice for PO created and save it")]
        [ScenarioRetry(3)]
        public async Task VerifyInvoiceCreatedWithDocumentNumber(Table table)
        {
            _logger.Information("Creating invoice for PO created and saving it");
            var quantities = new List<string>();
            foreach(var row in table.Rows){
               var column = row["quantities"];
               quantities.Add(column);
               _logger.Debug("Added quantity: {Quantity}", column);
            }
            
            await invoicePage.CreateFirstInvoice(quantities,(CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Invoice for PO created and saved successfully");
        }

        [Then(@"create a invoice without PO (.*)")]
        public async Task CreateInvoiceWithoutPO(string company)
        {
            _logger.Information("Creating invoice without PO for company: {Company}", company);
            await invoicePage.CreateNewInvoiceWithoutPO(company);
            _logger.Information("Invoice without PO created successfully");
        }

        [Then(@"create a new supplier without PO")]
        public async Task CreatSupplier()
        {
            _logger.Information("Creating a new supplier without PO");
            await invoicePage.CreateNewSupplier();
            _logger.Information("New supplier without PO created successfully");
        }

        [Then(@"create a PO by click on CreatePO in Enter invoice PO grid")]
        public async Task CreatePOInEnterInvoices()
        {
            _logger.Information("Creating a PO by clicking on CreatePO in Enter invoice PO grid");
            await invoicePage.CreatePOFromEnterInvoice();
            _logger.Information("PO created successfully from Enter invoice PO grid");
        }

        [Then(@"Complete Invoice")]
        public async Task CompleteInvoice()
        {
            _logger.Information("Completing invoice");
            bool isThreeWay = await invoicePage.CheckForLinked();
            _logger.Debug("Is three-way match: {IsThreeWay}", isThreeWay);
            
            if(isThreeWay){
                _logger.Information("Three-way match detected, completing receipt");
                await CompleteReceipt();
                await invoicePage.LinkToSelected((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Receipt completed and linked");
            }
            
            await invoicePage.CompleteInvoice((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Invoice completed successfully");
        }
        [Then(@"Open First Div")]
        public async Task OpenFirstDiv()
        {
            _logger.Information("Opening first div");
            await invoicePage.OpenFirstDiv();
            _logger.Information("First div opened successfully");
        }

        [Then(@"Create another invoice with desired quantities and complete it")]
        public async Task CreateAnotherInvoice(Table table)
        {
            _logger.Information("Creating another invoice with desired quantities and completing it");
            List<string> quantities = new List<string>();
            foreach(TableRow row in table.Rows){
               string column = row["quantities"];
               quantities.Add(column);
               _logger.Debug("Added quantity: {Quantity}", column);
            }
            await invoicePage.CreateAnotherInvoice(quantities,1,(CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Another invoice created and completed successfully");
        }

        [Then(@"Create a credit memo with desired quantities and complete it")]
        
        public async Task CreateCredit(Table table)
        {
            _logger.Information("Creating a credit memo with desired quantities and completing it");
            bool complete = true;
            List<string> quantities = new List<string>();
            foreach(TableRow row in table.Rows){
               string column = row["quantities"];
               quantities.Add(column);
               _logger.Debug("Added quantity: {Quantity}", column);
            }
            await invoicePage.CreateAnotherInvoice(quantities,2,(CommonContext)_scenarioContext["commonContext"],complete);
            _logger.Information("Credit memo created and completed successfully");
        }

        [Then(@"Create a credit memo with desired quantities")]
        public async Task CreateCreditNoComplete(Table table){
            _logger.Information("Creating a credit memo with desired quantities without completing");
            bool complete = false;
            List<string> quantities = new List<string>();
            foreach(TableRow row in table.Rows){
               string column = row["quantities"];
               quantities.Add(column);
               _logger.Debug("Added quantity: {Quantity}", column);
            }
            await invoicePage.CreateAnotherInvoice(quantities,2,(CommonContext)_scenarioContext["commonContext"],complete);
            _logger.Information("Credit memo created without completing successfully");
        }
        
        [Then(@"update mpa adding new row")]
        public async Task UpdateInvoiceMPA(){
            _logger.Information("Updating MPA by adding new row");
            await invoicePage.UpdateInvoiceMPA((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("MPA updated with new row successfully");
        }

        [Then(@"Complete New Invoice")]
        public async Task CompleteNewInvoice(){
            _logger.Information("Completing new invoice");
            bool isThreeWay = await invoicePage.CheckForLinked();
            _logger.Debug("Is three-way match: {IsThreeWay}", isThreeWay);
            
            if(isThreeWay){
                _logger.Information("Three-way match detected, completing receipt");
                await CompleteReceipt();
                await invoicePage.LinkToSelected((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Receipt completed and linked");
            }
            
            await invoicePage.CompleteNewInvoice((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("New invoice completed successfully");
        }
        [Then(@"Close PO")]
        public async Task ClosePO()
        {
            _logger.Information("Closing PO");
            await invoicePage.ClosePO();
            _logger.Information("PO closed successfully");
        }

        [Then(@"Complete the invoice")]
        public async Task ClickCompleteAction()
        {
            _logger.Information("Completing the invoice");
            bool isThreeWay = await invoicePage.CheckForLinked();
            _logger.Debug("Is three-way match: {IsThreeWay}", isThreeWay);
            
            if(isThreeWay){
                _logger.Information("Three-way match detected, completing receipt");
                await CompleteReceipt();
                await invoicePage.LinkToSelected((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Receipt completed and linked");
            }
            
            await invoicePage.CompleteInvoice((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Invoice completed successfully");
        }

        [Then(@"Verify Invoice Status (.*)")]
        public async Task VerifyStatus(string status)
        {
            _logger.Information("Verifying invoice status: {Status}", status);
            await invoicePage.VerifyInvoiceStatus(status);
            _logger.Information("Invoice status verified successfully");
        }

        [Then(@"Delete invoice")]
        public async Task DeleteAction()
        {
            _logger.Information("Deleting invoice");
            await invoicePage.DeleteButton();
            _logger.Information("Invoice deleted successfully");
        }


        [Then(@"Verify Undelete Action")]
        public async Task UndeleteAction()
        {
            _logger.Information("Verifying undelete action");
            await invoicePage.UndeleteButton();
            _logger.Information("Undelete action verified successfully");
        }

        
        [Then(@"Close PO Action")]
        public async Task CloseInvoiceAction()
        {
            _logger.Information("Executing Close PO action");
            await invoicePage.ClosePO();
            _logger.Information("Close PO action executed successfully");
        }

        [Then(@"Click on Submit for Approval Action")]
        public async Task ClickSubmitForApproval()
        {
            _logger.Information("Clicking on Submit for Approval action");
            bool isThreeWay = await invoicePage.CheckForLinked();
            _logger.Debug("Is three-way match: {IsThreeWay}", isThreeWay);
            
            if(isThreeWay){
                _logger.Information("Three-way match detected, completing receipt");
                await CompleteReceipt();
                await invoicePage.LinkToSelected((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Receipt completed and linked");
            }
            
            await invoicePage.SubmitForApproval((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Submit for Approval action completed successfully");
        }


        [Then(@"Click on Recall button")]
        public async Task ClickRecallButton()
        {
            _logger.Information("Clicking on Recall button");
            await invoicePage.Recall();
            _logger.Information("Recall button clicked successfully");
        }

        

        [Then(@"Verify Document Number in the Action Required Page")]
        public async Task VerifyDocumentInActionRequired()
        {
            _logger.Information("Verifying document number in the Action Required page");
            await invoicePage.ActionRequiredDocument((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Document number in Action Required page verified successfully");
        }

        [Then(@"Cancel Invoice")]
        public async Task CancelInvoiceAction()
        {
            _logger.Information("Cancelling invoice");
            await invoicePage.CancelInvoice();
            _logger.Information("Invoice cancelled successfully");
        }


        [Then(@"Cancel PO")]
        public async Task CancelPOAction()
        {
            _logger.Information("Cancelling PO");
            await invoicePage.CancelPO();
            _logger.Information("PO cancelled successfully");
        }


        [Then(@"Click on ChangePO Assignment button")]
        public async Task ClickChangePO()
        {
            _logger.Information("Clicking on ChangePO Assignment button");
            await invoicePage.ChangePOAssignment((CommonContext)_scenarioContext["commonContext"],"fromPo");
            _logger.Information("ChangePO Assignment button clicked successfully");
        }

        [Then(@"Click on Maintain Multiple Invoice Periods")]
        public async Task ClickChangeMultipleInvoicePeriods()
        {
            _logger.Information("Clicking on Maintain Multiple Invoice Periods");
            await invoicePage.ChangePOAssignment((CommonContext)_scenarioContext["commonContext"],"fromInvoice");
            _logger.Information("Maintain Multiple Invoice Periods clicked successfully");
        }

        [Then(@"Click on Single Period button")]
        public async Task ClickChangeSinglePeriod()
        {
            _logger.Information("Clicking on Single Period button");
            await invoicePage.ChangePOAssignment((CommonContext)_scenarioContext["commonContext"],"Single");
            _logger.Information("Single Period button clicked successfully");
        }

        [Then(@"Verify Add Line items table grid fields")]
        public async Task VerifyLineItemsTableGridFields(Table table)
        {
            _logger.Information("Verifying Add Line items table grid fields");
            foreach(TableRow row in table.Rows)
            {
               string column = row["columns"];
               _logger.Debug("Verifying column: {Column}", column);
               await invoicePage.VerifyLineItemsTableGrid(column);
            }
            _logger.Information("Add Line items table grid fields verified successfully");
        }

        [Then(@"Verify Delete button should not be visible in the GL Summary grid")]
        public async Task VerifyDeleteBtnNotVisible()
        {
            _logger.Information("Verifying Delete button should not be visible in the GL Summary grid");
            await invoicePage.VerifyDeleteButtonInGLSummary();
            _logger.Information("Verified Delete button is not visible in the GL Summary grid");
        }

        [Then(@"Verify Delete button is visible for the line items present in the table grid")]
        public async Task VerifyDeleteBtnVisible()
        {
            _logger.Information("Verifying Delete button is visible for the line items present in the table grid");
            await invoicePage.VerifyDeleteBtn();
            _logger.Information("Verified Delete button is visible for the line items");
        }

        [Then(@"Adding line items and update item details")]
        public async Task AddlingLineItems()
        {
            _logger.Information("Adding line items and updating item details");
            await invoicePage.AddLineItems();
            _logger.Information("Line items added and item details updated successfully");
        }

        [Then(@"Verify setzeroqty functionality")]
        public async Task VerifyQtyToSetZero()
        {
            _logger.Information("Verifying setzeroqty functionality");
            await invoicePage.SetZeroToQty();
            _logger.Information("setzeroqty functionality verified successfully");
        }

        [Then(@"Verify BulkAssignGLCode functionality")]
        public async Task VerifyBulkAssign()
        {
            _logger.Information("Verifying BulkAssignGLCode functionality");
            await invoicePage.BulkAssignGL();
            _logger.Information("BulkAssignGLCode functionality verified successfully");
        }

        [Then(@"Verify combining line items having 0 qty functionality")]
            public async Task VerifyCombineLineItemsQtyIsZero()
            {
                _logger.Information("Verifying combining line items having 0 qty functionality");
                await invoicePage.CombineLineItemsForQtyZero((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Combining line items having 0 qty functionality verified successfully");
            }

            [Then(@"Verify combining line items having one and Zero qty functionality")]
            public async Task VerifyCombineLineItemsForOneAndZeroQty()
            {
                _logger.Information("Verifying combining line items having one and Zero qty functionality");
                await invoicePage.CombineLineItemsQtyOneAndZero();
                _logger.Information("Combining line items having one and Zero qty functionality verified successfully");
            }

            [Then(@"Verify combining line items having one and one qty functionality")]
            public async Task VerifyCombineLineItemsForOneAndOneQty()
            {
                _logger.Information("Verifying combining line items having one and one qty functionality");
                await invoicePage.CombineLineItemsQtyOneAndOne();
                _logger.Information("Combining line items having one and one qty functionality verified successfully");
            }

            [Then(@"Verify Split By Percentage Line Items")]
            public async Task VerifySplitByPercentage()
            {
                _logger.Information("Verifying Split By Percentage Line Items");
                await invoicePage.SplitByPercentage();
                _logger.Information("Split By Percentage Line Items verified successfully");
            }

            [Then(@"Verify Split By Quantity Line Items")]
            public async Task VerifySplitByQuantity()
            {
                _logger.Information("Verifying Split By Quantity Line Items");
                await invoicePage.SplitByQuantity();
                _logger.Information("Split By Quantity Line Items verified successfully");
            }

            [Then(@"Verify GL Distribution Summary Table Grid columns")]
            public async Task VerifyGLTableGrid(Table table)
            {
                _logger.Information("Verifying GL Distribution Summary Table Grid columns");
                foreach(TableRow row in table.Rows)
                {
                   string column = row["columns"];
                   _logger.Debug("Verifying column: {Column}", column);
                   await invoicePage.VerifyGLDistributionGridColumns(column);
                }
                _logger.Information("GL Distribution Summary Table Grid columns verified successfully");
            }

            [Then(@"Verify GL Distribution Summary")]
            public async Task VerifyGLSummary()
            {
                _logger.Information("Verifying GL Distribution Summary");
                await invoicePage.GLDistribution((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("GL Distribution Summary verified successfully");
            }

             [Then(@"Verify Shipping Value")]
            public async Task VerifyGLShippingValue()
            {
                _logger.Information("Verifying Shipping Value");
                await invoicePage.VerifyShippingValue((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Shipping Value verified successfully");
            }

            [Then(@"Verify Discount Value")]
            public async Task VerifyGLDiscountValue()
            {
                _logger.Information("Verifying Discount Value");
                await invoicePage.VerifyDiscountValue((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Discount Value verified successfully");
            }

            [Then(@"Verify Taxes Value")]
            public async Task VerifyGLTaxesValue()
            {
                _logger.Information("Verifying Taxes Value");
                await invoicePage.VerifyTaxesValue((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Taxes Value verified successfully");
            }

            [Then(@"Verify Grand Total Value")]
            public async Task VerifyGLGrandTotalValue()
            {
                _logger.Information("Verifying Grand Total Value");
                await invoicePage.VerifyGrandTotalValue();
                _logger.Information("Grand Total Value verified successfully");
            }

            [Then(@"update taxes and save it")]
            public async Task UpdateTaxesAndDiscounts(Table table)
            {
                _logger.Information("Updating taxes and saving it");
                TableRow tr= table.Rows[0];
                await invoicePage.UpdateTaxes(new List<PoTaxDetails>{new PoTaxDetails(tr["tax"],tr["shipping"], tr["discount"], "","","")},(CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Taxes updated and saved successfully");
            }

            [Then(@"Complete and Close PO")]
            public async Task CompleteAndClosePO(){
                _logger.Information("Completing and Closing PO");
                bool isThreeWay = await invoicePage.CheckForLinked();
                if(isThreeWay){
                    _logger.Debug("Three-way match detected, completing receipt and linking");
                    await CompleteReceipt();
                    await invoicePage.LinkToSelected((CommonContext)_scenarioContext["commonContext"]);
                }
                await invoicePage.CompleteInvoice((CommonContext)_scenarioContext["commonContext"]);
                await invoicePage.ClosePO();
                _logger.Information("PO completed and closed successfully");
            }

            [Then(@"Create credit memo for the invoice")]
            public async Task CreateCreditMemo()
            {
                _logger.Information("Creating credit memo for the invoice");
                await invoicePage.VerifyCreditMemo((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Credit memo created successfully");
            }

            [Then(@"Print Invoice")]
            public async Task PrintInvoice()
            {
                _logger.Information("Printing Invoice");
                await invoicePage.VerifyPrintInvoice();
                _logger.Information("Invoice printed successfully");
            }

            [Then(@"Verify Invoice Related Documents table grid")]
            public async Task VerifyTableGridColumns(Table table)
            {
                _logger.Information("Verifying Invoice Related Documents table grid");
                foreach(TableRow row in table.Rows)
                {
                   string column = row["columns"];
                   _logger.Debug("Verifying column: {Column}", column);
                   await invoicePage.VerifyInvoiceRelatedDocsGridColumns(column);
                }
                _logger.Information("Invoice Related Documents table grid verified successfully");
            }

            [Then(@"Verify current invoice is showing in the Invoice related documents grid")]
            public async Task VerifyCurrentInvoice()
            {
                _logger.Information("Verifying current invoice is showing in the Invoice related documents grid");
                await invoicePage.VerifyCurrentInvoiceNumberIsShowingInRelatedDocGrid();
                _logger.Information("Current invoice verification completed successfully");
            }
            private async Task CompleteReceipt(){
                _logger.Information("Starting to complete receipt");
                ReceiptPage rp = new ReceiptPage(await invoicePage.CreateNewReceipt((CommonContext)_scenarioContext["commonContext"]));
                string rcNo = await rp.CreateReceipt(new List<string>());
                rcNo = rcNo.Trim();
                _logger.Debug("Receipt created with number: {ReceiptNumber}", rcNo);
                await rp.CompleteReceipt();
                _logger.Debug("Receipt completed, reopening page");
                await invoicePage.ReopenPage((CommonContext)_scenarioContext["commonContext"],rcNo);
                _logger.Information("Receipt completion process finished");
            }
            [Then(@"create a new receipt and verify the new receipt number showing in the Invoice Related Documnets table grid")]
            public async Task VerifyCreatedReceipt()
            {
                _logger.Information("Creating a new receipt and verifying it in the Invoice Related Documents grid");
                //Console.WriteLine(receiptPage);
                await CompleteReceipt();
                _logger.Information("Receipt creation and verification completed");
            }

            [Then(@"create a new invoice and verify the new invoice number showing in the Invoice Related Documnets table grid")]
            public async Task VerifyCreatedInvoice()
            {
                _logger.Information("Creating a new invoice and verifying it in the Invoice Related Documents grid");
                await invoicePage.CreateNewInvoice((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Invoice creation and verification completed");
            }

            [Then(@"Verify Link to Receipt function")]
            public async Task VerifyLinkToReceipt()
            {
                _logger.Information("Verifying Link to Receipt function");
                await invoicePage.LinkToSelected((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Link to Receipt function verified successfully");
            }

            [Then(@"Verify Unlink button function")]
            public async Task VerifyUnlink()
            {
                _logger.Information("Verifying Unlink button function");
                await invoicePage.UnlinkReceipt((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Unlink button function verified successfully");
            }

            [Then(@"Verify Cancel the selected documents")]
            public async Task VerifyCancelSelectedDocuments()
            {
                _logger.Information("Verifying Cancel the selected documents function");
                await invoicePage.CancelSelectedDoc();
                _logger.Information("Cancel selected documents function verified successfully");
            }

            [Then(@"Verify Receipt button should not visible")]
            public async Task VerifyReceiptButtonNotVisible()
            {
                _logger.Information("Verifying Receipt button should not be visible");
                await invoicePage.CreateNewReceiptBtn();
                _logger.Information("Receipt button visibility verification completed");
            }

            [Then(@"Open ViewInvoices")]
            public async Task OpenViewInvoices()
            {
                _logger.Information("Opening View Invoices");
                await invoicePage.EnterViewInvoices();
                _logger.Information("View Invoices opened successfully");
            }

            [Then(@"Verify View Invoices table grid columns")]
            public async Task VerifyViewInvoicesTableGridColumns(Table table)
            {
                _logger.Information("Verifying View Invoices table grid columns");
                foreach(TableRow row in table.Rows){
                   string column = row["columns"];
                   _logger.Debug("Verifying column: {Column}", column);
                   await invoicePage.VerifyEnterInvocieTableGridColumns(column);
                }
                _logger.Information("View Invoices table grid columns verified successfully");
            }

            [Then(@"create bulkinvoices")]
            public async Task VerifyCreateBulkInvoices()
            {
                _logger.Information("Creating bulk invoices");
                await invoicePage.CreateBulkInvoices((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Bulk invoices created successfully");
            }

            [Then(@"Delete the bulk invoices")]
            public async Task VerifyDeleteBulkInvoices()
            {
                _logger.Information("Deleting bulk invoices");
                await invoicePage.BulkDelete((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Bulk invoices deleted successfully");
            }

            [Then(@"Verify bulk delete status")]
            public async Task VerifyBulkDeleteActionStatus()
            {
                _logger.Information("Verifying bulk delete status");
                await invoicePage.VerifyBulkDelStatus((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Bulk delete status verified successfully");
            }

            [Then(@"Undelete the bulk invoices")]
            public async Task VerifyUndeleteBulkInvoices()
            {
                _logger.Information("Undeleting bulk invoices");
                await invoicePage.BulkUndelete((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Bulk invoices undeleted successfully");
            }

            [Then(@"Verify bulk undelete status")]
            public async Task VerifyBulkUndelActionStatus()
            {
                _logger.Information("Verifying bulk undelete status");
                await invoicePage.VerifyBulkUndelStatus((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Bulk undelete status verified successfully");
            }

            [Then(@"Complete the bulk invoices")]
            public async Task VerifyCompleteBulkInvoices()
            {
                _logger.Information("Completing bulk invoices");
                await invoicePage.BulkCompleteDocument((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Bulk invoices completed successfully");
            }

            [Then(@"Verify bulk complete action status")]
            public async Task VerifyBulkCompleteActionStatus()
            {
                _logger.Information("Verifying bulk complete action status");
                await invoicePage.VerifyBulkCompleteStatus((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Bulk complete action status verified successfully");
            }

            [Then(@"Bulk Close PO")]
            public async Task VerifyBulkClosePO()
            {
                _logger.Information("Closing PO in bulk");
                await invoicePage.BulkClosePO((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Bulk PO closing completed");
            }

            [Then(@"Verify Bulk Close PO status")]
            public async Task VerifyBulkClosePOStatus()
            {
                _logger.Information("Verifying Bulk Close PO status");
                await invoicePage.VerifyBulkClosePOStatus((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Bulk Close PO status verified successfully");
            }

            [Then(@"convert to single period and update")]
            public async Task ConvertToSinglePeriodNUpdate()
            {
                _logger.Information("Converting to single period and updating");
                await invoicePage.ConvertToSinglePeriodNUpdate((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Conversion to single period and update completed successfully");
            }

            [Then(@"update period in invoice")]
            public async Task UpdatePeriodInvoice()
            {
                _logger.Information("Updating period in invoice");
                await invoicePage.UpdatePeriodInvoice((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Period in invoice updated successfully");
            }

            [Then(@"convert to single period")]
            public async Task ConvertToSinglePeriod()
            {
                _logger.Information("Converting to single period");
                await invoicePage.ConvertToSinglePeriodNUpdate((CommonContext)_scenarioContext["commonContext"],false);
                _logger.Information("Conversion to single period completed successfully");
            }

            [Then(@"convert to multi period")]
            public async Task ConvertToMultiPeriod()
            {
                _logger.Information("Converting to multi period");
                await invoicePage.ConvertToMultiPeriod((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Conversion to multi period completed successfully");
            }

            [Then(@"Update single period in the invoice")]
            public async Task UpdateSinglePeriodInvoice()
            {
                _logger.Information("Updating single period in the invoice");
                await invoicePage.UpdatePeriodInvoice((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Single period in the invoice updated successfully");
            }

            [Then(@"Create invoice for PO created with new GL code")]
            [ScenarioRetry(3)] // Adding retry mechanism with 3 attempts
            public async Task InvoiceWithGLChange(Table table)
            {
                _logger.Information("Creating invoice for PO with new GL code");
                var quantities = new List<string>();
                foreach(var row in table.Rows){
                   var column = row["quantities"];
                   quantities.Add(column);
                   _logger.Debug("Added quantity: {Quantity}", column);
                }
            
                await invoicePage.CreateFirstInvoice(quantities,(CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Invoice for PO with new GL code created successfully");
            }

            [Then(@"Add new row")]
             public async Task AddNewRow(Table table)
            {
                _logger.Information("Adding new row");
                var poDetails= new List<PoDetais>();
                foreach(var row in table.Rows){
                    var detail = new PoDetais(row["quantities"],row["amounts"],row["UOM"],row["weights"]);
                    poDetails.Add(detail);
                    _logger.Debug("Added row with quantity: {Quantity}, amount: {Amount}", row["quantities"], row["amounts"]);
                }
                await invoicePage.AddNewRow(poDetails,(CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("New row added successfully");
            }
        }
}
