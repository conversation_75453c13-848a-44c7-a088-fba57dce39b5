using Microsoft.Playwright;
using SpecFlowProject.BusinessObjects;
using System.Globalization;
using SpecFlowProject.Utils;
using System.Text.RegularExpressions;
using System.Data;

namespace SpecFlowProject.Pom.Pages;

public class DepartmentsPage: Base
{
    private ILocator _departmentTableFirstChild => Page.FrameLocator("#v4Container").Locator("#viewDepartmentTable tbody tr:first-child");
    private ILocator _departmentTableFirstChildRead => _departmentTableFirstChild.Locator("td:nth-child(2)");
    private ILocator _departmentTableFirstChildInput => _departmentTableFirstChild.Locator("td:nth-child(2) input");
    private ILocator _departmentTableGlCodes => _departmentTableFirstChild.Locator("td:nth-child(3) span");
    private ILocator _departmentTableMembers => _departmentTableFirstChild.Locator("td:nth-child(4) span");
    private ILocator _departmentTableFirstChildedit => _departmentTableFirstChild.Locator("td:nth-child(5) button");
    private ILocator _departmentTableFirstChildcheckbox => _departmentTableFirstChild.Locator("td:nth-child(1) input");

    private ILocator _viewDept => Page.FrameLocator("#v4Container").Locator("#viewDepartmentTable");
    private ILocator _addNewDepartment => Page.FrameLocator("#v4Container").Locator("button:has-text('Add New Department')");
    private ILocator _deleteDepartment => Page.FrameLocator("#v4Container").Locator("button:has-text('Delete')");
    private ILocator _saveDepartment => Page.FrameLocator("#v4Container").Locator("button:has-text('Save')");
    private ILocator _cancelDepartment => Page.FrameLocator("#v4Container").Locator("button:has-text('Cancel')");
    private ILocator _clickFilter => Page.FrameLocator("#v4Container").Locator("#viewDepartmentTable .k-grid-header table thead tr th:nth-child(2) .k-grid-header-menu");
    private ILocator glCodesassign => Page.FrameLocator("#v4Container").Locator("div > div:last-child > div:nth-child(3) > div:first-child");
    private ILocator departmentMemberassign => Page.FrameLocator("#v4Container").Locator("div > div:last-child > div:nth-child(3) > div:last-child");
    private ILocator glCodeFilterinput => glCodesassign.Locator("> div:first-child input");

    private ILocator glCodeFilterDropdown => glCodesassign.Locator("#view-drop-down-list");
    private ILocator glCodeFilterGrid => glCodesassign.Locator("> div:last-child");
   
    private ILocator glCodeFilterGridInp => glCodeFilterGrid.Locator("Input").Nth(0);

    private ILocator glCodeUnassigned => glCodeFilterGrid.Locator("div:nth-child(1)");
    private ILocator glCodeUnassignedselectAll => glCodeUnassigned.Locator(">div input");
    private ILocator glCodeUnassignedinputs => glCodeUnassigned.Locator(">ul li input");
    private ILocator glCodeActions => glCodeFilterGrid.Locator("> div:nth-child(2)");
    private ILocator glCodebutton1 => glCodeActions.Locator("button").Nth(0);
    private ILocator glCodebutton2 => glCodeActions.Locator("button").Nth(1);
    private ILocator glCodebutton3 => glCodeActions.Locator("button").Nth(2);
    private ILocator glCodebutton4 => glCodeActions.Locator("button").Nth(3);
    private ILocator glCodeassigned => glCodeFilterGrid.Locator("div:nth-child(3)");
    private ILocator glCodeassignedselectAll => glCodeassigned.Locator(">div input");
    private ILocator glCodeassignedspan => glCodeassigned.Locator(">div span");
    private ILocator glCodeassignedinputs => glCodeassigned.Locator(">ul li input");
    private ILocator departmentMemberassignInput => departmentMemberassign.Locator(">div:first-child input");

    private ILocator departmentMemberassignSelect => departmentMemberassign.Locator("#view-drop-down-list");

    private ILocator departmentMemberassignGrid => departmentMemberassign.Locator(">div:last-child");

    private ILocator departmentMemberassignGridInp => departmentMemberassignGrid.Locator("Input").Nth(0);
    private ILocator departmentMemberUnassigned => departmentMemberassignGrid.Locator("div:nth-child(1)");
    private ILocator departmentMemberUnassignedselectAll => departmentMemberUnassigned.Locator(">div input");
    private ILocator departmentMemberUnassignedinputs => departmentMemberUnassigned.Locator(">ul li input");
    private ILocator departmentMemberActions => departmentMemberassignGrid.Locator("> div:nth-child(2)");
    private ILocator departmentMemberbutton1 => departmentMemberActions.Locator("button").Nth(0);
    private ILocator departmentMemberbutton2 => departmentMemberActions.Locator("button").Nth(1);
    private ILocator departmentMemberbutton3 => departmentMemberActions.Locator("button").Nth(2);
    private ILocator departmentMemberbutton4 => departmentMemberActions.Locator("button").Nth(3);
    private ILocator departmentMemberassigned => departmentMemberassignGrid.Locator("div:nth-child(3)");
    private ILocator departmentMemberassignedselectAll => departmentMemberassigned.Locator(">div input");
    private ILocator departmentMemberassignedspan => departmentMemberassigned.Locator(">div span");
    private ILocator departmentMemberassignedinputs => departmentMemberassigned.Locator(">ul li input");
    private ILocator okDiscard => Page.FrameLocator("#v4Container").Locator("button:has-text('OK, Discard Changes')");
    private ILocator ok => Page.FrameLocator("#v4Container").Locator("button:has-text('OK')");
    private ILocator nav => Page.FrameLocator("#v4Container").Locator("nav").Filter(new() { HasText = "View/Edit Departments" });
    private ILocator _activeDropdownUL => Page.FrameLocator("#v4Container").Locator(".k-list ul");
    private ILocator _activeDropdownLoopClick => Page.FrameLocator("#v4Container").Locator(".k-list ul li");
    private ILocator _selectDepartment => Page.FrameLocator("#v4Container").Locator(".k-grid-table tr:nth-child(1) td:nth-child(1) input");
    private ILocator _copyButton => Page.FrameLocator("#v4Container").Locator("#viewDepartmentTable button:has-text('Copy')");
    private ILocator _copyDepartmentpopup => Page.FrameLocator("#v4Container").Locator(".k-window-content.k-dialog-content div:nth-child(1) input");
    private ILocator _copyButtonpopup => Page.FrameLocator("#v4Container").Locator(".flex.flex-row.items-center.justify-end.gap-2 div:nth-child(1) button");
    private ILocator _successMessage => Page.FrameLocator("#v4Container").Locator(".Toastify__toast-body");

    private ILocator _checkesCopy => Page.FrameLocator("#v4Container").Locator(".k-window-content.k-dialog-content > ul > *");

    private ILocator glCodeTextRow1 => Page.FrameLocator("#v4Container").Locator(".k-grid-table tr:nth-child(1) td:nth-child(3) > div > span");

    private ILocator memberTextRow1 => Page.FrameLocator("#v4Container").Locator(".k-grid-table tr:nth-child(1) td:nth-child(4) > div > span");
    private ILocator _bulkMemberUpdate => Page.FrameLocator("#v4Container").Locator("button:has-text('Bulk Member Update')");
    private ILocator _selectbulkUpdateMember => Page.FrameLocator("#v4Container").Locator("#assignMemberTable tr:nth-child(2) input");
    private ILocator _assignSelectedMembers => Page.FrameLocator("#v4Container").Locator("button:has-text('Assign Selected Members')");
    private ILocator _departmentMemberSelected => Page.FrameLocator("#v4Container").Locator(".k-grid-table tr:nth-child(2) td:nth-child(2)");
    private ILocator _bulkGLCodeUpdate => Page.FrameLocator("#v4Container").Locator("button:has-text('Bulk GL Code Update')");
    private ILocator _selectbulkUpdateGlCode => Page.FrameLocator("#v4Container").Locator("#assignGLCodeTable tr:nth-child(2) input");
    private ILocator _assignSelectedGLCode => Page.FrameLocator("#v4Container").Locator("button:has-text('Assign Selected GL Codes')");
    
    private ILocator glCodeUnassignedTable => glCodeUnassigned.Locator(">ul li");

    private ILocator departmentMemberUnassignedTable => departmentMemberUnassigned.Locator(">ul li");

    private ILocator multiSelectClick => Page.FrameLocator("#v4Container").Locator(".k-multiselect input");

    private ILocator multiSelectDropdwonItems => Page.FrameLocator("#v4Container").Locator(".k-animation-container .k-popup ul > li ");

    private ILocator _assignedTableTd => Page.FrameLocator("#v4Container").Locator("#assignMemberTable table >tbody > tr > td:nth-child(2) > span");

    private ILocator _assignedGLTableTd => Page.FrameLocator("#v4Container").Locator("#assignGLCodeTable table >tbody > tr > td:nth-child(2) > span");

    private ILocator _assignedGLTableTH => Page.FrameLocator("#v4Container").Locator("#assignGLCodeTable table >thead > tr > th:nth-child(1) > input");

    private ILocator _assignedTableTH => Page.FrameLocator("#v4Container").Locator("#assignMemberTable table >thead > tr > th:nth-child(1) > input");
    private ILocator addFromDept => Page.FrameLocator("#v4Container").Locator("button:has-text('Add From Other Department')");
    private ILocator dropwpownDeps => Page.FrameLocator("#v4Container").Locator(".k-window-content .k-dropdownlist");

    private ILocator dropwpownDepsInput => Page.FrameLocator("#v4Container").Locator(".k-list-ul .k-list-item");

    private ILocator _saveAndCopy => Page.FrameLocator("#v4Container").Locator("button:has-text('Save and Add')");

    private ILocator selectDepLabel =>Page.FrameLocator("#v4Container").Locator(".slide-pane label:has-text('Select Departments')");
    public DepartmentsPage(IPage page) : base(page)
    {
        
    }
        
    //Open Home and expand checkbook
    public async Task ExpandAndOpenDepartments(){
        await _checkbookLink.ClickAsync();
        await _viewDepartmentsLink.ClickAsync();
        await this.SelectCompany();
        await _closeBtnX.ClickAsync();
        await WaitUntilLoaderHidden();
        Thread.Sleep(2000);
    }
    
    //Verify Column Titles
    public async Task VerifyColumn(string column){
        await this.VerifyTitle(column);
    }
    
    //Add New Department
    public async Task AddNewDepartment(CommonContext commonContext){
        string baseGl = RandomGenerator.RandomString(10);
        commonContext.DepartmentName = baseGl;
        
        await _addNewDepartment.WaitForAsync();
        await _addNewDepartment.ClickAsync();
        
        await _departmentTableFirstChildInput.WaitForAsync();
        await _departmentTableFirstChildInput.FillAsync(baseGl);
        await _viewDept.ClickAsync();
        await WaitUntilLoaderHidden();
    }

    private async Task filterDept(string deptName){
        await _clickFilter.ClickAsync();
        Thread.Sleep(1000);
        await _filterInput.WaitForAsync();
        await _filterInput.FocusAsync();
        await _filterInput.FillAsync(deptName);
        await _filterBtn.WaitForAsync();
        await _filterBtn.ClickAsync();
        await WaitUntilLoaderHidden();
    }
    //Rename Department
    public async Task RenameDepartment(CommonContext commonContext){
        string baseGl = RandomGenerator.RandomString(10);
        await filterDept(commonContext.DepartmentName??"");
        await _departmentTableFirstChildRead.ClickAsync();
        await _departmentTableFirstChildInput.FillAsync(baseGl);
        await _viewDept.ClickAsync();
        await WaitForResponseAsyncObj("api/Department",commonContext);
        commonContext.DepartmentName = baseGl;
    }

    private async Task delateDepartment(string departmentName, bool asset = false, int totalGlCodes = 0){
        await filterDept(departmentName);
        await _departmentTableFirstChildcheckbox.ClickAsync();
        if(asset){
            try{
                var text  = await _departmentTableGlCodes.InnerTextAsync();
                Assert.Equals(totalGlCodes,Int32.Parse(text.Split(" ")[0]));
        }catch(Exception){}
        }
        await _deleteDepartment.ClickAsync();
        if(await ok.CountAsync()>0){
            await ok.ClickAsync();
        }
    }
    //Delete Department
    public async Task DeleteDepartment(CommonContext commonContext){
        await _closeBtnX.ClickAsync();
        await delateDepartment(commonContext.DepartmentName??"", true, commonContext.TotalGLCodes);
    }

    private int getText(string input){
        return Int32.Parse(Regex.Match(input, @"\(([^)]*)\)").Groups[1].Value);
    }

    private async Task verifyFilterDropdowns(ILocator dropdown, ILocator inputs){
        await dropdown.ClickAsync();
        int dropdownCount = await _activeDropdownLoopClick.CountAsync();

        int activeCnt = await inputs.CountAsync();
        var c=0;
        for(int i =1;i<=dropdownCount;i++){
            if(i==2){
                continue;   
            }
            await _activeDropdownUL.Locator("li:nth-child("+i+")").ClickAsync();
            if(i==1){
                c = await inputs.CountAsync();
            }else{
                c-= await inputs.CountAsync();
            }
            await dropdown.ClickAsync();
        }
        Assert.That(c, Is.EqualTo(activeCnt));
        await _activeDropdownUL.Locator("li:nth-child(2)").ClickAsync();
    }

    private async Task saveDept(){
        if(await _saveDepartment.IsEnabledAsync()){
            await _saveDepartment.ClickAsync();
        }
        if(await okDiscard.CountAsync()>0){
            await okDiscard.ClickAsync();
        }
        await _backBtn.ClickAsync();
    }
    //Edit Department
    public async Task EditDepartment(CommonContext commonContext){
        await filterDept(commonContext.DepartmentName??"");  
        await _departmentTableFirstChildedit.HoverAsync();
        await _departmentTableFirstChildedit.ClickAsync();
        await _closeBtnX.ClickAsync();
        await glCodeUnassignedselectAll.ClickAsync();
        await departmentMemberUnassignedselectAll.ClickAsync();
        await Assertions.Expect(glCodebutton1).ToBeEnabledAsync();
        await Assertions.Expect(glCodebutton2).ToBeDisabledAsync();
        await Assertions.Expect(glCodebutton3).ToBeDisabledAsync();
        await Assertions.Expect(glCodebutton4).ToBeDisabledAsync();
        await Assertions.Expect(departmentMemberbutton1).ToBeEnabledAsync();
        await Assertions.Expect(departmentMemberbutton2).ToBeDisabledAsync();
        await Assertions.Expect(departmentMemberbutton3).ToBeDisabledAsync();
        await Assertions.Expect(departmentMemberbutton4).ToBeDisabledAsync();
        await glCodebutton1.ClickAsync();
        await Assertions.Expect(glCodebutton1).ToBeDisabledAsync();
        await glCodebutton4.ClickAsync();
        await departmentMemberbutton1.ClickAsync();
        await Assertions.Expect(departmentMemberbutton1).ToBeDisabledAsync();
        await departmentMemberbutton4.ClickAsync();
        await Assertions.Expect(glCodebutton4).ToBeDisabledAsync();
        await glCodebutton1.ClickAsync();
        await Assertions.Expect(glCodebutton2).ToBeDisabledAsync();
        await glCodebutton4.ClickAsync();
        await glCodeUnassignedselectAll.ClickAsync();
        int count = await glCodeUnassignedinputs.CountAsync();

        var count1= count/2;
        int id=2;
        foreach(var inputs in await glCodeUnassignedinputs.AllAsync()){
            if(id<count){
                await inputs.EvaluateAsync("node=>node.click()");
            }
            id++;
        }
        await Assertions.Expect(glCodebutton3).ToBeDisabledAsync();
        await glCodebutton2.ClickAsync();
        await glCodeassignedselectAll.ClickAsync();
        count = await glCodeassignedinputs.CountAsync();

        count1= count/2;
        id=0;
        foreach(var inputs in await glCodeassignedinputs.AllAsync()){
            await inputs.EvaluateAsync("node=>node.click()");
            id++;
            if(id == 1){
                break;
            }
            
        }
        await Assertions.Expect(glCodebutton2).ToBeDisabledAsync();
        await glCodebutton3.ClickAsync();
        var seleceted1 = getText(await glCodeassignedspan.InnerTextAsync());
        await glCodeFilterinput.FillAsync("-10");
        var seleceted = getText(await glCodeassignedspan.InnerTextAsync());
        await glCodeassignedselectAll.ClickAsync();    
        commonContext.TotalGLCodes = seleceted1 - seleceted;
        await glCodebutton4.ClickAsync();
        await glCodeFilterinput.ClearAsync();
        await Assertions.Expect(departmentMemberbutton4).ToBeDisabledAsync();
        await departmentMemberbutton1.ClickAsync();
        //await glCodeassignedselectAll.ClickAsync();
        await Assertions.Expect(departmentMemberbutton2).ToBeDisabledAsync();
        await departmentMemberbutton4.ClickAsync();
        await departmentMemberUnassignedselectAll.ClickAsync();
        count = await departmentMemberUnassignedinputs.CountAsync();

        count1= count/2;
        id=2;
        foreach(var inputs in await departmentMemberUnassignedinputs.AllAsync()){
            if(id<count){
                await inputs.EvaluateAsync("node=>node.click()");
            }
            id++;
        }
        await Assertions.Expect(departmentMemberbutton3).ToBeDisabledAsync();
        await departmentMemberbutton2.ClickAsync();
        await departmentMemberassignedselectAll.ClickAsync();
        count = await departmentMemberassignedinputs.CountAsync();

        count1= count/2;
        id=0;
        foreach(var inputs in await departmentMemberassignedinputs.AllAsync()){
            await inputs.EvaluateAsync("node=>node.click()");
            id++;
            if(id == 1){
                break;
            }
        }
        await Assertions.Expect(departmentMemberbutton2).ToBeDisabledAsync();
        await departmentMemberbutton3.ClickAsync();
        seleceted1 = getText(await departmentMemberassignedspan.InnerTextAsync());
        await departmentMemberassignInput.FillAsync("can");
        seleceted1 = getText(await departmentMemberassignedspan.InnerTextAsync());
        await departmentMemberassignedselectAll.ClickAsync();
        await departmentMemberbutton4.ClickAsync();
        await departmentMemberassignInput.ClearAsync();
        commonContext.TotalDepartmentNumbers = seleceted1 - seleceted;
        await saveDept();
    }

    private async Task<DepartmentSelectedDetails> createNewDept(int count,CommonContext commonContext){
        string baseGl = RandomGenerator.RandomString(10);
        await WaitUntilLoaderHidden();
        Thread.Sleep(500);
        await _addNewDepartment.ClickAsync();
        await _departmentTableFirstChildInput.WaitForAsync();
        await _departmentTableFirstChildInput.FillAsync(baseGl);
        await _viewDept.ClickAsync();
        await WaitUntilLoaderHidden();
        DepartmentSelectedDetails departmentSelectedDetails= new DepartmentSelectedDetails();
        departmentSelectedDetails.DeptName = baseGl;
        await filterDept(baseGl);
        await _departmentTableFirstChildedit.HoverAsync();
        await _departmentTableFirstChildedit.ClickAsync();
        await _closeBtnX.ClickAsync();
        int id=1;
        foreach(var inputs in await glCodeUnassignedTable.AllAsync()){
            if(id>count)
                break;

            await inputs.Locator("input").EvaluateAsync("node=>node.click()");    
            string glode = await inputs.Locator("div").InnerTextAsync()??"";   
            departmentSelectedDetails.SelectedGlCodes.Add(glode); 
            id++;
        }
        await glCodebutton2.ClickAsync();
        id=1;
        foreach(var inputs in await departmentMemberUnassignedTable.AllAsync()){
            if(id>count)
                break;
            await inputs.Locator("input").EvaluateAsync("node=>node.click()");    
            string glode = await inputs.Locator("div").InnerTextAsync()??"";   
            departmentSelectedDetails.SelectedMembers.Add(glode);  
            id++;
        }
        await departmentMemberbutton2.ClickAsync();
        await saveDept();
        await _closeBtnX.ClickAsync();
        await filterDept(baseGl);
        return departmentSelectedDetails;
    }

    
    private async Task<Tuple<DepartmentSelectedDetails, DepartmentSelectedDetails>> BulkUpdate(CommonContext commonContext, bool bulkMember = true, Tuple<DepartmentSelectedDetails, DepartmentSelectedDetails>? t = null){
        DepartmentSelectedDetails? newGlCode =t?.Item1;
        DepartmentSelectedDetails? newGlCode1 = t?.Item2;
        if(bulkMember){
            newGlCode =await createNewDept(3,commonContext);
            newGlCode1 =await createNewDept(1,commonContext);
        }
        await _selectDepartment.ClickAsync();
        if(bulkMember){
            await _bulkMemberUpdate.ClickAsync();
        }else{
            await _bulkGLCodeUpdate.ClickAsync();
        }
        await multiSelectClick.ClickAsync();
        await multiSelectDropdwonItems.Locator("> span:has-text('"+newGlCode?.DeptName+"')").ClickAsync();
        await selectDepLabel.ClickAsync();
        var arr = bulkMember ? newGlCode?.SelectedMembers.Intersect(newGlCode1?.SelectedMembers ?? Enumerable.Empty<string>()) : newGlCode?.SelectedGlCodes.Intersect(newGlCode1?.SelectedGlCodes ?? Enumerable.Empty<string>());
        var _assignTableTH=bulkMember?_assignedTableTH:_assignedGLTableTH;
        await _assignTableTH.ClickAsync();
        var cnt = 0;
        if(bulkMember){
            cnt = await getTotalgridCount("Members", true) + (arr?.Count() ?? 0);
            await _assignSelectedMembers.EvaluateAsync("node=>node.click()");
        }else{
            cnt = await getTotalgridCount("GL", true) + (arr?.Count() ?? 0);
            await _assignSelectedGLCode.EvaluateAsync("node=>node.click()");
        }
        await filterDept(newGlCode?.DeptName??"");
        var members = string.Empty;
        if(bulkMember){
            members = await memberTextRow1.InnerTextAsync() ?? "";
        }else{
            members = await glCodeTextRow1.InnerTextAsync() ?? "";
        }
        var membersTotal = int.Parse(members.Split(' ')[0]);
        Assert.That(membersTotal, Is.EqualTo(cnt));
        if (newGlCode == null || newGlCode1 == null)
        {
            throw new ArgumentNullException("newGlCode or newGlCode1 is null");
        }
        return new Tuple<DepartmentSelectedDetails, DepartmentSelectedDetails>(newGlCode, newGlCode1);

    }
    //BulkMemberUpdate
    public async Task BulkMemberUpdateFunctionality(CommonContext commonContext)
    {
        Tuple<DepartmentSelectedDetails,DepartmentSelectedDetails> t = await BulkUpdate(commonContext);
        await filterDept(t?.Item2?.DeptName??"");
        await BulkUpdate(commonContext, false,t);
        await _selectDepartment.ClickAsync();
        await _copyButton.ClickAsync();
        string baseGl = RandomGenerator.RandomString(10);
        await _copyDepartmentpopup.FillAsync(baseGl);
        var totalGl = 0;
        var totalMembers = 0;
        var idx = 1;
        foreach(var row in await _checkesCopy.AllAsync()){
            ILocator elem = row.Locator("input");
            var hasAttribute = await elem.EvaluateAsync<bool>("node=>Promise.resolve(node.hasAttribute('checked'))");
            if(!hasAttribute){
                await row.Locator("input").ClickAsync();
            }
            string text=await row.Locator("label").InnerTextAsync() ?? "";
            string result = text.Split('(', ')')[1];
            var it = int.Parse(result);
            if(idx<3){
                totalGl+=it;
            }else{
                totalMembers+=it;
            }
            idx++;
        }
        await _copyButtonpopup.ClickAsync();
        string message = await _successMessage.InnerTextAsync();
        await filterDept(baseGl);
        var glCodes = await glCodeTextRow1.InnerTextAsync() ?? "";
        var codesTotal = int.Parse(glCodes.Split(' ')[0]);
        Assert.That(totalGl, Is.EqualTo(codesTotal));
        var members = await memberTextRow1.InnerTextAsync() ?? "";
        var membersTotal = int.Parse(members.Split(' ')[0]);
        Assert.That(membersTotal, Is.EqualTo(totalMembers));
        await delateDepartment(baseGl);
        if (t?.Item2?.DeptName != null)
        {
            await filterDept(t.Item2.DeptName);
        }
        await _departmentTableFirstChildedit.ClickAsync();
        await _closeBtnX.ClickAsync();
        await addFromDept.ClickAsync();
        await dropwpownDeps.ClickAsync();
        await dropwpownDepsInput.Locator(".k-list-item-text:has-text('"+t?.Item1?.DeptName+"')").ClickAsync();
        await _saveAndCopy.ClickAsync();
        await _backBtn.ClickAsync();
        await _closeBtnX.ClickAsync();
        await filterDept(t?.Item2?.DeptName ?? "");
        members = await memberTextRow1.InnerTextAsync() ?? "";
        var membersTotal1 = int.Parse(members.Split(' ')[0]);
        Assert.That(membersTotal1, Is.EqualTo(membersTotal));
        var gls = await glCodeTextRow1.InnerTextAsync() ?? "";
        var glsTotal1 = int.Parse(gls.Split(' ')[0]);
        Assert.That(codesTotal, Is.EqualTo(glsTotal1));
        await delateDepartment(t?.Item1?.DeptName ?? "");
        await delateDepartment(t?.Item2?.DeptName ?? "");
    }
}