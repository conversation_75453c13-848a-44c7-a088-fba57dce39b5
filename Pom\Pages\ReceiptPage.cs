using Microsoft.Playwright;
using SpecFlowProject.BusinessObjects;
using System.Collections;
using System.Text.Json;
using ClosedXML.Excel;

namespace SpecFlowProject.Pom.Pages;



public class ReceiptPage: Base
{
    private ILocator _enterReceiptsBtn => Page.Locator("a[href='/Buyer/Receiving/EnterReceipt']");
    private ILocator _enterClosePopup => _iframe.Locator(".flex.items-center.justify-between button:has-text('Close')");
    private ILocator _tableData => _iframe.Locator("table tbody tr:nth-child(1) td:nth-child(2)");
    private ILocator _selectRow => _iframe.Locator("table tbody tr:nth-child(1) td:nth-child(3)");
    private ILocator _searchPONumber => _iframe.Locator("main>div>div:nth-child(1)>div:nth-child(1)>input");
    private ILocator _recepitText => _iframe.Locator("div:nth-child(1) > div:nth-child(1) > div:nth-child(1) > p:nth-child(2)");
    private ILocator _deliveryDate => _iframe.Locator("div:nth-child(6) > div > span > span > button > span");
    private ILocator _deliveryDateSelect => _iframe.Locator(".k-calendar-nav-today");
    private ILocator _receiptStatus => _iframe.Locator("div:nth-child(7) > p.pl-1");
    private ILocator _deliveryDriverName => _iframe.Locator("div:nth-child(8) > div > span > span > input");
    private ILocator _deliveryCompanyName => _iframe.Locator("div:nth-child(9) > div > span > span > input");
    private ILocator _reOpenBtn => _iframe.Locator("button:has-text('Re-Open')");
    private ILocator _deleteBtn => _iframe.Locator("button:has-text('Delete')");
    private ILocator _deleteOK => _iframe.Locator(".flex.justify-end.gap-2 button:nth-child(1)");
    private ILocator _unDeleteBtn => _iframe.Locator("button:has-text('Undelete')");
    private ILocator _cancelReceiptBtn => _iframe.Locator("button:has-text('Cancel Receipt')");
    private ILocator _changePOAssignmentBtn => _iframe.Locator("button:has-text('Change PO Assignment')");
    private ILocator _cancelPO => _iframe.Locator("button:has-text('Cancel PO')");
    private ILocator _itemQuantity => _iframe.Locator(".k-widget >.k-pane >div>main> div:nth-child(4) table tbody tr td:nth-child(6) input");

    private ILocator _itemQuantityNoAll => _iframe.Locator(".k-widget >.k-pane >div>main> div:nth-child(4) table tbody tr td:nth-child(4) input");
    private ILocator _confirmPopupBtn => _iframe.Locator(".flex.justify-end.gap-2 button:nth-child(1)");
    private ILocator _searchSupplierName => _iframe.Locator(".k-window-content.k-dialog-content div input");
    private ILocator _createSupplier => _iframe.Locator(".w-full.flex.justify-center.gap-2.mb-1 button:nth-child(2)");
    private ILocator _selectSupplier => _iframe.Locator(".k-widget.k-window.k-dialog .k-widget.k-grid tbody tr:nth-child(1) td:nth-child(1)");
    private ILocator _selectInvGrid => _iframe.Locator(".k-grid-content table tbody tr:nth-child(1) td:nth-child(2)");
    private ILocator _addLineItemBtn => _iframe.Locator("button:has-text('Add Line Item')");
    private ILocator _setZeroQtyBtn => _iframe.Locator("button:has-text('Set Zero Quantity')");
    private ILocator _bulkAssignBtn => _iframe.Locator("button:has-text('Bulk Assign GL Codes')");
    private ILocator _bulkAssignBtnDropdwn => _iframe.Locator(".k-widget.k-window.k-dialog div p span .k-input-value-text");
    private ILocator _selectbulkGLCode => _iframe.Locator(".k-list ul li:nth-child(3)");
    private ILocator _assignSelectedGLCode => _iframe.Locator(".flex.flex-row.justify-end.gap-2 button:nth-child(1)");
    private ILocator _selectedGLCode => _iframe.Locator(".k-window-content.k-dialog-content span span span");
    private ILocator _combineLineItemsBtn => _iframe.Locator("button:has-text('Combine Line Items')");
    private ILocator _combineLineItemsQty => _iframe.Locator("table > tbody > tr > td:nth-child(1) > span input");
    private ILocator _combineLineItemsName => _iframe.Locator("table > tbody > tr > td:nth-child(2) > span input");
    private ILocator _combineLineItemDesc => _iframe.Locator("table > tbody > tr > td:nth-child(3) > span input");
    private ILocator _combineLineItemGL => _iframe.Locator("table > tbody > tr > td:nth-child(4) > span span span");
    private ILocator _combineLineItemSelectedGLCode => _iframe.Locator(".k-list ul li:nth-child(3)");
    private ILocator _combineLineItemUOM => _iframe.Locator(".k-window-content table > tbody > tr > td:nth-child(5) > span input");
    private ILocator _combineLineItemApplyBtn => _iframe.Locator(".flex.justify-end.gap-2 button:nth-child(1)");
    private ILocator _newlineLitemName1 => _iframe.Locator("table > tbody > tr:nth-child(3) > td:nth-child(9) > span > span > input");
    private ILocator _newlineLitemName2 => _iframe.Locator("tbody > tr:nth-child(4) > td:nth-child(9)  input");
    private ILocator _newlineLitemQty1 => _iframe.Locator(".k-grid-container:last-child tbody tr:nth-child(3) td:nth-child(6) input");
    private ILocator _newlineLitemQty2 => _iframe.Locator(".k-grid-container:last-child tbody tr:nth-child(4) td:nth-child(6) input");
    private ILocator _newlineLitemDesc1 => _iframe.Locator("tbody > tr:nth-child(3) > td:nth-child(10) > span > span input");
    private ILocator _newlineLitemDesc2 => _iframe.Locator("tbody > tr:nth-child(4) > td:nth-child(10) > span > span input");
    private ILocator _newlineLitemGLCode1 => _iframe.Locator("tbody > tr:nth-child(3) > td:nth-child(11) > span > span");

    private ILocator _newlineLitemName1NoAll => _iframe.Locator("table > tbody > tr:nth-child(3) > td:nth-child(5) > span > span > input");
    private ILocator _newlineLitemName2NoAll => _iframe.Locator("tbody > tr:nth-child(4) > td:nth-child(5)  input");
    private ILocator _newlineLitemQty1NoAll => _iframe.Locator(".k-grid-container:last-child tbody tr:nth-child(3) td:nth-child(4) input");
    private ILocator _newlineLitemQty2NoAll => _iframe.Locator(".k-grid-container:last-child tbody tr:nth-child(4) td:nth-child(4) input");
    private ILocator _newlineLitemDesc1NoAll => _iframe.Locator("tbody > tr:nth-child(3) > td:nth-child(6) > span > span input");
    private ILocator _newlineLitemDesc2NoAll => _iframe.Locator("tbody > tr:nth-child(4) > td:nth-child(6) > span > span input");
    private ILocator _newlineLitemGLCode1NoAll => _iframe.Locator("tbody > tr:nth-child(3) > td:nth-child(7) > span > span");
    private ILocator _newlineLitemGLCodeSelect => _iframe.Locator(".k-list ul li:nth-child(3)");
    private ILocator _newlineLitemGLCode2 => _iframe.Locator("tbody > tr:nth-child(4) > td:nth-child(11) > span > span");
    private ILocator _newlineLitem1UOM => _iframe.Locator("table > tbody > tr:nth-child(3) > td:nth-child(12) > span > span > input");
    private ILocator _newlineLitem2UOM => _iframe.Locator("table > tbody > tr:nth-child(4) > td:nth-child(12) > span > span > input");

    private ILocator _newlineLitemGLCode2NoAll => _iframe.Locator("tbody > tr:nth-child(4) > td:nth-child(7) > span > span");
    private ILocator _newlineLitem1UOMNoAll => _iframe.Locator("table > tbody > tr:nth-child(3) > td:nth-child(8) > span > span > input");
    private ILocator _newlineLitem2UOMNoAll => _iframe.Locator("table > tbody > tr:nth-child(4) > td:nth-child(8) > span > span > input");
    private ILocator _rowsInReceipt => _iframe.Locator(".k-widget main div:nth-child(4) table tbody tr");
    private ILocator _tbodyInReceipt => _iframe.Locator(".k-widget main div:nth-child(4) table tbody");
    private ILocator _selectLineItem1 => _iframe.Locator("div:nth-child(1) > table > tbody > tr:nth-child(1) > td:nth-child(2) input");
    private ILocator _selectLineItem2 => _iframe.Locator("div:nth-child(1) > table > tbody > tr:nth-child(2) > td:nth-child(2) input");
    private ILocator _selectLineItem3 => _iframe.Locator("div:nth-child(1) > table > tbody > tr:nth-child(3) > td:nth-child(2) input");
    private ILocator _selectLineItem4 => _iframe.Locator("div:nth-child(1) > table > tbody > tr:nth-child(4) > td:nth-child(2) input");
    private ILocator _changePO => _iframe.Locator("button:has-text('Change PO Assignment')");
    private ILocator _selectChangePO => _iframe.Locator(".k-window-content.k-dialog-content table tr:nth-child(1) td:nth-child(3)");
    private ILocator _chnagePONext => _iframe.Locator(".k-window-content nav > div > div > button");
    private ILocator _applyPO => _iframe.Locator("button:has-text('Apply to this PO')");
    private ILocator _relatedDocTabClick => _iframe.Locator("div > main > div:nth-child(2)");
    private ILocator _relatedReceiptNumber => _iframe.Locator("div:nth-child(1) > table > tbody > tr:nth-child(1) > td:nth-child(2) > button");
    private ILocator _relatedNewReceiptNumber => _iframe.Locator("div:nth-child(1) > table > tbody > tr.k-master-row.k-alt > td:nth-child(2) > button");
    private ILocator _relatedNewInvoiceNumber => _iframe.Locator("tbody > tr:nth-child(3) > td:nth-child(2) > button");
    private ILocator _documentType(int i) =>_iframe.Locator(".p-3 .k-grid-table  tbody tr:nth-child("+i+") td:nth-child(3)");
    private ILocator _documentStatus(int i) =>_iframe.Locator(".p-3 .k-grid-table  tbody tr:nth-child("+i+") td:nth-child(7)");
    private ILocator _documentInput(int i) =>_iframe.Locator(".p-3 .k-grid-table  tbody tr:nth-child("+i+") td:nth-child(1)>input");
    private ILocator _rowsInRelated => _iframe.Locator(".p-3 .k-grid-table  tbody tr td:nth-child(3)");
    private ILocator _relateddocTest(int i) =>_iframe.Locator(".p-3 .k-grid-table  tbody tr:nth-child("+i+") td:nth-child(4)");
    private ILocator _relateddocButton(int i) =>_iframe.Locator("table > tbody > tr:nth-child("+i+") > td:nth-child(4) > div > div > button");
    private ILocator _tablePOColumns => _iframe.Locator(".k-grid-table tr td:nth-child(3)");
    private ILocator _poNumberInGrid(int i) => _iframe.Locator(".k-grid-table tr:nth-child("+i+") td:nth-child(3)");
    private ILocator _poNumberSelect(int i) => _iframe.Locator(".k-grid-table tr:nth-child("+i+") td:nth-child(1) input");
    private ILocator bulkCompleteDocBtn => Page.FrameLocator("#v4Container").Locator("button:has-text('Complete Document')");
    private ILocator bulkDeleteBtn => Page.FrameLocator("#v4Container").Locator("div.flex.flex-wrap > div:nth-child(2) > div > button");
    private ILocator bulkUndeleteBtn => Page.FrameLocator("#v4Container").Locator("button:has-text('Undelete')");
    private ILocator bulkCancelPOBtn => Page.FrameLocator("#v4Container").Locator("button:has-text('Cancel PO')");
    private ILocator bulkReopenBtn => Page.FrameLocator("#v4Container").Locator("button:has-text('Re-Open')");
    private ILocator okConfirmBtn => Page.FrameLocator("#v4Container").Locator(".flex.justify-end.gap-2 button:nth-child(1)");
    private ILocator showFilters => Page.FrameLocator("#v4Container").Locator(".flex.items-center.justify-between.w-full span:nth-child(2) span span");
    private ILocator showComplete => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(5)");
    private ILocator showAllReceipts => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(2)");
    private ILocator showAllActiveReceipts => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(1)");
    private ILocator showQtyLessThan100 => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(3)");
    private ILocator showQtyGreaterThan100 => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(4)");
    private ILocator showDeleted => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(6)");
    private ILocator showPendingGoods => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(7)");
    private ILocator showStatus => Page.FrameLocator("#v4Container").Locator(".k-grid-table tr:nth-child(2) td:nth-child(5)");
    private ILocator clearFiltersBtn => Page.FrameLocator("#v4Container").Locator(".flex.items-center.justify-between.w-full button:nth-child(1)");
    private ILocator receiptsStatus => Page.FrameLocator("#v4Container").Locator(".k-grid-table tr:nth-child(1) td:nth-child(5)");
    private ILocator exportToExcel => Page.FrameLocator("#v4Container").Locator(".flex.items-center.justify-between.w-full div span:nth-child(1) span span");
    private ILocator presetFilters => Page.FrameLocator("#v4Container").Locator(".flex.items-center.justify-between.w-full div span:nth-child(2) span span");
    private ILocator showAll => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(3)");
    private ILocator currentPage => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(2)");
    private ILocator allPages => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(3)");
    private ILocator _poNumberFilterBtn => _iframe.Locator("div th:nth-child(3) span.k-cell-inner > div > span");
    private ILocator _filteredReeciptBtn => _iframe.Locator("div tbody tr:nth-child(1) td:nth-child(4) button");
    private ILocator _printReceipt => _iframe.Locator("button:has-text('Print')");
    private ILocator _showHistory => _iframe.Locator(".p-4 label:nth-child(1) input");
    private ILocator _showNotes => _iframe.Locator(".p-4 label:nth-child(2) input");
    private ILocator _includeAttachments => _iframe.Locator(".p-4 label:nth-child(3) input");
    private ILocator _printPDF => _iframe.Locator("div.k-dialog-buttongroup.k-actions.k-actions-stretched > div > button:nth-child(1)");
    private ILocator _savePDF => _iframe.Locator(".flex.flex-row.gap-2.justify-end a button");

    private ILocator _theadItemThree => _iframe.Locator("main>div:last-child .k-grid-header > .k-grid-header-wrap > table > thead > tr > th:nth-child(3) > span >span");
    private string allItemReceived="All Items Received";
    public ReceiptPage(IPage page) : base(page)
    {
        _logger.Debug("ReceiptPage initialized");
    }

    public async Task ExpandReceiving()
    {
        _logger.Debug("Expanding Receiving module");
        if(!await IsVisibleAsync(_receiving,1000,5)){
            _logger.Error("Receiving Module is not visible for the user");
            Assert.Fail("Receiving Module is not visible for the user");
        }
        await _receiving.EvaluateAsync("node=>node.click()");
        _logger.Debug("Receiving module expanded successfully");
    }

    //Navigate to the Enter Receipts Page
     public async Task EnterReceipts()
    {
        _logger.Debug("Navigating to Enter Receipts page");
        await _enterReceiptsBtn.EvaluateAsync("node=>node.click()");
        _logger.Debug("Navigated to Enter Receipts page");
    }
    private async Task reenterPo(int count){
        _logger.Debug("Attempting to reenter PO, attempt {Count}", count);
        if(await _tableData.CountAsync()==0 && count<10){
            Thread.Sleep(100);
            await reenterPo(count++);
        }
        _logger.Debug("PO reenter process completed");
    }
    //Search PO Number
    public async Task SearchPO(CommonContext commonContext)
    {
        _logger.Debug("Searching for PO number: {PoNumber}", commonContext.PoNumber);
        await _closeBtnX.ClickAsync();
        await _tableData.WaitForAsync();
        await _searchPONumber.FillAsync(commonContext.PoNumber);
        await _tableData.WaitForAsync();
        _logger.Debug("PO search completed");
    }

    public async Task SearchPONumber(string poNumber)
    {
        _logger.Debug("Searching for PO number: {PoNumber}", poNumber);
        await _closeBtnX.ClickAsync();
        await _tableData.WaitForAsync();
        await _searchPONumber.FillAsync(poNumber);
        await _tableData.WaitForAsync();
        _logger.Debug("PO search completed");
    }

    public async Task NavigatePages()
    {
        _logger.Debug("Navigating pages");
        await _nextBtn.ClickAsync();
        await _closeBtnX.WaitForAsync();
        await _closeBtnX.ClickAsync();
        await _nextBtn.ClickAsync();
        await continueWithoutattachemnt.ClickAsync();
        _logger.Debug("Navigation completed");
    }
    public async Task NavigatePartialCreate(CommonContext commonContext)
    {
        _logger.Debug("Starting partial receipt creation");
        await _closeBtnX.ClickAsync();
        var _receiptNumber=await _recepitText.InnerTextAsync();
        _logger.Debug("Receipt number: {ReceiptNumber}", _receiptNumber);
        await EnterReceipts();
        if(await IsVisibleAsync(_company,1000,5)){
            _logger.Debug("Company selection required");
            await _company.ClickAsync();
            await _companySelect.ClickAsync();
        }
        await SearchPO(commonContext);
        await _nextBtn.ClickAsync();
        await _closeBtnX.ClickAsync();
        await Assertions.Expect(_selectInvGrid).ToContainTextAsync(_receiptNumber);
        _logger.Debug("Partial receipt creation completed");
    }

    //Create Receipt
    public async Task<string> CreateReceipt(List<string> quantities){
        _logger.Debug("Creating receipt with {Count} quantities", quantities.Count);
        await _closeBtnX.ClickAsync();
        await _deliveryDate.ClickAsync();
        await _deliveryDateSelect.WaitForAsync();
        await _deliveryDateSelect.ClickAsync();
        await _deliveryDriverName.FillAsync("Test");
        await _deliveryCompanyName.FillAsync("Avendra");
        var index =0;
        var str =await _theadItemThree.TextContentAsync() ?? string.Empty;
        var hasAddit = str==allItemReceived;
        var itm = hasAddit?_itemQuantity:_itemQuantityNoAll;
        _logger.Debug("Using {ItemType} for quantity input", hasAddit ? "itemQuantity" : "itemQuantityNoAll");
        foreach (var input in quantities){
            if(await itm.Nth(index).IsEditableAsync())
            {
                _logger.Debug("Filling quantity {Index} with value {Value}", index, input);
                await itm.Nth(index).FillAsync(input);
                index++;
            }
        }
        _logger.Debug("Filled {Count} quantity fields", index);
        await _showScans.ClickAsync();
        await _uploadBtn.ClickAsync();
        _logger.Debug("Uploading document: {FilePath}", UPLOADPDFFILE);
        await _uploadDoc.SetInputFilesAsync(UPLOADPDFFILE);
        await _uploadClose.ClickAsync();
        await _hideScans.ClickAsync();   
        if(!await _completeBtn.IsEnabledAsync()){
            _logger.Debug("Complete button not enabled, saving receipt");
            await _saveBtn.ClickAsync();
        }
        var receiptNumber = await _recepitText.InnerTextAsync();
        _logger.Debug("Receipt created with number: {ReceiptNumber}", receiptNumber);
        return receiptNumber;
    }

    //Complete the Receipt
    public async Task CompleteReceipt(){
        _logger.Debug("Starting CompleteReceipt method");
        if(!await IsVisibleAsync(_completeBtn,1000,5)){
            _logger.Error("Complete button not visible - permission 743 required");
            Assert.Fail("enable 743 permisison for the user");
        }
        _logger.Debug("Clicking complete button");
        await _completeBtn.ClickAsync();  
        _logger.Debug("Clicking confirm popup button");
        await _confirmPopupBtn.ClickAsync();
        _logger.Debug("Receipt completed successfully");
    }

    //Create Receipt without PO
    public async Task CreateReceiptWithoutPO(string supplier)
    {
        _logger.Debug("Starting CreateReceiptWithoutPO for supplier: {Supplier}", supplier);
        await _closeBtnX.ClickAsync();
        await _tableData.WaitForAsync();
        _logger.Debug("Clicking next button");
        await _nextBtn.ClickAsync();
        if(!await IsVisibleAsync(_searchSupplierName,1000,5)){
            _logger.Error("Search supplier field not visible - permission 744 required");
            Assert.Fail("Disable 744 permisison for the user");
        }
        _logger.Debug("Filling supplier name: {Supplier}", supplier);
        await _searchSupplierName.FillAsync(supplier);
        await _selectSupplier.ClickAsync();
        _logger.Debug("Creating receipt without PO");
        await _createReceiptBtn.ClickAsync();
        await continueWithoutattachemnt.ClickAsync();
        await _closeBtnX.ClickAsync();
        _logger.Debug("Receipt without PO created successfully");
        await Assertions.Expect(Page).ToHaveURLAsync(Page.Url);
    }

    //create a Supplier in the Enter Receipts page
    public async Task CreateSupplier()
    {  
        _logger.Debug("Starting CreateSupplier method");
        await _closeBtnX.ClickAsync();
        await _tableData.WaitForAsync();
        _logger.Debug("Clicking next button");
        await _nextBtn.ClickAsync();
        if(!await IsVisibleAsync(_createSupplier,1000,5)){
            _logger.Error("Create supplier button not visible - permission 744 required");
            Assert.Fail("Disable 744 permisison for the user");
        }
        _logger.Debug("Clicking create supplier button");
        await _createSupplier.ClickAsync();
        _logger.Debug("Supplier creation initiated");
        await Assertions.Expect(Page).ToHaveURLAsync(Page.Url);
    }

    public async Task VerifyPOGridColumns(string column)
    {
        _logger.Debug("Verifying PO grid column: {Column}", column);
        await this.VerifyTitle(column);
    }  

    //Re-open the receipt
    public async Task ReopenReceipt()
    {
        _logger.Debug("Reopening receipt");
        await _reOpenBtn.ClickAsync();
        _logger.Debug("Receipt reopened successfully");
    }

    //Delete the receipt
    public async Task DeleteReceipt()
    {
        _logger.Debug("Starting DeleteReceipt method");
        if(!await IsVisibleAsync(_completeBtn,1000,5)){
            _logger.Error("Complete button not visible - permission 740 required");
            Assert.Fail("enable 740 permisison for the user");
        }
        _logger.Debug("Clicking delete button");
        await _deleteBtn.First.ClickAsync();
        _logger.Debug("Confirming deletion");
        await _deleteOK.ClickAsync();
        _logger.Debug("Receipt deleted successfully");
    }

    //Undelete the receipt
    public async Task UndeleteReceipt()
    {
        _logger.Debug("Undeleting receipt");
        await _unDeleteBtn.ClickAsync();
        _logger.Debug("Receipt undeleted successfully");
    }

    //Cancel the receipt
    public async Task CancelReceipt()
    {
        _logger.Debug("Cancelling receipt");
        await _cancelReceiptBtn.ClickAsync();
        _logger.Debug("Confirming cancellation");
        await _deleteOK.ClickAsync();
        _logger.Debug("Receipt cancelled successfully");
    }

    //Save the Receipt
    public async Task VerifyPrintReceipt()
    {
        _logger.Debug("Starting VerifyPrintReceipt method");
        await _printReceipt.ClickAsync();
        _logger.Debug("Configuring print options");
        await _showHistory.ClickAsync();
        await _showNotes.ClickAsync();
        await _includeAttachments.ClickAsync();
        _logger.Debug("Saving receipt as PDF");
        await _savePDF.ClickAsync();
        string downloadPath = System.AppDomain.CurrentDomain.BaseDirectory;
        _logger.Debug("Waiting for file download to: {DownloadPath}", downloadPath);
        bool isDownloaded = await WaitForFileDownload(downloadPath, UPLOADPDFFILE, TimeSpan.FromSeconds(30));
        if (!isDownloaded)
        {
            _logger.Error("PDF file download failed");
            Assert.Fail("File has not been downloaded");
        }
        _logger.Debug("Receipt PDF verified and downloaded successfully");
    }

    private async Task<bool> WaitForFileDownload(string downloadPath, string fileName, TimeSpan timeout)
    {
        _logger.Debug("Starting WaitForFileDownload method with path: {DownloadPath}, file: {FileName}", downloadPath, fileName);
        var start = DateTime.Now;
        while (DateTime.Now - start < timeout)
        {
            _logger.Debug("Checking for file existence");
            var files = Directory.EnumerateFiles(downloadPath, fileName);
            if (files.Any())
            {
                _logger.Debug("File found successfully");
                return true;
            }
            _logger.Debug("File not found yet, waiting 500ms before next check");
            await Task.Delay(500); // Check every 500 milliseconds
        }
        _logger.Error("File download timed out after {Timeout} seconds", timeout.TotalSeconds);
        return false;
    }

    //Add New Line Items
    public async Task AddNewLineItem()
    {
        _logger.Debug("Starting AddNewLineItem method");
        //await _rowsInReceipt.ScrollIntoViewIfNeededAsync();
        var _rowCount = await _rowsInReceipt.CountAsync();
        _logger.Debug("Current row count: {RowCount}", _rowCount);
        if(await IsVisibleAsync(_addLineItemBtn,1000,5)){
            _logger.Debug("Add line item button is visible, clicking it");
            await _addLineItemBtn.DblClickAsync();
            var str =await _theadItemThree.TextContentAsync() ?? string.Empty;
            var hasAddit = str==allItemReceived;
            _logger.Debug("Has 'All Items Received' column: {HasAllItemsReceived}", hasAddit);
            if(hasAddit){
                _logger.Debug("Adding line items with 'All Items Received' column");
                await _newlineLitemName1.WaitForAsync();
                await _newlineLitemName1.FillAsync("123");
                await _newlineLitemDesc1.FillAsync("apple");
                await _newlineLitemQty1.FillAsync("1");
                await _newlineLitemGLCode1.ClickAsync();
                await _newlineLitemGLCodeSelect.EvaluateAsync("node=>node.click()");
                await _newlineLitem1UOM.FillAsync("AA");
                await _newlineLitemName2.WaitForAsync();
                await _newlineLitemName2.FillAsync("456");
                await _newlineLitemDesc2.FillAsync("Grapes");
                await _newlineLitemQty2.FillAsync("1");
                await _newlineLitemGLCode2.ClickAsync();
                await _newlineLitemGLCodeSelect.EvaluateAsync("node=>node.click()");
                await _newlineLitem2UOM.FillAsync("BB");
                _logger.Debug("First and second line items added successfully");
            }else{
                _logger.Debug("Adding line items without 'All Items Received' column");
                await _newlineLitemName1NoAll.WaitForAsync();
                await _newlineLitemName1NoAll.FillAsync("123");
                await _newlineLitemDesc1NoAll.FillAsync("apple");
                await _newlineLitemQty1NoAll.FillAsync("1");
                await _newlineLitemGLCode1NoAll.ClickAsync();
                await _newlineLitemGLCodeSelect.EvaluateAsync("node=>node.click()");
                await _newlineLitem1UOMNoAll.FillAsync("AA");
                await _newlineLitemName2NoAll.WaitForAsync();
                await _newlineLitemName2NoAll.FillAsync("456");
                await _newlineLitemDesc2NoAll.FillAsync("Grapes");
                await _newlineLitemQty2NoAll.FillAsync("1");
                await _newlineLitemGLCode2NoAll.ClickAsync();
                await _newlineLitemGLCodeSelect.EvaluateAsync("node=>node.click()");
                await _newlineLitem2UOMNoAll.FillAsync("BB");
                _logger.Debug("First and second line items added successfully");
            }
        }
        else {
            _logger.Warning("Add line item button is not visible");
        }
    }

    //verify delete the line items
    public async Task VerifyDeleteBtn()
    {
        _logger.Debug("Starting VerifyDeleteBtn method");
        var _rowCount = await _rowsInReceipt.CountAsync();
        _logger.Debug("Current row count: {RowCount}", _rowCount);
        var nextCnt = 1;            
        for(int i=0;i<(_rowCount);++i)
        {
            _logger.Debug("Checking delete button for row {RowNumber}", nextCnt);
            if(await IsVisibleAsync(_tbodyInReceipt.Locator("tr:nth-child("+ nextCnt +") td:nth-child(14) button"),1000,5)){
                _logger.Debug("Delete button found for row {RowNumber}, clicking it", nextCnt);
                await _tbodyInReceipt.Locator("tr:nth-child("+ nextCnt +") td:nth-child(14) button").ClickAsync();
                await _deleteOK.ClickAsync();
                _logger.Debug("Row {RowNumber} deleted successfully", nextCnt);
            }
            else {
                _logger.Debug("No delete button found for row {RowNumber}", nextCnt);
            }
            nextCnt++;
        }
        _logger.Debug("All rows processed for deletion");
        //await _saveBtn.ClickAsync();  
    }

    //verify setzero quantity
    public async Task SetZeroToQty()
    {
        _logger.Debug("Starting SetZeroToQty method");
        await Task.Run(() =>
        {
            _logger.Debug("Running empty task in SetZeroToQty");
            // Add your asynchronous code here
        });
        _logger.Debug("Completed SetZeroToQty method");
    }

    //verify Bulkassign
    public async Task BulkAssignGL()
    {
        _logger.Debug("Starting BulkAssignGL method");
        await _selectLineItem3.EvaluateAsync("node=>node.click()");
        await _selectLineItem4.EvaluateAsync("node=>node.click()");
        _logger.Debug("Selected line items 3 and 4");
        
        if(await _bulkAssignBtn.IsEnabledAsync())
        {
            _logger.Debug("Bulk assign button is enabled, clicking it");
            await _bulkAssignBtn.ClickAsync();
            await _bulkAssignBtnDropdwn.WaitForAsync();
            await _bulkAssignBtnDropdwn.ClickAsync();
            await _selectbulkGLCode.EvaluateAsync("node=>node.click()");
            await _selectedGLCode.WaitForAsync();
            string glcode=await _selectedGLCode.InnerTextAsync();
            _logger.Debug("Selected GL code: {GLCode}", glcode);
            await _assignSelectedGLCode.ClickAsync();
            //await _saveBtn.ClickAsync();
            var str =await _theadItemThree.TextContentAsync() ?? string.Empty;
            var hasAddit = str==allItemReceived;
            _logger.Debug("Has 'All Items Received' column: {HasAllItemsReceived}", hasAddit);
            var gl1 = hasAddit?_newlineLitemGLCode1:_newlineLitemGLCode1NoAll;
            var gl2 = hasAddit?_newlineLitemGLCode2:_newlineLitemGLCode2NoAll;
            await Assertions.Expect(gl1).ToContainTextAsync(glcode);
            await Assertions.Expect(gl2).ToContainTextAsync(glcode);
            _logger.Debug("GL code verification completed successfully");
        }
        else {
            _logger.Warning("Bulk assign button is not enabled");
        }
    }

    //verify combinelineitems with qty zero
    public async Task CombineLineItemsForQtyZero()
    {
        _logger.Debug("Starting CombineLineItemsForQtyZero method");
        await _selectLineItem3.EvaluateAsync("node=>node.click()");
        await _selectLineItem4.EvaluateAsync("node=>node.click()");
        _logger.Debug("Selected line items 3 and 4");
        if(await _combineLineItemsBtn.IsEnabledAsync())
        {
            _logger.Debug("Combine line items button is enabled, clicking it");
            await _combineLineItemsBtn.ClickAsync();
            await _combineLineItemsQty.WaitForAsync();
            await _combineLineItemsQty.ClearAsync();
            await _combineLineItemsQty.FillAsync("1");
            await _combineLineItemsName.ClearAsync();
            await _combineLineItemsName.FillAsync("combineline");
            await _combineLineItemDesc.ClearAsync();
            await _combineLineItemDesc.FillAsync("combinedescription");
            await _combineLineItemGL.ClickAsync();
            await _combineLineItemSelectedGLCode.EvaluateAsync("node=>node.click()");
            await _combineLineItemUOM.ClearAsync();
            await _combineLineItemUOM.FillAsync("CC");
            _logger.Debug("Filled combine line item form");
            string getItemName=await _combineLineItemsName.InnerTextAsync();
            string getItemDesc=await _combineLineItemDesc.InnerTextAsync();
            string getGLCode=await _combineLineItemGL.InnerTextAsync();
            string getUOM=await _combineLineItemUOM.InnerTextAsync();
            _logger.Debug("Form values - Name: {Name}, Description: {Description}, GL Code: {GLCode}, UOM: {UOM}", 
                getItemName, getItemDesc, getGLCode, getUOM);
            await _combineLineItemApplyBtn.ClickAsync();
            _logger.Debug("Applied combine line items");
            //await _saveBtn.ClickAsync();
            var str =await _theadItemThree.TextContentAsync() ?? string.Empty;
            var hasAddit = str==allItemReceived;
            _logger.Debug("Has 'All Items Received' column: {HasAllItemsReceived}", hasAddit);
            if(hasAddit){
                _logger.Debug("Verifying combined line item with 'All Items Received' column");
                await Assertions.Expect(_newlineLitemName1).ToContainTextAsync(getItemName);
                await Assertions.Expect(_newlineLitemDesc1).ToContainTextAsync(getItemDesc);
                await Assertions.Expect(_newlineLitemGLCode1).ToContainTextAsync(getGLCode);
                await Assertions.Expect(_newlineLitem1UOM).ToContainTextAsync(getUOM);
            }else{
                _logger.Debug("Verifying combined line item without 'All Items Received' column");
                await Assertions.Expect(_newlineLitemName1NoAll).ToContainTextAsync(getItemName);
                await Assertions.Expect(_newlineLitemDesc1NoAll).ToContainTextAsync(getItemDesc);
                await Assertions.Expect(_newlineLitemGLCode1NoAll).ToContainTextAsync(getGLCode);
                await Assertions.Expect(_newlineLitem1UOMNoAll).ToContainTextAsync(getUOM);
            }
            _logger.Debug("Combined line item verification completed successfully");
        }
        else {
            _logger.Warning("Combine line items button is not enabled");
        }
    }

    //verify combinelineitems with qty one and zero
    public async Task CombineLineItemsQtyOneAndZero()
    {
        _logger.Debug("Starting CombineLineItemsQtyOneAndZero");
        await _selectLineItem2.EvaluateAsync("node=>node.click()");
        _logger.Debug("Selected line item 2");
        await _selectLineItem3.EvaluateAsync("node=>node.click()");
        _logger.Debug("Selected line item 3");
        if(await _combineLineItemsBtn.IsEnabledAsync())
        {
            _logger.Debug("Combine line items button is enabled, clicking it");
            await _combineLineItemsBtn.ClickAsync();
            _logger.Debug("Clearing and filling combine line items quantity");
            await _combineLineItemsQty.ClearAsync();
            await _combineLineItemsQty.FillAsync("2");
            _logger.Debug("Clearing and filling UOM");
            await _combineLineItemUOM.ClearAsync();
            await _combineLineItemUOM.FillAsync("CC");
            string getUOM=await _combineLineItemUOM.InnerTextAsync();
            _logger.Debug("UOM value: {UOM}", getUOM);
            await _combineLineItemApplyBtn.ClickAsync();
            _logger.Debug("Applied combine line items");
            await VerifyDeleteBtn();
            _logger.Debug("Verified delete button");
        }
        else
        {
            _logger.Warning("Combine line items button is not enabled");
        }
    }

     //Combining line items qty having 1 and 1
    public async Task CombineLineItemsQtyOneAndOne()
    {
        _logger.Debug("Starting CombineLineItemsQtyOneAndOne");
        await _selectLineItem1.EvaluateAsync("node=>node.click()");
        _logger.Debug("Selected line item 1");
        await _selectLineItem2.EvaluateAsync("node=>node.click()");
        _logger.Debug("Selected line item 2");
    }

    public async Task VerifyLineItemsTableGrid(string column)
    {
        _logger.Debug("Starting VerifyLineItemsTableGrid with column: {Column}", column);
        await this.VerifyTitle(column);
        _logger.Debug("Verified title with column: {Column}", column);
    }

     // Click on Change PO Assignment
    public async Task ChangePOAssignment(CommonContext commonContext)
    {
        _logger.Debug("Starting ChangePOAssignment");
        if(!await IsVisibleAsync(_changePO,1000,5)){
            _logger.Error("Change PO button not visible - permission 756 required");
            Assert.Fail("enable 756 permisison for the user");
        }
        await _changePO.ClickAsync();
        _logger.Debug("Clicked on Change PO button");
        await _selectChangePO.ClickAsync();
        _logger.Debug("Selected Change PO");
        await _chnagePONext.ClickAsync();
        _logger.Debug("Clicked on Change PO Next button");
        var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await _applyPO.ClickAsync();
            _logger.Debug("Clicked on Apply PO button");
        });
        _logger.Debug("New page opened, bringing to front");
        BringSecondPageToFront(newPage,commonContext);
        await _closeBtnX.ClickAsync();
        _logger.Debug("Clicked on Close button");
        await VerifyStatus("changed PO");
        _logger.Debug("Verified status: changed PO");
        await BringBasePageToFront(commonContext);
        _logger.Debug("Brought base page to front");
    }

    public async Task ClickOnRelatedDocTab()
    {
        _logger.Debug("Clicking on Related Documents tab");
        await _relatedDocTabClick.ClickAsync();
        _logger.Debug("Clicked on Related Documents tab");
    }

    //verify Related Documents table grid columns
    public async Task VerifyRelatedDocsGridColumns(string column)
    {
        _logger.Debug("Starting VerifyRelatedDocsGridColumns with column: {Column}", column);
        await this.VerifyTitle(column);
        _logger.Debug("Verified title with column: {Column}", column);
    }

    // Verify current receipt number is showing in the related documents table grid
    public async Task VerifyCurrentReceiptNumberIsShowingInRelatedDocGrid()
    {
        _logger.Debug("Starting VerifyCurrentReceiptNumberIsShowingInRelatedDocGrid");
        await ClickOnRelatedDocTab();
        var receiptNumber= await _recepitText.InnerTextAsync();
        _logger.Debug("Current receipt number: {ReceiptNumber}", receiptNumber);
        await Assertions.Expect(_relatedReceiptNumber).ToContainTextAsync(receiptNumber);
        _logger.Debug("Verified receipt number is showing in related documents grid");
    }

    // Creating a new receipt in the related documents tab
    public async Task CreateNewReceipt(CommonContext commonContext)
    {
        _logger.Debug("Starting CreateNewReceipt");
        await _createNewDocument.EvaluateAsync("node=>node.click()");
        _logger.Debug("Clicked on Create New Document button");
        await _createReceiptBtn.WaitForAsync();
        await _createReceiptBtn.ClickAsync();
        _logger.Debug("Clicked on Create Receipt button");
        IPage newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await continueWithoutattachemnt.ClickAsync();
            _logger.Debug("Clicked on Continue Without Attachment button");
        });
        _logger.Debug("New page opened, bringing to front");
        BringSecondPageToFront(newPage,commonContext);
        await BringBasePageToFront(commonContext);
        _logger.Debug("Brought base page to front");
    }

    // Creating a new invoice in the related documents tab
    public async Task CreateNewInvoice(CommonContext commonContext)
    {
        _logger.Debug("Starting CreateNewInvoice");
        await ClickOnRelatedDocTab();
        if(!await IsVisibleAsync(_createNewDocument,1000,5)){
            _logger.Error("Create New Document button not visible - invoice module required");
            Assert.Fail("enable invoice module for the user");
        }
        await _createNewDocument.EvaluateAsync("node=>node.click()");
        _logger.Debug("Clicked on Create New Document button");
        await _invoiceCreditBtn.ClickAsync();
        _logger.Debug("Clicked on Invoice/Credit button");
        var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await continueWithoutattachemnt.ClickAsync();
            _logger.Debug("Clicked on Continue Without Attachment button");
        });
        _logger.Debug("New page opened, bringing to front");
        BringSecondPageToFront(newPage,commonContext);
        await BringBasePageToFront(commonContext);
        _logger.Debug("Brought base page to front");
    }

    // Verify Link to Selected functionality
    public async Task LinkToSelected()
    {
        _logger.Debug("Starting LinkToSelected");
        await ClickOnRelatedDocTab();
        var totalCount=await _rowsInRelated.CountAsync();
        _logger.Debug("Total rows in related documents: {Count}", totalCount);
        for(int i=1;i<=totalCount;++i)
        {
            var documentType=await _documentType(i).InnerTextAsync();
            var status=await _documentStatus(i).InnerTextAsync();
            _logger.Debug("Row {Index}: Document Type: {Type}, Status: {Status}", i, documentType, status);
            if(documentType=="Invoice")
            {
                await _documentInput(i).CheckAsync();
                _logger.Debug("Checked document input for row {Index}", i);
                await _linkToSelectedBtn.ClickAsync();
                _logger.Debug("Clicked on Link to Selected button");
                await VerifyStatus("linked to invoice");
                _logger.Debug("Verified status: linked to invoice");
                break;
            }
        }
    }

    // Verify Unlink Receipt functionality
    public async Task UnlinkReceipt()
    {
        _logger.Debug("Starting UnlinkReceipt");
        await ClickOnRelatedDocTab();
        var totalCount=await _rowsInRelated.CountAsync();
        _logger.Debug("Total rows in related documents: {Count}", totalCount);
        for(int i=1;i<=totalCount;i++)
        {
            var documentType=await _documentType(i).InnerTextAsync();
            _logger.Debug("Row {Index}: Document Type: {Type}", i, documentType);
            if(documentType=="Invoice")
            {
                await _relateddocButton(i).ClickAsync();
                _logger.Debug("Clicked on related document button for row {Index}", i);
                await VerifyStatus("linked to invoice changed");
                _logger.Debug("Verified status: linked to invoice changed");
                break;
            }
        }        
    }

    // Verify Cancel Selected Document functionality
    public async Task CancelSelectedDoc()
    {
        _logger.Debug("Starting CancelSelectedDoc");
        await ClickOnRelatedDocTab();
        var totalCount=await _rowsInRelated.CountAsync();
        _logger.Debug("Total rows in related documents: {Count}", totalCount);
        for(int i=1;i<=totalCount;++i)
        {
            var status=await _documentStatus(i).InnerTextAsync();
            _logger.Debug("Row {Index}: Status: {Status}", i, status);
            if(status=="In Progress")
            {
                await _documentInput(i).CheckAsync();
                _logger.Debug("Checked document input for row {Index}", i);
                await _cancelSelectedDocumentBtn.ClickAsync();
                _logger.Debug("Clicked on Cancel Selected Document button");
                await _deleteOK.ClickAsync();
                _logger.Debug("Clicked on Delete OK button");
                await VerifyStatus("Cancelled");
                _logger.Debug("Verified status: Cancelled");
                break;
            }
        }        
    }

    //Navigate to the View Receipts page
    public async Task EnterViewRecipts()
    {
        _logger.Debug("Starting EnterViewRecipts");
        await _viewReceiptsBtn.ClickAsync();
        _logger.Debug("Clicked on View Receipts button");
    }

    public async Task CreateBulkReceipts(CommonContext commonContext){
        _logger.Debug("Starting CreateBulkReceipts");
        for(int i=0; i<2;i++)
        {
            _logger.Debug("Creating receipt {Index} with PO number {PONumber}", i+1, commonContext.PoNumbers[i]);
            await _enterReceiptsBtn.ClickAsync();
            _logger.Debug("Clicked on Enter Receipts button");
            if(await IsVisibleAsync(_company,1000,5)){
                _logger.Debug("Company selection is visible");
                await _company.ClickAsync();
                _logger.Debug("Clicked on company dropdown");
                await _companySelect.ClickAsync();
                _logger.Debug("Selected company");
            }
            await SearchPONumber(commonContext.PoNumbers[i]);
            _logger.Debug("Searched for PO number: {PONumber}", commonContext.PoNumbers[i]);
            await NavigatePages();
            _logger.Debug("Navigated pages");
            await CreateReceipt(new List<string>{"0"});
            _logger.Debug("Created receipt for PO: {PONumber}", commonContext.PoNumbers[i]);
        } 
        _logger.Debug("Completed CreateBulkReceipts");
    }
    
    private async Task SelectPos(CommonContext commonContext){
        _logger.Debug("Starting SelectPos");
        Task.Delay(2000);
        var totalCount=await _tablePOColumns.CountAsync();
        _logger.Debug("Total PO columns: {Count}", totalCount);
        bool firstDone=false, secondDone=false;
        for(int i=1;i<=totalCount;++i)
        {
            string po=await _poNumberInGrid(i).InnerTextAsync();
            _logger.Debug("Checking PO at row {Index}: {PONumber}", i, po);
            if(po==commonContext.PoNumbers[0] || po==commonContext.PoNumbers[1])
            {
                if(firstDone){
                    secondDone = true;
                    _logger.Debug("Second PO selected: {PONumber}", po);
                }
                await _poNumberSelect(i).ClickAsync();
                _logger.Debug("Selected PO: {PONumber}", po);
                firstDone=true;
            }
            if(firstDone && secondDone){
                _logger.Debug("Both POs selected, breaking loop");
                break;
            }
        }
        _logger.Debug("Completed SelectPos");
    }

    //verify bulk complete
    public async Task BulkCompleteDocument(CommonContext commonContext)
    {
        _logger.Debug("Starting BulkCompleteDocument");
        await showFilters.WaitForAsync();
        _logger.Debug("Waited for show filters");
        await showFilters.EvaluateAsync("node => node.click()");
        _logger.Debug("Clicked on show filters");
        await showAllReceipts.EvaluateAsync("node => node.click()");
        _logger.Debug("Clicked on show all receipts");
        await SelectPos(commonContext);
        _logger.Debug("Selected POs");
        if(await bulkCompleteDocBtn.IsEnabledAsync()){
            _logger.Debug("Bulk complete document button is enabled");
            await bulkCompleteDocBtn.WaitForAsync();
            await bulkCompleteDocBtn.ClickAsync();
            _logger.Debug("Clicked on bulk complete document button");
        }
        _logger.Debug("Completed BulkCompleteDocument");
    }

    // Verify Bulk Complete Status
    public async Task VerifyBulkCompleteStatus(CommonContext commonContext)
    {
       _logger.Debug("Starting VerifyBulkCompleteStatus");
       await Page.ReloadAsync();
       _logger.Debug("Reloaded page");
       await Page.WaitForLoadStateAsync();
       _logger.Debug("Waited for load state");
       if(await IsVisibleAsync(_company,1000,5)){
            _logger.Debug("Company selection is visible");
            await _company.ClickAsync();
            _logger.Debug("Clicked on company dropdown");
            await _companySelect.ClickAsync();
            _logger.Debug("Selected company");
        } 
       await showFilters.ClickAsync();
       _logger.Debug("Clicked on show filters");
       await showComplete.ClickAsync();
       _logger.Debug("Clicked on show complete");
       Task.Delay(2000);
       await VerifyInvoiceHistoryStatus(commonContext.PoNumbers[0],"Complete",commonContext);
       _logger.Debug("Verified status for PO {PONumber}: Complete", commonContext.PoNumbers[0]);
       await VerifyInvoiceHistoryStatus(commonContext.PoNumbers[1],"Complete",commonContext);
       _logger.Debug("Verified status for PO {PONumber}: Complete", commonContext.PoNumbers[1]);
       _logger.Debug("Completed VerifyBulkCompleteStatus");
    }

    //verify bulk Re-open
    public async Task BulkReopen(CommonContext commonContext)
    {
        _logger.Debug("Starting BulkReopen");
        await clearFiltersBtn.ClickAsync();
        _logger.Debug("Cleared filters");
        await SelectPos(commonContext);
        _logger.Debug("Selected POs");
        if(await bulkReopenBtn.IsEnabledAsync()){
            _logger.Debug("Bulk reopen button is enabled");
            await bulkReopenBtn.WaitForAsync();
            await bulkReopenBtn.ClickAsync();
            _logger.Debug("Clicked on bulk reopen button");
        }
        _logger.Debug("Completed BulkReopen");
    }

    // Verify Bulk Re-Open Status
    public async Task VerifyBulkReopenStatus(CommonContext commonContext)
    {  
       _logger.Debug("Starting VerifyBulkReopenStatus");
       await showFilters.WaitForAsync(); 
       _logger.Debug("Waited for show filters");
       await showFilters.EvaluateAsync("node => node.click()");
       _logger.Debug("Clicked on show filters");
       Task.Delay(2000);
       await showAllReceipts.ClickAsync();
       _logger.Debug("Clicked on show all receipts");
       Task.Delay(2000);
       await VerifyInvoiceHistoryStatus(commonContext.PoNumbers[0],"open",commonContext);
       _logger.Debug("Verified status for PO {PONumber}: open", commonContext.PoNumbers[0]);
       await VerifyInvoiceHistoryStatus(commonContext.PoNumbers[1],"open",commonContext);
       _logger.Debug("Verified status for PO {PONumber}: open", commonContext.PoNumbers[1]);
       _logger.Debug("Completed VerifyBulkReopenStatus");
    }

    //verify bulk Delete
    public async Task BulkDelete(CommonContext commonContext)
    {
        _logger.Debug("Starting BulkDelete");
        await clearFiltersBtn.ClickAsync();
        _logger.Debug("Cleared filters");
        await SelectPos(commonContext);
        _logger.Debug("Selected POs");
        if(await bulkDeleteBtn.IsEnabledAsync()){
            _logger.Debug("Bulk delete button is enabled");
            await bulkDeleteBtn.WaitForAsync();
            await bulkDeleteBtn.ClickAsync();
            _logger.Debug("Clicked on bulk delete button");
            await okConfirmBtn.ClickAsync();
            _logger.Debug("Confirmed deletion");
        }
        _logger.Debug("Completed BulkDelete");
    }

    // Verify Bulk Undelete
    public async Task BulkUndelete(CommonContext commonContext)
    {
        _logger.Debug("Starting BulkUndelete");
        await clearFiltersBtn.ClickAsync();
        _logger.Debug("Cleared filters");
        await showFilters.EvaluateAsync("node => node.click()");
        _logger.Debug("Clicked on show filters");
        Task.Delay(2000);
        await showDeleted.ClickAsync();
        _logger.Debug("Clicked on show deleted");
        Task.Delay(2000);
        await SelectPos(commonContext);
        _logger.Debug("Selected POs");
         
        if(await bulkUndeleteBtn.IsEnabledAsync()){
            _logger.Debug("Bulk undelete button is enabled");
            await bulkUndeleteBtn.WaitForAsync();
            await bulkUndeleteBtn.ClickAsync();
            _logger.Debug("Clicked on bulk undelete button");
            if(await IsVisibleAsync(okConfirmBtn,1000,5)){
                await okConfirmBtn.ClickAsync();
                _logger.Debug("Confirmed undelete");
            }
        }
        _logger.Debug("Completed BulkUndelete");
    }

    //verify invoice history status 
    private async Task VerifyInvoiceHistoryStatus(string invoiceNumbers, string status1, CommonContext commonContext)
    {
        _logger.Debug("Starting VerifyInvoiceHistoryStatus for {InvoiceNumber} with expected status {Status}", invoiceNumbers, status1);
        Task.Delay(2000);
        await _poNumberFilterBtn.ClickAsync();
        _logger.Debug("Clicked on PO number filter button");
        await _filterInput.ClearAsync();
        _logger.Debug("Cleared filter input");
        await _filterInput.FillAsync(invoiceNumbers);
        _logger.Debug("Filled filter input with: {InvoiceNumber}", invoiceNumbers);
        await _filterBtn.ClickAsync();
        _logger.Debug("Clicked on filter button");
        _logger.Debug("Completed VerifyInvoiceHistoryStatus");
    }
    //Verify Bulk Delete Status
    public async Task VerifyBulkDelStatus(CommonContext commonContext)
    {
        _logger.Debug("Starting VerifyBulkDelStatus");
        await showFilters.WaitForAsync();
        _logger.Debug("Show filters button is visible");
        await showFilters.EvaluateAsync("node => node.click()");
        _logger.Debug("Clicked on show filters");
        Task.Delay(2000);
        await showDeleted.ClickAsync();
        _logger.Debug("Clicked on show deleted");
        Task.Delay(2000);
        await VerifyInvoiceHistoryStatus(commonContext.PoNumbers[0],"Deleted",commonContext);
        _logger.Debug("Verified status for first PO number");
        await VerifyInvoiceHistoryStatus(commonContext.PoNumbers[1],"Deleted",commonContext);
        _logger.Debug("Verified status for second PO number");
        _logger.Debug("Completed VerifyBulkDelStatus");
    }

    //Verify Bulk Undelete Status
    public async Task VerifyBulkUndelStatus(CommonContext commonContext)
    {
        _logger.Debug("Starting VerifyBulkUndelStatus");
        await showFilters.WaitForAsync();
        _logger.Debug("Show filters button is visible");
        await showFilters.EvaluateAsync("node => node.click()");
        _logger.Debug("Clicked on show filters");
        Task.Delay(2000);
        await showAllReceipts.ClickAsync();
        _logger.Debug("Clicked on show all receipts");
        Task.Delay(2000);
        await VerifyInvoiceHistoryStatus(commonContext.PoNumbers[0],"In Progress",commonContext);
        _logger.Debug("Verified status for first PO number");
        await VerifyInvoiceHistoryStatus(commonContext.PoNumbers[1],"In Progress",commonContext);
        _logger.Debug("Verified status for second PO number");
        _logger.Debug("Completed VerifyBulkUndelStatus");
    }

    //verify bulk cancel
    public async Task BulkCancelPO(CommonContext commonContext)
    {
        _logger.Debug("Starting BulkCancelPO");
        //await showFilters.WaitForAsync();
        await clearFiltersBtn.ClickAsync();
        _logger.Debug("Cleared filters");
        Task.Delay(2000);
        await showFilters.EvaluateAsync("node => node.click()");
        _logger.Debug("Clicked on show filters");
        Task.Delay(2000);
        await showAllReceipts.ClickAsync();
        _logger.Debug("Clicked on show all receipts");
        await SelectPos(commonContext);
        _logger.Debug("Selected POs");
        if(await bulkCancelPOBtn.IsEnabledAsync()){
            _logger.Debug("Bulk cancel PO button is enabled");
            await bulkCancelPOBtn.WaitForAsync();
            await bulkCancelPOBtn.ClickAsync();
            _logger.Debug("Clicked on bulk cancel PO button");
            if(await IsVisibleAsync(okConfirmBtn,1000,5)){
                _logger.Debug("Confirmation dialog appeared");
                await okConfirmBtn.ClickAsync();
                _logger.Debug("Confirmed cancellation");
            }
        }
        _logger.Debug("Completed BulkCancelPO");
    }

    // Verify Bulk Cancel Status
    public async Task VerifyBulkCancelStatus(CommonContext commonContext)
    {
        _logger.Debug("Starting VerifyBulkCancelStatus");
        await showFilters.WaitForAsync();
        _logger.Debug("Show filters button is visible");
        await showFilters.EvaluateAsync("node => node.click()");
        _logger.Debug("Clicked on show filters");
        Task.Delay(2000);
        await showAllReceipts.ClickAsync();
        _logger.Debug("Clicked on show all receipts");
        await VerifyInvoiceHistoryStatus(commonContext.PoNumbers[0],"PO closed",commonContext);
        _logger.Debug("Verified status for first PO number");
        await VerifyInvoiceHistoryStatus(commonContext.PoNumbers[1],"PO closed",commonContext);
        _logger.Debug("Verified status for second PO number");
        _logger.Debug("Completed VerifyBulkCancelStatus");
    }

    public async Task TableGridColumns(string column)
    {
        _logger.Debug("Starting TableGridColumns with column: {Column}", column);
        await this.VerifyTitle(column);
        _logger.Debug("Completed TableGridColumns");
    }

    //Verify Export to Excel
    public async Task VerifyExportToExcel(ArrayList excelTableTitles)
    {
        _logger.Debug("Starting VerifyExportToExcel");
        await exportToExcel.ClickAsync();
        _logger.Debug("Clicked on export to Excel button");
        var waitForDownloadTask = Page.WaitForDownloadAsync();
        _logger.Debug("Waiting for download");
        await currentPage.ClickAsync();
        _logger.Debug("Clicked on current page");
        var download = await waitForDownloadTask;
        _logger.Debug("Download completed");
        string userHome = System.AppDomain.CurrentDomain.BaseDirectory;
        string viewRecipts= userHome + "/"+ download.SuggestedFilename;
        _logger.Debug("Saving file to: {FilePath}", viewRecipts);
        await download.SaveAsAsync(viewRecipts);
#pragma warning disable CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
        VerifyExportToExcel(excelTableTitles,viewRecipts);
#pragma warning restore CS4014 // Because this call is not awaited, execution of the current method continues before the call is completed
        _logger.Debug("Completed VerifyExportToExcel");
    }

    //verify column filters
    public async Task VerifyFilters()
    {
        _logger.Debug("Starting VerifyFilters");
        int columnCount = await Page.FrameLocator("#v4Container").Locator(".k-grid-table thead th").CountAsync();
        _logger.Debug("Found {ColumnCount} columns", columnCount);
        for (int i=3; i <= columnCount; ++i)
        {
            _logger.Debug("Processing column {ColumnIndex}", i);
            var cells = await Page.FrameLocator("#v4Container").Locator(".k-grid-table tr td:nth-child("+i+")").ElementHandlesAsync();
            _logger.Debug("Found {CellCount} cells in column {ColumnIndex}", cells.Count, i);
            foreach (var cell in cells)
            {
                // Get the text of the cell
                string cellText = await cell.InnerTextAsync();
                // If the cell's text is not empty, perform the filtering operation
                if (!string.IsNullOrEmpty(cellText))
                {
                    _logger.Debug("Filtering by cell text: {CellText}", cellText);
                    await Page.FrameLocator("#v4Container").Locator(".k-widget.k-grid th:nth-child("+i+") span.k-cell-inner div span").ClickAsync();
                    _logger.Debug("Clicked on column header filter");
                    await _filterInput.FillAsync(cellText);
                    _logger.Debug("Filled filter input with: {CellText}", cellText);
                    await _filterBtn.ClickAsync();
                    _logger.Debug("Applied filter");
                }
            }
        }
        _logger.Debug("Completed VerifyFilters");
    }

    //Verify Preset Filters
    public async Task VerifyPresetFilters()
    {
        _logger.Debug("Starting VerifyPresetFilters");
        await VerifyPresetFilterDropdown("Show All Receipts");
        await VerifyPresetFilterDropdown("Show Qty-Variance < 100%");
        await VerifyPresetFilterDropdown("Show Qty-Variance >= 100%");
        await VerifyPresetFilterDropdown("Show Complete");
        await VerifyPresetFilterDropdown("Show Deleted");
        await VerifyPresetFilterDropdown("Show Pending Receipt Goods");
        _logger.Debug("Completed VerifyPresetFilters");
    }

    private async Task VerifyPresetFilterDropdown(string dropdownOption)
    {
        _logger.Debug("Starting VerifyPresetFilterDropdown with option: {Option}", dropdownOption);
        await presetFilters.ClickAsync();
        _logger.Debug("Clicked on preset filters dropdown");
        var option= Page.FrameLocator("#v4Container").Locator($"text={dropdownOption}");
        await option.ClickAsync();
        _logger.Debug("Selected option: {Option}", dropdownOption);
        // Get the table data
        var rows = Page.FrameLocator("#v4Container").Locator(".k-grid-table tr");
        var rowCount = await rows.CountAsync();
        _logger.Debug("Found {RowCount} rows after applying filter", rowCount);
        for (int i = 0; i < rowCount; i++)
        {
            var rowText = await rows.Nth(i).InnerTextAsync();
            _logger.Debug("Row {RowIndex} text: {RowText}", i, rowText);
        }
        _logger.Debug("Completed VerifyPresetFilterDropdown for option: {Option}", dropdownOption);
    }
}
