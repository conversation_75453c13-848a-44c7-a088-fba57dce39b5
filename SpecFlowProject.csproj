<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <RunSettingsFilePath>./.runsettings</RunSettingsFilePath>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <PublishWithAspNetCoreTargetManifest>false</PublishWithAspNetCoreTargetManifest>
     <SuppressTfmSupportBuildWarnings>true</SuppressTfmSupportBuildWarnings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.104.1" />
    <PackageReference Include="FreeSpire.PDF" Version="10.1.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.5.0" />
    <PackageReference Include="Microsoft.Playwright.NUnit" Version="1.38.0" />
    <PackageReference Include="NPOI" Version="2.7.2" />
    <PackageReference Include="SpecFlow" Version="3.9.74" />
    <PackageReference Include="SpecFlow.NUnit" Version="3.9.74" />
    <PackageReference Include="SpecFlow.Plus.LivingDocPlugin" Version="3.9.57" />
    <PackageReference Include="NUnit" Version="3.13.3" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.4.2" />
    <PackageReference Include="NUnit.Analyzers" Version="3.6.1" />
    <PackageReference Include="coverlet.collector" Version="3.2.0" />
    <PackageReference Include="Complex.Specflow.Assist" Version="2.0.0" />
    <PackageReference Include="SSH.NET" Version="2024.2.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.4.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="3.1.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="2.3.0" />
    <PackageReference Include="ExtentReports" Version="4.1.0" />
    <PackageReference Include="ExtentReports.Core" Version="1.0.3" />
  </ItemGroup>
    <ItemGroup>
       <Content Include=".\UploadFiles\**">
           <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
       </Content>
       <Content Include=".\auth\**">
           <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
       </Content>
       <Content Include="Features\*.feature" />
  </ItemGroup>

</Project>
