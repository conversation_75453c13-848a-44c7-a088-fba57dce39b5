using Microsoft.Playwright;
using SpecFlowProject.BusinessObjects;
using System.Globalization;
using System.Reflection.Metadata.Ecma335;
namespace SpecFlowProject.Pom.Pages;

public class ActionRequiredPage: Base
{
    public ActionRequiredPage(IPage page) : base(page)
    {

    }

    private ILocator presetFilters => Page.FrameLocator("#v4Container").Locator("#action-required-grid-dropdown span span");

    private ILocator actionRequiredInvoice => Page.FrameLocator("#v4Container").Locator(".actionRequiredGrid > .k-grid-header thead th:nth-child(3) .k-icon");

    private ILocator glSummaryGrid=> Page.FrameLocator("#v4Container").Locator(".glSummaryGrid > .k-grid-container tbody tr");

    private ILocator glSummaryIcon=> Page.FrameLocator("#v4Container").Locator(".actionRequiredGrid > .k-grid-container tbody tr td:nth-child(11)>div");

    private ILocator glSummarypopup => Page.FrameLocator("#v4Container").Locator(".k-window-content .k-grid-container tr");

    private ILocator glSummaryexpansion => Page.FrameLocator("#v4Container").Locator(".k-window-content .k-grid-header th:nth-child(1) svg");

    private ILocator glSummaryData=> Page.FrameLocator("#v4Container").Locator(".actionRequiredGrid > .k-grid-container tbody tr:nth-child(1)");
    public async Task VerifyGlSummery(CommonContext commonContext)
    {
        await glSummaryIcon.ClickAsync();
        var multi = commonContext.HasMulti;
        foreach(var row in await glSummarypopup.AllAsync()){
            var glCode = await row.Locator("td:nth-child(1)").TextContentAsync()??"";
            glCode = glCode.Trim();
            if(multi){
                await glSummaryexpansion.ClickAsync();
                foreach(var pds in await row.Locator(".k-grid-table tr").AllAsync()){
                    ItemDetails? value =null;
                    var periodId = await row.Locator("td:nth-child(3)").TextContentAsync()??"";
                    if(commonContext.SendForApproval.TryGetValue(glCode+"#"+periodId, out value)){
                        var ams = GetAmount(await row.Locator("td:nth-child(5)").TextContentAsync()??"0");
                        Assert.Equals(ams,value.getTotalAmount());
                    }
                }
            }else{
                ItemDetails? it = null;
                foreach(KeyValuePair<string, ItemDetails> entry in commonContext.SendForApproval){
                    string gcode = entry.Key.Split("#")[0];
                    if(gcode==glCode){
                        it = entry.Value;
                        break;
                    }
                }
                if(it!=null){
                    var amt = GetAmount(await row.Locator("td:nth-child(4)").TextContentAsync()??"0");
                    Assert.That(Math.Round(amt, 2),Is.EqualTo(Math.Round(it.getTotalAmount(), 2)));
                }
            }

        }
        await _closeBtnX.ClickAsync();
    }
    public async Task VerifyGlCodesTrLog(CommonContext commonContext){
        foreach(var row in await glSummaryGrid.AllAsync()){
            var glCode = await row.Locator("td:nth-child(1)").TextContentAsync()??"";
            glCode = glCode.Trim();
            var period = await row.Locator("td:nth-child(3)").TextContentAsync()??"";
            period = period.Trim();
            var smallp = period.Split("-");
            period = smallp[0]+" - "+smallp[1];
            var r = commonContext.SendForApproval.Keys.Where(p=>{
                var q = p.Split("#");
                if(q.Length == 2){
                    return q[0] == glCode && q[1].Contains(period);
                }else{
                    return q[0] == glCode;
                }
            }).ToList();
            //Assert.IsTrue(r.Count==1,"");
            await row.Locator("td:nth-child(4) button").ClickAsync();
            await _closePopupBtn.ClickAsync();
        }
    }

    public async Task FilterInvoice(CommonContext commonContext){
        await glSummaryData.WaitForAsync();
        await actionRequiredInvoice.ClickAsync();
        await Page.WaitForTimeoutAsync(1000);
        await _filterInput.FillAsync(commonContext.PoNumber+"-0");
        await _filterBtn.ClickAsync();
    }
    //Navigate to the Action Required Page
    public async Task OpenActionRequiredPage(){

        await _actionRequiredLink.ClickAsync();
        await _closeBtnX.ClickAsync();
    }

    //Verifying the action recquired grid columns titles
    public async Task ActionRequiredGridColumnsTitles(string column)
    {
        await this.VerifyTitle(column,true);
    }

    //Verifying the action required table grid preset filters
    public async Task VerifyPresetFilters()
    {
        await VerifyPresetFilterDropdown("Show All");
        await VerifyPresetFilterDropdown("Show Only Force Invoices");
        await VerifyPresetFilterDropdown("Show Only Over Spending Limit");
        await VerifyPresetFilterDropdown("Show Only Over Budget");
        await VerifyPresetFilterDropdown("Show Only Not in a Current Budget Period");
        await VerifyPresetFilterDropdown("Show Only Buyer Notes");
        await VerifyPresetFilterDropdown("Show Only Invoices");
        await VerifyPresetFilterDropdown("Show Only Credits");
        await VerifyPresetFilterDropdown("Show Only Qty Received < 100%");
        await VerifyPresetFilterDropdown("Show Only Qty Received >= 100%");
    }

    private async Task VerifyPresetFilterDropdown(string dropdownOption)
    {
        await presetFilters.ClickAsync();
        var option= Page.FrameLocator("#v4Container").Locator($"span.k-list-item-text:text('{dropdownOption}')");
        await option.ClickAsync();
    }
}