@OCR
Feature: Verify Auto Set GL Code functionality

@AutoSetGLCodeFromOwnedFolder
Scenario Outline:Verify OCR Auto Set GL Code functionality from Owned Folder
    Given Open the Web url
    Then Login to site with username and password
    Then HomePage Loaded
    Then Click on Favorites Products link
    Then Create Buyers Owned Folder
    Then Create Po for supplier
    Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | |
    Then Add Products in to the Owned Folder
    Then I update the PDF ITEM# field with the CommonContext item number
    Then Expand InvoiceManager
    Then Open ScanmanagerInvoice page
    Then Select Company
    Then upload document for Auto Set GL Code
    Then Filter the Uploaded Auto Set GL Code document
    Then Click Needs Review Button
    Then Select Supplier
    Then Verify GL Code from the Favourites

@AutoSetGLCodeFromSharedFolder
Scenario Outline:Verify OCR Auto Set GL Code functionality from Shared Folder
    Given Open the Web url
    Then Login to site with username and password
    Then HomePage Loaded
    Then Click on Favorites Products link
    Then Create Shared Folder
    Then Create Po for supplier
    Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | |
    Then Add Products in to the Shared Folder
    Then I update the PDF ITEM# field with the CommonContext item number
    Then Expand InvoiceManager
    Then Open ScanmanagerInvoice page
    Then Select Company
    Then upload document for Auto Set GL Code
    Then Filter the Uploaded Auto Set GL Code document
    Then Click Needs Review Button
    Then Select Supplier
    Then Verify GL Code from the Favourites

@AutoSetGLCodeFromAcceptedPO
Scenario Outline:Verify OCR Auto Set GL Code functionality from Accepted PO
    Given Open the Web url
    Then Login to site with username and password
    Then HomePage Loaded
    Then Open checkbook with return
	Then Create Po for supplier
	Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
		| quantities | amounts | UOM | weights | glType |
		| 1          | 1002    |     | 5       | Exempt |
	Then Submit and Approve PO.
    Then I update the PDF ITEM# field with the CommonContext item number
    Then Expand InvoiceManager
    Then Open ScanmanagerInvoice page
    Then Select Company
    Then upload document for Auto Set GL Code
    Then Filter the Uploaded Auto Set GL Code document
    Then Click Needs Review Button
    Then Enter PO Number
    Then Verify GL Code from the Favourites

@MergeOCRLineItems
Scenario Outline:Verify Merge OCR Line Items functionality
    Given Open the Web url
    Then Login to site with username and password
    Then HomePage Loaded
    Then Open checkbook with return
	Then Create Po for supplier
	Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
		| quantities | amounts | UOM | weights | glType |
		| 1          | 1002    |     | 5       | Exempt |
	Then Submit and Approve PO.
    Then I update the PDF two ITEM# field with the CommonContext item number
    Then Expand InvoiceManager
    Then Open ScanmanagerInvoice page
    Then Select Company
    Then upload document for Auto Set GL Code
    Then Filter the Uploaded Auto Set GL Code document
    Then Click Needs Review Button
    Then Verify Quantity

@VerifyEmptyUOM
Scenario Outline:Verify Empty UOM functionality
    Given Open the Web url
    Then Login to site with username and password
    Then HomePage Loaded
    Then Open checkbook with return
	Then Create Po for supplier
	Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
		| quantities | amounts | UOM | weights | glType |
		| 1          | 1       |  EA   | 5       | Exempt |
    Then Submit and Approve PO.
    Then I update the PDF two ITEM# field with the CommonContext item number and UOM Empty
    Then Expand InvoiceManager
    Then Open ScanmanagerInvoice page
    Then Select Company
    Then upload document for Auto Set GL Code
    Then Filter the Uploaded Auto Set GL Code document
    Then Click Needs Review Button
    Then Verify UOM

@ChangePOAssignmentAutoSet
Scenario Outline:Verify Change PO Assignment functionality
    Given Open the Web url
    Then Login to site with username and password
    Then HomePage Loaded
    Then Open checkbook with return
    Then Create Po for supplier
    Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | |
    Then Submit and Approve PO.
    Then Create Po for supplier
    Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | |
    Then Submit and Approve PO.
    Then I update the PDF ITEM# field with the CommonContext item number
    Then Expand InvoiceManager
    Then Open ScanmanagerInvoice page
    Then Select Company
    Then upload document for Auto Set GL Code
    Then Filter the Uploaded Auto Set GL Code document
    Then Click Needs Review Button
    Then Verify GL Code from the Favourites
    Then Verify Cancelled Invoice Line Item present in the New Invoice Line Items



    
