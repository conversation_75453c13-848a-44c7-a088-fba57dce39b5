using TechTalk.SpecFlow;
using SpecFlowProject.Drivers;
using TechTalk.SpecFlow.Infrastructure;
using Microsoft.Playwright;
using SpecFlowProject.Utils;
using System.Threading.Tasks;
using System;

[assembly: Parallelizable(ParallelScope.Fixtures)]
namespace SpecFlowProject.Hooks
{
    [Binding]
    public class Hook
    {
        public Hook(Context context, ISpecFlowOutputHelper specFlowOutputHelper){
            _context = context;
            _specFlowOutputHelper = specFlowOutputHelper;
        }
        readonly Context _context;
        readonly PlayWrightDriver playwright=new();

        private readonly ISpecFlowOutputHelper _specFlowOutputHelper;


        [BeforeScenario]
        public async Task BeforeScenario(ScenarioContext scenarioContext, FeatureContext featureContext)
        {
            // Check if this is a retry attempt for the scenario
            if (ScenarioRetryManager.ShouldRetryScenario(scenarioContext))
            {
                var scenarioRetryState = ScenarioRetryManager.GetRetryState(scenarioContext);
                
                if (scenarioRetryState.Attempts > 0)
                {
                    _context.LogInfo("Retry attempt {Attempt} for scenario: {ScenarioTitle}", 
                        scenarioRetryState.Attempts, scenarioContext.ScenarioInfo.Title);
                }
            }
            
            _context.LogInfo("Starting scenario: {ScenarioTitle}", scenarioContext.ScenarioInfo.Title);
            _context.StartTestTimer();
            _context.TestName = scenarioContext.ScenarioInfo.Title;
            _context.LogInfo("Initializing Playwright");
            await playwright.CreatePlaywright();
            _context.LogInfo("Creating browser and page with browser type: {BrowserType}", TestContext.Parameters["Browser"]!);
            _context.Page = await playwright.CreateBrowserAndPage(TestContext.Parameters["Browser"]!);
            _context.LogInfo("Setting default timeout to 100000ms");
            _context.Page.SetDefaultTimeout(100000);
            
            if(bool.Parse(TestContext.Parameters["StartTracing"]!)){
                _context.LogInfo("Starting tracing with screenshots, snapshots, and sources");
                await _context.Page.Context.Tracing.StartAsync(new() { Screenshots = true, Snapshots = true, Sources = true });
            }
            
            _context.LogInfo("Browser initialization complete");
        }

        [AfterScenario]
        public async Task AfterScenario(ScenarioContext scenarioContext, FeatureContext featureContext)
        {
            long testDuration = _context.StopTestTimer();
            bool testPassed = scenarioContext.TestError == null;
            _context.LogInfo("Finishing scenario: {ScenarioTitle}. Test {TestResult} in {Duration}ms", 
                scenarioContext.ScenarioInfo.Title, 
                testPassed ? "PASSED" : "FAILED", 
                testDuration);
            
            // Update scenario retry state if the scenario failed and has the retry tag
            if (!testPassed && ScenarioRetryManager.ShouldRetryScenario(scenarioContext))
            {
                var scenarioRetryState = ScenarioRetryManager.GetRetryState(scenarioContext);
                scenarioRetryState.HasFailed = true;
                scenarioRetryState.LastException = scenarioContext.TestError;
                _context.LogWarning("Scenario failed with retry tag: {ScenarioTitle}", 
                    scenarioContext.ScenarioInfo.Title);
            }
            
            try{
                _context.LogDebug("Checking for proxy user removal");
                if (_context != null && _context.Page != null && await _context.Page.Locator("a[href='/Account/RemoveProxyUser']").CountAsync()>0){
                    _context.LogInfo("Removing proxy user");
                    await _context.Page.Locator("a[href='/Account/RemoveProxyUser']").ClickAsync();
                    await _context.Page.Locator("button:has-text('Not Now')").ClickAsync();
                }
                
                var tracing = bool.Parse(TestContext.Parameters["StartTracing"]!);
                var screenshts = bool.Parse(TestContext.Parameters["StoreScreenshots"]!);
                
                if (scenarioContext.TestError != null)
                {
                    _context.LogError(scenarioContext.TestError, "Test failed with error: {ErrorMessage}", scenarioContext.TestError.Message);
                    string testTitle = scenarioContext.ScenarioInfo.Title.Replace(">", "");
                    
                    if(screenshts){
                        _context.LogInfo("Taking screenshot for failed test");
                        Directory.CreateDirectory("Screenshots");
                        string title1 = testTitle.Replace(" ","_");
                        if (_context != null && _context.Page != null)
                        {
                            var screenshot = await _context.Page.ScreenshotAsync(new()
                            {
                                Path = "Screenshots/"+title1+".png"
                            });
                            _context.LogInfo("Screenshot saved to: Screenshots/{FileName}.png", title1);
                            _specFlowOutputHelper.AddAttachment("Screenshots/"+title1+".png");
                        }
                    }
                    
                    _specFlowOutputHelper.WriteLine(scenarioContext.TestError.StackTrace);
                    
                    if(tracing && _context != null && _context.Page != null){
                        _context.LogInfo("Stopping tracing and saving trace file");
                        await _context.Page!.Context.Tracing.StopAsync(new() { Path = $"TestResults/{testTitle}.zip" });
                        TestContext.AddTestAttachment(@"TestResults\" + scenarioContext.ScenarioInfo.Title.Replace(">", "") + ".zip");
                    }
                }
                else
                {
                    if(tracing && _context != null && _context.Page != null){
                        _context.LogInfo("Stopping tracing without saving trace file");
                        await _context.Page!.Context.Tracing.StopAsync();
                    }
                }
                
                if(_context != null && _context.Page != null && await _context.Page.Locator("a[href='/Account/LogOff']").CountAsync()>0){
                    _context.LogInfo("Logging off user");
                    await _context.Page.Locator("a[href='/Account/LogOff']").ClickAsync();
                }
                
                // if(TestContext.Parameters["EnableAzure"]=="true"){
                //     await azure.SendScenarioStepsToAzure(scenarioAllSteps, scenarioContext);
                // }
                
                if(_context != null && _context.Page != null){
                    _context.LogInfo("Closing browser page");
                    await _context.Page.CloseAsync();
                }
                
                _context.LogInfo("Disposing Playwright resources");
                playwright.Dispose();
            }catch(Exception ex){
                _context.LogError(ex, "Error during test cleanup: {ErrorMessage}", ex.Message);
                if(_context != null && _context.Page != null){
                    _context.LogInfo("Closing browser page after error");
                    await _context.Page.CloseAsync();
                }
                playwright.Dispose();
            }
        }
        
        [AfterTestRun]
        public static async Task AfterTest(){
            LoggerService.Instance.Information("Test run completed, cleaning up resources");
            await Task.Run(() =>{
                string path = @".\\auth\\glcodes.txt"; 
                LoggerService.Instance.Debug("Deleting file: {FilePath}", path);
                File.Delete(path);
                LoggerService.Instance.Information("Cleanup completed");
            });
            
            // Reset all retry states
            ScenarioRetryManager.ResetAllRetryStates();
        }
        
        [AfterScenario("retry")]
        public static void AfterScenarioWithRetry(ScenarioContext scenarioContext)
        {
            // Check if the scenario has failed
            if (scenarioContext.TestError != null)
            {
                var retryState = ScenarioRetryManager.GetRetryState(scenarioContext);
                
                if (retryState.HasFailed)
                {
                    int maxRetries = ScenarioRetryManager.GetMaxRetries(scenarioContext);
                    
                    // If we haven't reached the maximum number of retries, retry the scenario
                    if (retryState.Attempts < maxRetries)
                    {
                        retryState.IncrementAttempts();
                        LoggerService.Instance.Warning("Scenario {ScenarioTitle} failed. Retrying... (Attempt {Attempt}/{MaxRetries})", 
                            scenarioContext.ScenarioInfo.Title, retryState.Attempts, maxRetries);
                        
                        // The actual retry is handled by the ScenarioRetryAttribute
                    }
                    else
                    {
                        LoggerService.Instance.Error("Scenario {ScenarioTitle} failed after {MaxRetries} attempts", 
                            scenarioContext.ScenarioInfo.Title, maxRetries);
                    }
                }
            }
        }
    }
}
