using TechTalk.SpecFlow;
using SpecFlowProject.Pom.Pages;
using SpecFlowProject.Hooks;
using System.Collections;
using SpecFlowProject.BusinessObjects;
using SpecFlowProject.Utils;

namespace SpecFlowProject.Steps
{
    [Binding]
    public class ExportSteps
    {
        readonly Context _context;
        readonly ExportPage _exportPage;
        private readonly LoggerService _logger;
        private ScenarioContext _scenarioContext;

        public ExportSteps(Context context,ScenarioContext scenarioContext)
        {
            _context = context;
            _exportPage = new ExportPage(_context.Page!);
            _scenarioContext = scenarioContext;
            _logger = LoggerService.Instance;
            _logger.Debug("ExportSteps initialized");
        }
        [Then(@"Click on Export button and Export the file for include budget amt")]
        public async Task Exportfun()
        {
            _logger.Information("Exporting file for include budget amount");
            await _exportPage.Exportfun1();
            _logger.Debug("Export completed for include budget amount");
        }
        private async Task VerifyExcel(Table table){
            _logger.Debug("Verifying Excel file with table data");
            ArrayList myList = new ArrayList(); 
            foreach(TableRow row in table.Rows){
               string column = row["columns"];
               _logger.Debug("Adding column to verification list: {Column}", column);
               myList.Add(column);
            }
            _logger.Debug("Downloading Excel file and verifying {Count} columns", myList.Count);
            await _exportPage.DownloadExcelandTxt(myList);
            _logger.Debug("Excel verification completed");
        }
        private async Task Verifytxt(Table table){
            _logger.Debug("Verifying text file with table data");
            ArrayList myList = new ArrayList(); 
            foreach(TableRow row in table.Rows){
               string column = row["columns"];
               _logger.Debug("Adding column to verification list: {Column}", column);
               myList.Add(column);
            }
            _logger.Debug("Downloading text file and verifying {Count} columns", myList.Count);
            await _exportPage.DownloadExcelandTxt(myList);
            _logger.Debug("Text file verification completed");
        }
        [Then(@"Close Side Pane")]
        public async Task CloseSidePane(){
            _logger.Debug("Closing side pane");
            await _exportPage.CloseSidePane();
            _logger.Debug("Side pane closed");
        }
        [Then(@"Verify Column titles of txt file")]
        public async Task DownLoadTxt(Table table)
        {
            _logger.Information("Verifying column titles of text file");
            await Verifytxt(table);
            _logger.Information("Text file column titles verified");
        }
        [Then(@"Verify Column titles of Excel file")]
        public async Task DownLoadExcel(Table table)
        {
            _logger.Information("Verifying column titles of Excel file");
            await VerifyExcel(table);
            _logger.Information("Excel file column titles verified");
        }
        [Then(@"Click on Export button and Export the file for Forecast Amount")]
        public async Task ExportAllpropwithforecastamt()
        {
            _logger.Information("Exporting file for forecast amount");
            await _exportPage.ExportAllpropinforecastamt();
            _logger.Debug("Export completed for forecast amount");
        }
        [Then(@"Click on Export button, select single tab and Export the file for include budget amt")]
        public async Task ExportAllpropsingletabwithincludebugdetamt()
        {
            _logger.Information("Exporting file for single tab with include budget amount");
            await _exportPage.Exportsingletabforincludebudgetamt();
            _logger.Debug("Export completed for single tab with include budget amount");
        }
        [Then(@"Click on Export button, select singletab and Export the file for Forecast Amount")]
        public async Task ExportAllpropsingletabwithincludeforecastamt()
        {
            _logger.Information("Exporting file for single tab with forecast amount");
            await _exportPage.Exportsingletabforincludeforecastamt();
            _logger.Debug("Export completed for single tab with forecast amount");
        }
        [Then(@"Click on Export button,select single company and Export the file for include budget amt")]
        public async Task Exportsinglecomforincludebudgetamt()
        {
            await _exportPage.Exportsinglecomwithincludebudgetamt();
        }
        [Then(@"Click on Export button, select singlecomp and Export the file for Forecast Amount")]
        public async Task Exportsinglecompwithincludeforecastamt()
        {
            await _exportPage.Exportsinglecompwithincludeforecastamt();
        }
        [Then(@"Click on Export button, select txt tab and Export the file for include budget amt")]
        public async Task Exporttxtabwithincludebudgetamt()
        {
            await _exportPage.Exporttxtabwithincludebudgetamt();
        }
        [Then(@"Click on Export button, select txt tab and Export the file for include forecast")]
        public async Task Exporttxttabwithincludeforecastamt()
        {
            await _exportPage.Exporttxttabinincludeforecastamt();
        }
        [Then(@"Click on Export button,single comp, select txt tab and Export the file for include budget amt")]
        public async Task Exportsinglecomptxttabwithincludebudgetamt()
        {
            await _exportPage.Exportsubcomptxttabwithincludebudgetamt();
        }
        [Then(@"Click on Export button, select txt tab, All GL codes and Export the file for include forecast amt")]
        public async Task Exportsinglecomptxttabwithincludeforecastamt()
        {
            await _exportPage.Exportsubcomptxttabwithincludeforecastamt();
        }
        [Then(@"Click on Export button and Export the file for superadmin")]
        public async Task Exportsuperadmin()
        {
            await _exportPage.Exportsuperadminfun();
        }
        [Then(@"Click on Export button,select include forecast amount and Export the file for superadmin")]
        public async Task Exportsuperadmin1()
        {
            await _exportPage.Exportsuperadminfun1();
        }
        [Then(@"Click on Export button, select single tab and Export the file for superadmin")]
        public async Task Exportsingletabadmin()
        {
            await _exportPage.Exportsingletabsuperadmin();
        }
        [Then(@"Click on Export button,select single tab,select include forecast amount and Export the file for superadmin")]
        public async Task Exportsingletabsuperadminincludeforecastamt()
        {
            await _exportPage.Exportsingletabsuperadminincforecastamt();
        }
        [Then(@"Click on Export button, select single company and Export the file for superadmin")]
        public async Task Exportsinglecompsuperadminincludebudgetamt()
        {
            await _exportPage.Exportsinglecompsuperadminincludebudgetamt();
        }
        [Then(@"Click on Export button,select single comp,select include forecast amount and Export the file for superadmin")]
        public async Task Exportsinglecompsuperadminincludeforecastamt()
        {
            await _exportPage.Exportsinglecompsuperadminincludeforecastamt();
        }
        [Then(@"Click on Export button, select single company,select all gl codes and Export the file for superadmin")]
        public async Task Exportsinglecompallglcodessuperadminincludebudgetamt()
        {
            await _exportPage.Exportsinglecompallglcodessuperadminincludebudgetamt();
        }
        [Then(@"Click on Export button,select single comp, select All GL Codes, select include forecast amount and Export the file for superadmin")]
        public async Task Exportsinglecompallglcodessuperadminincludeforecastamt()
        {
            await _exportPage.Exportsinglecompallglcodessuperadminincludeforecastamt();
        }
        [Then(@"Click on Export button, select txt tab and Export the file for superadmin")]
        public async Task Exportfuncwithtxtabincludebudgetamt()
        {
            await _exportPage.Exportfuncfromtxttabincludebudgetamt();
        }
        [Then(@"Click on Export button,select txttab, select All GL Codes, select include forecast amount and Export the file for superadmin")]
        public async Task Exportfuncwithtxttabincludeforecastamt()
        {
            await _exportPage.Exportfuncfromtxttabincludeforecastamt();
        }
        [Then(@"Click on Export button, select single company, txt tab, All GL Codes and Export the file for superadmin")]
        public async Task Exportfuncwithsinglecomptxtabincludebudgetamt()
        {
            await _exportPage.Exportfuncwithsinglecomptxtabincludebudgetamt();
        }
        [Then(@"Click on Export button,select single company, select txttab, select All GL Codes and Export the file for superadmin")]
        public async Task Exportfunctionwithsinglecompanytxttabincludeforecastamt()
        {
            await _exportPage.Exportfuncwithsinglecomptxttabincludeforecastamtinsuperadmin();
        }

    }
}
