using TechTalk.SpecFlow;
using SpecFlowProject.Pom.Pages;
using SpecFlowProject.Hooks;
using System.Collections;
using SpecFlowProject.BusinessObjects;
using TechTalk.SpecFlow.Infrastructure;
using SpecFlowProject.Utils;

namespace SpecFlowProject.Steps
{
    [Binding]
    public class CheckbookSteps
    {
        readonly Context _context;
        readonly CheckbookPage _checkbookPage;

        private ScenarioContext _scenarioContext;
        private readonly ISpecFlowOutputHelper _specFlowOutputHelper;
        private readonly LoggerService _logger;

        public CheckbookSteps(Context context,ScenarioContext scenarioContext, ISpecFlowOutputHelper specFlowOutputHelper)
        {
            _context = context;
            _checkbookPage = new CheckbookPage(_context.Page!);
            _scenarioContext = scenarioContext;
            _specFlowOutputHelper = specFlowOutputHelper;
            _logger = LoggerService.Instance;
            _logger.Information("CheckbookSteps initialized");
        }
        [Then(@"Open checkbook")]
        public async Task OpenCheckbook()
        {
            _logger.Information("Opening checkbook");
            await _checkbookPage.ExpandAndOpenCheckBook((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Checkbook opened successfully");
        }

        [Then(@"Open checkbook no checks")]
        public async Task OpenCheckbookNoChecks()
        {
            _logger.Information("Opening checkbook with no checks");
            await _checkbookPage.OpenCheckbookNoChecks((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Checkbook with no checks opened successfully");
        }


        [Then(@"Open checkbook and filter for GL code where status is ""([^""]*)""")]
        public async Task ThenOpenCheckbookAndFilterForGLCodeWhereStatusIsAndSetForcastAmountToLesthanZero(string exempt)
        {
            _logger.Information("Opening checkbook and filtering for GL code with status: {Status}", exempt);
            await _checkbookPage.OpenCheckbookandreturnExmptGLcodes((CommonContext)_scenarioContext["commonContext"], GLstatus: exempt);
            //await _checkbookPage.SetupForecastAmountToLessThanZero();
            await _checkbookPage.CaptureGLCode((CommonContext)_scenarioContext["commonContext"], _specFlowOutputHelper);
            _logger.Information("Checkbook opened and GL code filtered successfully");
            await _checkbookPage.ClickSelectAllColumnsCheckbox();
        }

        // [Then(@"click checkbox Select All columns in the column dropdown")]
        // public async Task ThenClickCheckboxSelectAllColumnsInTheColumnDropdown()
        // {
        //     _logger.Information("Clicking Select All columns checkbox in the column dropdown");
        //     await _checkbookPage.ClickSelectAllColumnsCheckbox();
        //     _logger.Information("Select All columns checkbox clicked successfully");
        // }


        [Then(@"Open checkbook with return")]
        public async Task OpenCheckbookWithRerun()
        {   
            _logger.Information("Opening checkbook with return");
            await _checkbookPage.ExpandAndOpenCheckBookReturn((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Checkbook with return opened successfully");
        }
        [Then(@"Open checkbook With GlMasks")]
        public async Task OpenCheckbookWithMasks()
        {
            _logger.Information("Opening checkbook with GL masks");
            await _checkbookPage.ExpandAndOpenCheckBookWithGlMasks((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Checkbook with GL masks opened successfully");
        }
        

        [Then(@"Select Department and ShowAll Filter And Verify Data")]
        public async Task VerifyCheckbookDropdown()
        {
            _logger.Information("Selecting Department and verifying ShowAll filter data");
            await _checkbookPage.ClickDepartments();
            _logger.Information("Department selected and data verified successfully");
        }

        [Then(@"Click Year View")]
        public async Task ClickYearView()
        {
            _logger.Information("Clicking Year View");
            await _checkbookPage.ClickYearView();
            _logger.Information("Year View clicked successfully");
        }

        private async Task VerifyGrid(Table table){
            _logger.Information("Verifying grid data");
            foreach(TableRow row in table.Rows){
               string column = row["columns"];
               _logger.Debug("Verifying column: {Column}", column);
               await _checkbookPage.VerifyDataGrid(column);
            }
            _logger.Information("Grid data verified successfully");
        }

        [Then(@"Verify Grid fields for PeriodView")]
        public async Task VerifyPeriodViewGridHeader(Table table)
        {
            _logger.Information("Verifying grid fields for Period View");
            await VerifyGrid(table);
            _logger.Information("Period View grid fields verified successfully");
        }

        [Then(@"Verify Grid fields for YearView")]
        public async Task VerifyYearViewGridHeader(Table table)
        {
            _logger.Information("Verifying grid fields for Year View");
            await VerifyGrid(table);
            _logger.Information("Year View grid fields verified successfully");
        }


        [Then(@"Add GLCodes and save")]
        public async Task AddGLCodes(Table table)
        {
            _logger.Information("Adding GL codes and saving");
            List<GlCodes> glList = new List<GlCodes>();
            foreach(TableRow row in table.Rows){
                glList.Add(new GlCodes(row["forcast amount"],row["Fixed"],row["active"]));
                _logger.Debug("Added GL code with forecast amount: {ForecastAmount}, Fixed: {Fixed}, Active: {Active}", 
                    row["forcast amount"], row["Fixed"], row["active"]);
            }
            ((CommonContext)_scenarioContext["commonContext"]).GLCodesList = glList;
            await _checkbookPage.AddNewGlCodes((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("GL codes added and saved successfully");
        }

        [Then(@"Update/Verify Adjust Forecast Amount by %")]
        public async Task AdjustForecastAmt()
        {
            _logger.Information("Updating/Verifying Adjust Forecast Amount by %");
            await _checkbookPage.AdjustForecastAmts((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Forecast Amount adjusted and verified successfully");
        }

        [Then(@"Add/Verify journal entry")]
        public async Task AddJournalEntry()
        {
            _logger.Information("Adding/Verifying journal entry");
            await _checkbookPage.AddJournalEntry((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Journal entry added and verified successfully");
        }
        
        [Then(@"Verify checkbook and return")]  
        public async Task VerifyCheckBook(){
            _logger.Information("Verifying checkbook and returning");
            await _checkbookPage.VerifyCheckBook(((CommonContext)_scenarioContext["commonContext"]),true);
            _logger.Information("Checkbook verified and returned successfully");
        }

        [Then(@"Verify checkbook")]
        public async Task VerifyCheckBook1(){
            _logger.Information("Verifying checkbook");
            await _checkbookPage.VerifyCheckBook(((CommonContext)_scenarioContext["commonContext"]));
            _logger.Information("Checkbook verified successfully");
        }

        [Then(@"Edit and Verify GLCode Description, Forecast Amount, Active and Fixed Codes(.*)")]
        public async Task EditGlCodes(string yearview)
        {
            _logger.Information("Editing and verifying GL code properties with year view: {YearView}", !string.IsNullOrEmpty(yearview));
            await _checkbookPage.EditGlCodes((CommonContext)_scenarioContext["commonContext"],!string.IsNullOrEmpty(yearview));
            _logger.Information("GL code properties edited and verified successfully");
        }

        [Then(@"Delete/verify GLCodes(.*)")]
        public async Task DeleteGLCodes(string yearview)
        {
            _logger.Information("Deleting/verifying GL codes with year view: {YearView}", !string.IsNullOrEmpty(yearview));
            await _checkbookPage.DeleteGLCodes((CommonContext)_scenarioContext["commonContext"],!string.IsNullOrEmpty(yearview));
            _logger.Information("GL codes deleted and verified successfully");
        }

        [Then(@"Undelete/Verify GLCodes(.*)")]
        public async Task UnDeleteGLCodes(string yearview)
        {
            _logger.Information("Undeleting/verifying GL codes with year view: {YearView}", !string.IsNullOrEmpty(yearview));
            await _checkbookPage.UnDeleteGLCodes((CommonContext)_scenarioContext["commonContext"],!string.IsNullOrEmpty(yearview));
            _logger.Information("GL codes undeleted and verified successfully");
        }

        [Then(@"Add GLCodes and save with period copyacross")]
        public async Task AddGlCodesWithPeriod(Table table)
        {
            _logger.Information("Adding GL codes with period copyacross and saving");
            List<GlCodes> glList = new List<GlCodes>();
            foreach(TableRow row in table.Rows){
                glList.Add(new GlCodes(row["forcast amount"],row["budget amount"],row["Fixed"],row["active"]));
                _logger.Debug("Added GL code with forecast amount: {ForecastAmount}, budget amount: {BudgetAmount}, Fixed: {Fixed}, Active: {Active}", 
                    row["forcast amount"], row["budget amount"], row["Fixed"], row["active"]);
            }
            ((CommonContext)_scenarioContext["commonContext"]).GLCodesList = glList;
            await _checkbookPage.AddGlCodesWithPeriod((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("GL codes with period copyacross added and saved successfully");
        }

        [Then(@"Verify Invoice amounts is matching with checkbook amount")]
        public async Task VerifyPOWithAmounts()
        {
            _logger.Information("Verifying invoice amounts match with checkbook amount");
            await _checkbookPage.ExpandAndOpenCheckBook((CommonContext)_scenarioContext["commonContext"]);
            await _checkbookPage.VerifyPOWithAmounts((CommonContext)_scenarioContext["commonContext"],"actualized");
            _logger.Information("Invoice amounts verified successfully");
        }

        [Then(@"Open Checkbook")]
        public async Task OpenCheckbookNoExpansion()
        {
            _logger.Information("Opening checkbook without expansion");
            await _checkbookPage.OpenCheckbook((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Checkbook opened successfully without expansion");
        }
        [Then(@"Verify Grid fields in Checkbook")]
        public async Task VerifyDataGrid(Table table)
        {
            _logger.Information("Verifying grid fields in Checkbook");
            foreach(TableRow row in table.Rows)
            {
               string column = row["columns"];
               _logger.Debug("Verifying column: {Column}", column);
               await _checkbookPage.VerifyDataGrid(column);
            }
            _logger.Information("Grid fields in Checkbook verified successfully");
        }

        [Then("Validate Hold amount on PO.")]
        public async Task ValidateHoldPo()
        {
            _logger.Information("Validating hold amount on PO");
            await _checkbookPage.ExpandAndOpenCheckBook((CommonContext)_scenarioContext["commonContext"]);
            await _checkbookPage.VerifyPOWithAmounts((CommonContext)_scenarioContext["commonContext"],"actualized");
            _logger.Information("Hold amount on PO validated successfully");
        }

        

        [Then(@"VerifyPlannedAmt")]
        public async Task VerifyPlannedAmount()
        {
            _logger.Information("Verifying planned amount");
            await _checkbookPage.VerifyPlannedAmt((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Planned amount verified successfully");
        }

        [Then(@"Verify CommittedAmt")]
        public async Task VerifyCommittedAmount()
        {
            _logger.Information("Verifying committed amount");
            await _checkbookPage.VerifyCommittedAmt((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Committed amount verified successfully");
        }

        [Then(@"Verify ActualizedAmt")]
        public async Task VerifyActualizedAmount()
        {
            _logger.Information("Verifying actualized amount");
            await _checkbookPage.VerifyActualizedAmt((CommonContext)_scenarioContext["commonContext"]);
            _logger.Information("Actualized amount verified successfully");
        }

        [Then(@"exempt all GL Codes")]
        public async Task ExemptAllGLCodes()
        {
            _logger.Information("Exempting all GL codes");
            await _checkbookPage.ExemptAllGLCodes();
            _logger.Information("All GL codes exempted successfully");
        }
        [Then(@"Verify Exempt Assign users in PO Workflows")]
        public async Task ExemptGLCodeAll()
        {
            _logger.Information("Verifying exempt assign users in PO workflows");
            await _checkbookPage.ExemptGLCodeinPOWorkflow();
            _logger.Information("Exempt assign users in PO workflows verified successfully");
        }
        [Then(@"Verify Exempt Assign users in Invoice Workflows")]
        public async Task ExemptGLCodeAll1()
        {
            _logger.Information("Verifying exempt assign users in Invoice workflows");
            await _checkbookPage.ExemptGLCodeinInvoiceWorkflow();
            _logger.Information("Exempt assign users in Invoice workflows verified successfully");
        }
    }
}
