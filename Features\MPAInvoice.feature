@Invoice
Feature:Manage Multi-Period Accounting for Invoices

@MPAInvoiceManagement
Scenario Outline:Create and Edit invoice with MPA
    Given Open the Web url 
	Then Login to site with username and password
	Then HomePage Loaded
    Then Change user in work area with user <PERSON><PERSON><PERSON> - Stackular - Manager
    Then Open checkbook with return
    Then Create Po for supplier
    Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | |
        | 1 | 1 | | |
    Then click on Edit Periods button
	Then update MPA with 2 periods.
    Then Submit and send for approval to Jason-Stackular Manager
    Then Change user in work area to parent
    Then Copy PO Url
    Then Delete row and approve PO
    Then Expand Invoice Manager And Enter Invoice
    Then Select Company
    Then Enter Document Number with next
    Then Verify PO number in the Enter invoice PO grid
    Then Create invoice for PO created and save it
        | quantities|
    Then update mpa adding new row  
    Then Complete the invoice
    Then Create a credit memo with desired quantities
        |quantities|
        | -1 |   
    Then update mpa adding new row    
    Then Complete New Invoice
    Then Close PO Action
@MPAInvoicePerm762
Scenario Outline:Manage Invoice with MPA Permission 762
    Given Open the Web url 
	Then Login to site with username and password
	Then HomePage Loaded
    Then Change user in work area with user <PERSON><PERSON><PERSON>ack<PERSON> 
    Then Open checkbook with return
    Then Create Po for supplier
    Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | | 
    Then click on Edit Periods button
	Then update MPA with 2 periods.
    Then Save and Approve period PO
    Then Expand Invoice Manager And Enter Invoice
    Then Select Company
    Then Enter Document Number with next
    Then Verify PO number in the Enter invoice PO grid
    Then Create invoice for PO created and save it
        | quantities|
    Then convert to single period and update
    Then update period in invoice
    Then Complete the invoice
    Then proxylogoff
@MPAInvoiceNoPerm
Scenario Outline:Manage Invoice with MPA without 762 and 763 permissions
    Given Open the Web url 
	Then Login to site with username and password
	Then HomePage Loaded
    Then Change user in work area with user Nurul Alam - Stackular - Manager
    Then Open checkbook with return
    Then Create Po for supplier
    Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | | 
    Then click on Edit Periods button
	Then update MPA with 2 periods.
    Then Save and Approve period PO
    Then Expand Invoice Manager And Enter Invoice
    Then Select Company
    Then Enter Document Number with next
    Then Verify PO number in the Enter invoice PO grid
    Then Create invoice for PO created and save it
        | quantities|
    Then convert to single period
    Then convert to multi period
    Then Complete the invoice
    Then proxylogoff
@NonMPAPOWithPerm762
Scenario Outline:Verify Non MPA PO and invoice with 762 permission
    Given Open the Web url 
	Then Login to site with username and password
	Then HomePage Loaded
    Then Change user in work area with user Jason Storch - Buyer
    Then Open checkbook with return
    Then Create Po for supplier
    Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | | 
    Then Save and Approve period PO
    Then Expand Invoice Manager And Enter Invoice
    Then Select Company
    Then Enter Document Number with next
    Then Verify PO number in the Enter invoice PO grid
    Then Create invoice for PO created and save it
        | quantities|
    Then Update single period in the invoice
    Then proxylogoff
@NonMPAPOWithPerm763
Scenario Outline:Verify Non MPA PO and invoice with 763 permission
    Given Open the Web url
    Then Login to site with username and password
    Then HomePage Loaded
    Then Open checkbook with return
    Then Create Po for supplier
    Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | | 
    Then Save and Approve period PO
    Then Expand Invoice Manager And Enter Invoice
    Then Select Company
    Then Enter Document Number with next
    Then Verify PO number in the Enter invoice PO grid
    Then Create invoice for PO created and save it
        | quantities|
    Then update mpa adding new row
    Then proxylogoff



