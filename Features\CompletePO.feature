@PO
Feature:1_Complete PO
@createinvoiceandlineitemfunctionalityvalidation @retry
Scenario Outline: 00_AVerify Line Items Table grid
# This scenario has retry mechanism enabled. It will retry up to 3 times if it fails.
    Given Open the Web url
    Then Login to site with username and password
    Then HomePage Loaded
    Then Open checkbook with return
    Then Create Po for supplier
    Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | |
        | 1 | 2 | | |
    Then Submit and Approve PO.
    Then Expand Invoice Manager And Enter Invoice
    Then Select Company
    Then Enter Document Number with next
    Then Verify PO number in the Enter invoice PO grid
    Then Create invoice for PO created and save it
        | quantities|
    Then Verify Add Line items table grid fields
        | columns |
        | All Items Invoiced |
        | Taxable |
        | Current Doc Qty |
        | % vs PO |
        | Item # |
        | Description|
        | GL Code|
        | UOM|
        | Price|
        | Sub Total|
        | Actions |
    Then Adding line items and update item details
    Then Verify setzeroqty functionality
    Then Verify BulkAssignGLCode functionality
    Then Verify combining line items having 0 qty functionality
    Then Verify combining line items having one and Zero qty functionality
    Then Verify combining line items having one and one qty functionality
    Then Verify Delete button is visible for the line items present in the table grid
    Then Verify Split By Percentage Line Items
    Then Verify Split By Quantity Line Items
@invoicedetailspagevalidation @retry
Scenario Outline:00_BVerify Add Line items table grid
# This scenario has retry mechanism enabled. It will retry up to 3 times if it fails.
    Given Open the Web url
    Then Login to site with username and password
    Then HomePage Loaded
    Then Open checkbook with return
    Then Create Po for supplier
    Then  Enter  quantity,itemnumber,description and amount specific GL Code from GL code
        |quantities|amounts|UOM|weights|
        | 1 | 1 | | |
        | 1 | 2 | | |
    Then Submit and Approve PO.
    Then Expand Receiving Module and Enter Receipts page
    Then Select Company
    Then Search PO details by PONUmber
    Then Create Receipt
    Then Add line items and update item details
    Then Verify setzeroqty
    Then Verify BulkAssignGLCode
    Then Verify combining line items having 0 qty
    Then Verify combining line items having one and Zero qty
    Then Verify combining line items having one and one qty
    Then Verify Delete button is visible for the line items present in the Grid
    
@ViewReceiptmodulevalidation @retry
Scenario Outline: 11_Create PO.Navigate to Enter Receipts. Validate PO Grid, Receipts grid ,PO Summary. Create a Receipt for PO Created when blind receiving is off
# This scenario has retry mechanism enabled. It will retry up to 3 times if it fails.
    Given Open the Web url
    Then Login to site with username and password
    Then HomePage Loaded
    Then Open checkbook with return
    Then Expand Offline
    Then Copy and Approve PO.
    Then Expand Receiving Module and Enter Receipts page
    Then Select Company
    Then Search PO details by PONUmber
    Then Verify PO grid fields
       | columns       |
       | PO #          |
       | # of Receipts |
       | PO Status     |
       | Status Date   |
       | Supplier Name |
       | Buyer         |
       | Subject       |
    Then Create And Verify Receipt
