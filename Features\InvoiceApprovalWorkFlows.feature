@Invoice
Feature: Verify PO Approvals and Invoice Approval workflow functionality
@ExemptGLCode
Scenario Outline: Verify Exempt PO workflow and Invoice Approval workflow functionality by creating Exempt GLCode
    Given Open the Web url 
	Then Login to site with username and password
	Then HomePage Loaded
    Then Open checkbook with return
    Then Expand Offline
    Then Copy and Approve PO.
    Then Expand Invoice Manager And Enter Invoice
    Then Select Company
    Then Enter Document Number with next
    Then Verify PO number in the Enter invoice PO grid
    Then Create invoice for PO created with new GL code
        | quantities|
    Then Complete the invoice

@RequiredGLCode
Scenario Outline: Verify Exempt PO workflow and Invoice Approval workflow functionality by creating Required GLCode 
    Given Open the Web url
	Then Login to site with username and password
	Then HomePage Loaded
    Then Change user in work area with user Nurul Alam - Stackular - Manager
    Then Open checkbook and filter for GL code where status is "Required"
	Then Create Po for supplier
	Then Enter  quantity,itemnumber,description and amount specific GL Code from GL code
		| quantities | amounts | UOM | weights | glType   |
		| 1          | 1     |     | 5       | Required |
    Then Submit and send for approval to Jason-Stackular Manager
    Then Change user in work area to parent
    Then Copy PO Url
    Then Submit and Approve PO.
    Then Expand Invoice Manager And Enter Invoice
    Then Select Company
    Then Enter Document Number with next
    Then Verify PO number in the Enter invoice PO grid
    Then Create invoice for PO created with new GL code
        | quantities|
    Then Click on Submit for Approval Action
    Then logoff
    Then Login to site with Vice President Credentials
    Then HomePage Loaded
    Then Check if po approved
    Then Expand Invoice Manager
    Then Verify Document Number in the Action Required Page
