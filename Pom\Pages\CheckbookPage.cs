using System;
using Microsoft.Playwright;
using SpecFlowProject.BusinessObjects;
using System.Globalization;
using System.Collections;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ClosedXML.Excel;
using NPOI.SS.UserModel;
using NPOI.HSSF.UserModel;
using SpecFlowProject.Utils;
using System.Text.RegularExpressions;
using Newtonsoft.Json.Linq;
using System.Linq.Expressions;
using static NPOI.HSSF.Util.HSSFColor;
using TechTalk.SpecFlow.Infrastructure;

namespace SpecFlowProject.Pom.Pages;


public class CheckbookPage: Base
{
    private ILocator columnDropDown => Page.FrameLocator("#v4Container").Locator(".columnEditorDropdown");

    private ILocator bdAmtCheckbox  => Page.FrameLocator("#v4Container").Locator("#initialBudget");

    private ILocator bdAmtCheckbox1  => Page.FrameLocator("#v4Container").Locator("#initialBudgetPeriod");
    private ILocator poACheckbox => Page.FrameLocator("#v4Container").Locator("#poApproval");
    private ILocator inACheckbox => Page.FrameLocator("#v4Container").Locator("#invoiceApproval");
    private ILocator SelectallCheckbox => Page.FrameLocator("#v4Container").Locator("div.k-list-header input");

    private ILocator bdRemainCheckbox  => Page.FrameLocator("#v4Container").Locator("#remainingInitialBudgetAmount");

    private ILocator bdPerceCheckbox  => Page.FrameLocator("#v4Container").Locator("#remainingInitialBudgetAmountPercentage");
    private ILocator fcAmtCheckbox  => Page.FrameLocator("#v4Container").Locator("#budgetAmount");
    private ILocator fcRemainCheckbox  => Page.FrameLocator("#v4Container").Locator("#remainingBudgetAmount");

    private ILocator fcPerceCheckbox  => Page.FrameLocator("#v4Container").Locator("#remainingBudgetAmountPercentage");

    private ILocator applyButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Apply')");

    private ILocator _departmentDropdown => Page.FrameLocator("#v4Container").Locator("main>div:nth-child(1) .k-dropdownlist");
    private ILocator _viewallLogsButton => Page.FrameLocator("#v4Container").Locator("button:has-text('View All Logs')");
    private ILocator _editButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Edit')");
    private ILocator _cancelButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Cancel')");
    private ILocator _clearFilters => Page.FrameLocator("#v4Container").Locator("button:has-text('Clear Filters')");
    private ILocator _saveButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Save')");
    private ILocator _periodViewButton => Page.FrameLocator("#v4Container").Locator("span:has-text('Period View')");
    private ILocator _yearViewButton => Page.FrameLocator("#v4Container").GetByText("Year View", new() { Exact = true }); 
    private ILocator _showActiveDropdown => Page.FrameLocator("#v4Container").Locator(".k-toolbar .k-dropdownlist").Nth(0);
    private ILocator _departmentDropdownClick => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(1)");
    private ILocator _departmentDropdownClickOrig => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(1)");
    private ILocator _activeDropdownLoopClick => Page.FrameLocator("#v4Container").Locator(".k-list ul li");
    private ILocator _activeDropdownDelete => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(5)");
    private ILocator _activeDropdownUL => Page.FrameLocator("#v4Container").Locator(".k-list ul");
    private ILocator _addNewGlCodeButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Add New GL Code')");
    private ILocator _deleteButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Delete')");
    private ILocator _unDeleteButton => Page.FrameLocator("#v4Container").Locator("button:has-text('UnDelete')");
    private ILocator _adjustByPercentButton => Page.FrameLocator("#v4Container").Locator("button:has-text('Adjust Forecast By %')");
    private ILocator _adjustByPercentInput => Page.FrameLocator("#v4Container").Locator(".k-window-content input");
    private ILocator _caliculateButton => Page.FrameLocator("#v4Container").Locator(".k-widget button:has-text('Calculate')");
    private ILocator _clickFilter(int i) => Page.FrameLocator("#v4Container").Locator(".k-grid-header table thead tr:last-child th:nth-child("+i+") .k-grid-header-menu").Nth(0);
    private ILocator _filterDropdown => Page.FrameLocator("#v4Container").Locator(".k-filter-menu .k-dropdownlist");
    private ILocator _filterDropdownEquals => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(3)");
    private ILocator _filterDropdownoption(int i) => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child("+i+")");
    private ILocator _dropdownTreeCb => Page.FrameLocator("#v4Container").Locator("#period-dropdown"); 

    private ILocator _dropdownTreeCbExpand => Page.FrameLocator("#v4Container").Locator("#period-dropdown[aria-expanded='true']"); 
    private ILocator _dropdownTreeCbTree => Page.FrameLocator("#v4Container").Locator(".k-popup > .k-treeview > .k-treeview-lines > .k-treeview-item");
    private ILocator _amountText(int i) => Page.FrameLocator("#v4Container").Locator(".k-grid-table tbody tr td:nth-child("+i+") button");
    private ILocator _poTotals => Page.FrameLocator("#v4Container").Locator(".k-window-content table tbody tr td:nth-child(6)");
    private ILocator _textArea => Page.FrameLocator("#v4Container").Locator(".k-window-content .k-textarea textarea");
    private ILocator _clickPopupFilter => Page.FrameLocator("#v4Container").Locator(".k-dialog .k-grid-header table thead tr:last-child th:nth-child(4) .k-grid-header-menu");
    private ILocator _glCodeRowNoCopy(int i) => Page.FrameLocator("#v4Container").Locator(".view-year-checkbook table tbody tr:nth-child("+i+")");
    private ILocator _glCodeRow(int i) => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable table tbody tr:nth-child("+i+")");
    private ILocator _glCodeRowYear(int i) => Page.FrameLocator("#v4Container").Locator(".view-year-checkbook  table tbody tr:nth-child("+i+")");
    private ILocator _glCodeValYear(int i,int j) => _glCodeRowYear(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeDescYear(int i,int j) => _glCodeRowYear(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeVal(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeDesc(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeBudget(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeForecast(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeFixed(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") .k-switch");
    private ILocator _glCodeActiveYear(int i,int j) => _glCodeRowYear(i).Locator("td:nth-child("+j+") .k-switch");
    private ILocator _glCodeFixedYear(int i,int j) => _glCodeRowYear(i).Locator("td:nth-child("+j+") .k-switch");
    private ILocator _glCodeForcastAmt(int i,int j) => _glCodeRowYear(i).Locator("td:nth-child("+j+") input");
    private ILocator _glCodeCopyAccross(int i,int j) => _glCodeRowYear(i).Locator("td:nth-child("+j+") button:has-text('Copy Across')");

    private ILocator _budgetForcast => Page.FrameLocator("#v4Container").Locator(".k-popup ul li:has-text('Budget and Forecast')");
    private ILocator _onlyBudget => Page.FrameLocator("#v4Container").Locator(".k-popup ul li:has-text('Only Budget')");
    private ILocator _onlyForcast => Page.FrameLocator("#v4Container").Locator(".k-popup ul li:has-text('Only Forecast')");
    private ILocator _glCodeActive(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") .k-switch");
    private ILocator _glCodeSelector(int i) => _glCodeRow(i).Locator("td:nth-child(1) input"); 
    private ILocator _glCodeJournelButton(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") button"); 
    private ILocator _glCodeViewButton(int i,int j) => _glCodeRow(i).Locator("td:nth-child("+j+") button"); 
    private ILocator _adjustBalanceGrid  => Page.FrameLocator("#v4Container").Locator("#adjustBalanceGrid table tbody tr:nth-child(1)");
    private ILocator _adjustBalanceGridInput => _adjustBalanceGrid.Locator("td:nth-child(12) input");
    private ILocator _adjustBalanceGridSelect => _adjustBalanceGrid.Locator("td:nth-child(11) .k-dropdownlist");
    private ILocator _allDepartments => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(1):has-text('All Departments')");
    private ILocator _FirstDepartments => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(2)");
    private ILocator _addClick => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(2)");
    private ILocator _updateButton => Page.FrameLocator("#v4Container").Locator(".k-dialog-buttongroup button:has-text('Update')");
    private ILocator _glCodeRows => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable table tbody tr");
    private ILocator _glCodeRowsNoCopyYear => Page.FrameLocator("#v4Container").Locator(".view-year-checkbook  table tbody tr");
    private ILocator _glCodeRowsYear => Page.FrameLocator("#v4Container").Locator(".view-year-checkbook table tbody tr");
    private ILocator _glTrans => Page.FrameLocator("#v4Container").Locator("#TransactionGrid table tbody tr");
    private ILocator _glTransFull => Page.FrameLocator("#v4Container").Locator("#TransactionGrid .k-grid-header table thead tr:lastchild th:nth-child(0) .k-grid-header-menu");
    private ILocator _approveBtnConfirm => _iframe.Locator(".k-dialog .k-dialog-buttongroup button:has-text('OK')");

    private ILocator hasUnsaved => _iframe.GetByRole(AriaRole.Button, new() { Name = "OK", Exact = true });
    private ILocator _okBtnConfirm => _iframe.Locator(".k-dialog .k-dialog-buttongroup button:has-text('Ok')");
    private ILocator _theadperiods => Page.FrameLocator("#v4Container").Locator("table thead tr th.budget-amt-header .k-column-title:has-text('Period')");
    private ILocator _plannedAmtLink(int i) => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable table tr:nth-child(1) td:nth-child("+i+") button");
    private ILocator _committedAmtLink(int i) => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable table tr:nth-child(1) td:nth-child("+i+") button");
    private ILocator _actualizedAmtLink(int i) => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable table tr:nth-child(1) td:nth-child("+i+") button");
    private ILocator _GLCodeFilter(int i) => Page.FrameLocator("#v4Container").Locator(".k-grid-header table thead tr:last-child th:nth-child("+i+") .k-grid-header-menu");
    private ILocator _ShowAll => _iframe.Locator(".k-window-content.k-dialog-content .k-tabstrip-items.k-reset li:nth-child(2)");
    private ILocator _plannedPOFilter => Page.FrameLocator("#v4Container").Locator("#holdAmountTransactionGrid .k-grid-header table thead tr:last-child th:nth-child(4) .k-grid-header-menu");
    private ILocator _committedPOFilter => Page.FrameLocator("#v4Container").Locator("#committedAmountTransactionGrid .k-grid-header table thead tr:last-child th:nth-child(4) .k-grid-header-menu");
    private ILocator _actualizedPOFilter => Page.FrameLocator("#v4Container").Locator("#actualizedTransactionGrid .k-grid-header table thead tr:last-child th:nth-child(4) .k-grid-header-menu");
    private ILocator _closeGrid => Page.FrameLocator("#v4Container").Locator(".k-dialog-buttongroup.k-actions.k-actions-stretched button");
    private ILocator _expandRow => Page.FrameLocator("#v4Container").Locator("#holdAmountTransactionGrid tbody tr:nth-child(1) td:nth-child(1) path");

    private ILocator budgetTxt(int i) => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable .k-grid-header thead tr:last-child th:nth-child("+i+")");

    private ILocator _selectAll => Page.FrameLocator("#v4Container").Locator(".k-grid-header table thead tr:first-child th:nth-child(1) input");
    private ILocator _bulkManage => Page.FrameLocator("#v4Container").Locator("button:has-text('Bulk Manage GL Code Attributes')");

    private ILocator labelLocator =>  Page.FrameLocator("#v4Container").Locator("label[aria-label='Always Exempt from Approval']");

    private ILocator assignAllUsers => Page.FrameLocator("#v4Container").Locator(".k-button-icon.k-icon.k-i-caret-double-alt-right");

    private ILocator sidePaneSave => Page.FrameLocator("#v4Container").Locator(".slide-pane button:has-text('Save')");

    private ILocator sidePaneCancel => Page.FrameLocator("#v4Container").Locator(".slide-pane button:has-text('Cancel')");
    private ILocator _okButton => Page.FrameLocator("#v4Container").Locator("button:has-text('OK')");
    private ILocator _InvoiceApprovalColumn => Page.FrameLocator("#v4Container").Locator(".k-grid-header table thead tr:last-child th:last-child");
    private ILocator _OffHyperlinkInvoice => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable tbody tr:first-child td:last-child button.text-link");

    private ILocator _OffHyperlinkPO => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable tbody tr:first-child td:nth-last-child(2) button.text-link");

    private ILocator sidePanelPOGrid => Page.FrameLocator("#v4Container").Locator(".slide-pane__content > div:last-child > div:first-child");

    private ILocator sidePanelInvoiceGrid => Page.FrameLocator("#v4Container").Locator(".slide-pane__content > div:last-child > div:last-child");

    private ILocator _AlwaysExemptPOfromApproval => sidePanelPOGrid.Locator("label[aria-label='Always Exempt from Approval']");
    private ILocator _AlwaysRequiredPOfromApproval => sidePanelPOGrid.Locator("label[aria-label='Always Require Approval']");
    private ILocator _AlwaysExemptInvoicefromApproval => sidePanelInvoiceGrid.Locator("label[aria-label='Always Exempt from Approval']");
    private ILocator _AlwaysRequiredInvoicefromApproval => sidePanelInvoiceGrid.Locator("label[aria-label='Always Require Approval']");
    private ILocator _selectallcheckboxofunassignedinpoapproval => sidePanelPOGrid.Locator("> div:last-child > div:last-child input").Nth(0);
    private ILocator _selectallcheckboxofunassignedininvoiceapproval => sidePanelInvoiceGrid.Locator("> div:last-child > div:last-child input").Nth(0);
    private ILocator _DoubleArrwbtninpoapproval => sidePanelPOGrid.Locator(".k-button-icon.k-icon.k-i-caret-double-alt-right");
    private ILocator _DoubleArrwbtnininvoiceapproval =>  sidePanelInvoiceGrid.Locator(".k-button-icon.k-icon.k-i-caret-double-alt-right");
    private ILocator _ImportExport => Page.FrameLocator("#v4Container").Locator("button:has-text('Import/Export')");
    private ILocator _FiscalYear => Page.FrameLocator("#v4Container").Locator("div.slide-pane__content>div:nth-child(3) div.mb-2:nth-child(5)");
    private ILocator _selectfiscalyear => Page.FrameLocator("#v4Container").Locator("div.k-list-content>ul li:nth-child(4)");
    private ILocator _Exportbtn => Page.FrameLocator("#v4Container").Locator(".flex.items-center.justify-end div button");
    private ILocator _propertydropdown => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(2) span span span");
    private ILocator _firstcompanydropdown => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(2)");
    private ILocator _singletabbtn => Page.FrameLocator("#v4Container").Locator("div.slide-pane__content>div:nth-child(3) div.mb-2:nth-child(4) ul>li:nth-child(2) input");
    private ILocator _txtbtn => Page.FrameLocator("#v4Container").Locator("div.slide-pane__content>div:nth-child(3) div.mb-2:nth-child(3) span:nth-child(2) input");
    private ILocator _allglcodes => Page.FrameLocator("#v4Container").Locator("div.slide-pane__content>div:nth-child(3) div.mb-2:nth-child(6) ul>li:nth-child(2) input");
    private ILocator _firstGLcode => Page.FrameLocator("#v4Container").Locator(".k-grid-container tr:first-child td:nth-child(2) span");
    private ILocator _includeforecastamt => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(7) div:nth-child(2) label:nth-child(2) span input");
    private ILocator _singlecomincludeforecastamt => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(2) label:nth-child(2) span input");
    private ILocator _singlecomallglcodes => Page.FrameLocator("#v4Container").Locator("div.slide-pane__content>div:nth-child(3) div.mb-2:nth-child(5) ul>li:nth-child(2) input");
    private ILocator _mgmtcompanydropdown => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(2) .k-dropdownlist button");
    private ILocator _innertextofmgmtcompany => Page.FrameLocator("#v4Container").Locator("div.k-list-content ul li:nth-child(12)");
    private ILocator _superadminpropertydropdown => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(3) span span span:nth-child(1)");
    private ILocator _superadmininnertextpropertydropdown => Page.FrameLocator("#v4Container").Locator("div.k-list-content ul li:nth-child(12)");
    private ILocator _fiscalyearofsuperadmin => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(6) span span span:nth-child(1)");
    private ILocator _selectfiscalyearofsuperadmin => Page.FrameLocator("#v4Container").Locator("div.k-list-content>ul li:nth-child(2)");
    private ILocator _superadmintxt => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) span:nth-child(2) input");
    private ILocator _superadminsingletab => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div :nth-child(5) li:nth-child(2) input");
    private ILocator _superadminallglcodes => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div :nth-child(5) li:nth-child(2) input");
    private ILocator _superadminsinglecomallglcodes => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div :nth-child(1) li:nth-child(2) input");
    private ILocator _superadminsinglecomfiscalyear => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(5) span span span:nth-child(1)");

    private ILocator _superadminincludeforecastamtbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(8) div:nth-child(2) label:nth-child(2) span input");
    private ILocator _superadminsinglecomincludeforecastamtbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(2) label:nth-child(2) span input");
    private ILocator _superadmintxtfiscalyear => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(5) span span span");
    private ILocator _superadmintxtinnerfiscalyear => Page.FrameLocator("#v4Container").Locator("div.k-list-content ul li:nth-child(3)");
    private ILocator _superadmintxtallglcodes => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div :nth-child(2) li:nth-child(2) input");
    private ILocator _superadmintxtforecastamtbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(2) label:nth-child(2) span input");
    private ILocator _Importsuperadminbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(2) div button:nth-child(2)");
    private ILocator _Importsuperadminfiscalyear => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(3) span span span");
    private ILocator _Importsuperadminselectfiscalyear => Page.FrameLocator("#v4Container").Locator("div.k-list-content ul li:nth-child(3)");
    private ILocator _Importbtn => Page.FrameLocator("#v4Container").Locator(".flex.items-center.justify-end div button");
    private ILocator _Proceedandimportbtn => Page.FrameLocator("#v4Container").Locator("button:has-text('Proceed and Import')");
    private ILocator _Draganddropbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(5)");
    private ILocator _Importbudgetamtbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div :nth-child(2) li:nth-child(1) input");
    private ILocator _ViewResultsbtn => Page.FrameLocator("#v4Container").Locator(".slide-pane__content div:nth-child(3) div:nth-child(7) div p:nth-child(3)");
    private ILocator _Downloadbtn => Page.FrameLocator("#v4Container").Locator(".p-4.border-dashed.border.border-gray-300.flex.justify-between button");
    private ILocator _xiconinpopup => Page.FrameLocator("#v4Container").Locator(".k-window-actions.k-dialog-actions");
    private ILocator _txtbuttoninsuperadminforimport => Page.FrameLocator("#v4Container").Locator(".k-window-content.k-dialog-content div:nth-child(2) div ul li:nth-child(2) input");

    private ILocator sortedPO => Page.FrameLocator("#v4Container").Locator("#viewPeriodCheckbookTable table thead th:nth-child(12) .k-link");

    private ILocator _filterInput1 => _iframe.Locator(".k-filter-menu input");

    public CheckbookPage(IPage page) : base(page)
    {
        _logger.Debug("CheckbookPage initialized");
    }
    
    private async Task SetGlCodes(CommonContext commonContext){
        _logger.Debug("Setting GL codes");
        string path = @".\\auth\\glcodes.txt"; 
        _logger.Debug($"Checking if file exists at path: {path}");
        if (File.Exists(path) && commonContext.GLCodesData.Count == 0) 
        {
            _logger.Debug("File exists and GLCodesData is empty. Reading from file.");
            string readText = File.ReadAllText(path); 
            _logger.Debug("Deserializing GL codes from file");
            Dictionary<string,GlStatus> totalData = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string,GlStatus>>(readText) ?? new Dictionary<string,GlStatus>();
            _logger.Debug($"Loaded {totalData.Count} GL codes from file");
            commonContext.GLCodesData  = totalData;
            _logger.Debug("Waiting for response from GLBudgetsByPeriod API");
            await WaitForResponseAsync("api/GLCode/GLBudgetsByPeriod",commonContext);
            _logger.Debug("Completed API call");
        }else if(commonContext.GLCodesData.Count == 0){
            _logger.Debug("GLCodesData is empty and file doesn't exist. Fetching from API.");
            Newtonsoft.Json.Linq.JArray arr = await WaitForResponseAsync("api/GLCode/GLBudgetsByPeriod",commonContext);
            Dictionary<string,GlStatus> totalData = new Dictionary<string,GlStatus>();
            if(arr!=null){
                _logger.Debug($"Received {arr.Count} GL codes from API");
                //Console.WriteLine("Inside");
                for (int i = 0; i < arr.Count; i++)
                {
                    #pragma warning disable CS8602 // Dereference of a possibly null reference.
                    var code = arr[i]["code"].ToString();
                    var invoiceApproval = GetType(arr[i]["invoiceGlApproval"]["status"].ToString());
                    var poApproval = GetType(arr[i]["poGlApproval"]["status"].ToString());
                    
                    #pragma warning disable CS8604 // Possible null reference argument.
                    var POC = arr[i]["poGlApproval"]["clientIds"].Values<long>().Count();
                    var IOC = arr[i]["invoiceGlApproval"]["clientIds"].Values<long>().Count();
                    var isActive = (bool)arr[i]["isActive"] && !(bool)arr[i]["isDeleted"];
                    #pragma warning restore CS8604 // Possible null reference argument.
                    #pragma warning restore CS8602 // Dereference of a possibly null reference.
                    if(poApproval == "Exempt"){
                        poApproval = POC>10?"Exempt":"Off";
                    }
                    if(invoiceApproval == "Exempt"){
                        invoiceApproval = IOC>10?"Exempt":"Off";
                    }
                    totalData.Add(code,new GlStatus(poApproval,invoiceApproval,isActive));
                }
                _logger.Debug("Processing and sorting GL codes");
                var newtotalData = totalData.OrderBy(x=>x.Value.POStatus).ThenBy(x=>x.Value.InvoiceStatus).ToDictionary(group => group.Key, group => group.Value);
                _logger.Debug("Serializing GL codes to save to file");
                string content = Newtonsoft.Json.JsonConvert.SerializeObject(newtotalData); 
                _logger.Debug($"Writing {newtotalData.Count} GL codes to file");
                File.WriteAllText(path, content);
                /*foreach(KeyValuePair<string, GlStatus> entry in newtotalData)
                {
                    Console.WriteLine(entry.Key);
                }*/
                commonContext.GLCodesData  = newtotalData;
                _logger.Debug("GL codes successfully loaded from API and saved to file");
            }else{
                _logger.Error("API call failed. Falling back to UI extraction");
                Console.WriteLine("Call Failed");
                await _closeBtnX.ClickAsync();
                await sortedPO.ClickAsync();
                _logger.Debug("Extracting GL codes from UI");
                foreach(var elem in await _glCodeRows.AllAsync()){
                   var c = await elem.Locator("td:nth-child(3)").InnerTextAsync()??"";   
                   totalData.Add(c,new GlStatus("Off","Off",true));
                }
                _logger.Debug($"Extracted {totalData.Count} GL codes from UI");
                string content = Newtonsoft.Json.JsonConvert.SerializeObject(totalData); 
                _logger.Debug("Saving GL codes to file");
                File.WriteAllText(path, content);
                commonContext.GLCodesData  = totalData;
                _logger.Debug("GL codes successfully loaded from UI and saved to file");
            }
        }
        else {
            _logger.Debug("GLCodesData already populated, skipping loading");
        }
        _logger.Debug("SetGlCodes completed");
        await Task.Run(()=>{});
    }

    public async Task ExpandAndOpenCheckBookReturn(CommonContext commonContext){
        _logger.Debug("Starting ExpandAndOpenCheckBookReturn");
        string path = @".\\auth\\glcodes.txt"; 
        if(commonContext.GLCodesData.Count>0){
            _logger.Debug("GLCodesData already populated, returning");
            return;
        }else if (File.Exists(path) && commonContext.GLCodesData.Count == 0) 
        {
            _logger.Debug($"Loading GL codes from file: {path}");
            string readText = File.ReadAllText(path); 
            Dictionary<string,GlStatus> totalData = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string,GlStatus>>(readText) ?? new Dictionary<string,GlStatus>();
            commonContext.GLCodesData  = totalData;
            _logger.Debug($"Loaded {totalData.Count} GL codes from file");
        }else{
            _logger.Debug("File not found or GLCodesData empty, calling ExpandAndOpenCheckBook");
            await ExpandAndOpenCheckBook(commonContext);
        }
        _logger.Debug("ExpandAndOpenCheckBookReturn completed");
        await Task.Run(()=>{});
    }
    //Open checkbook
    public async Task OpenCheckbook(CommonContext commonContext){
        _logger.Debug("Starting OpenCheckbook");
        await _viewEditChecbook.ClickAsync();
        _logger.Debug("Clicked on View/Edit Checkbook");
        await SetGlCodes(commonContext);
        if(await IsVisibleAsync(_closeBtnX,1000,5)){
            _logger.Debug("Close button found, clicking it");
            await _closeBtnX.ClickAsync();
        }
        _logger.Debug("Checking for departments");
        await HasDepartments(commonContext);
        var bdTxt = commonContext.HasDepartments?4:3;
        var txt = await budgetTxt(bdTxt).InnerTextAsync()??"";
        if(txt == "Budget"){
            _logger.Debug("Budget field found, setting HasBudget to true");
            commonContext.HasBudget  =true;
            await HideBudgetFields();
        }
        _logger.Debug("OpenCheckbook completed");
    }
    //Open checkbook and filter by exempt GL codes
    public async Task OpenCheckbookandreturnExmptGLcodes(CommonContext commonContext ,bool cbOpened=false,string GLstatus="")
    {
        _logger.Debug($"Starting OpenCheckbookandreturnExmptGLcodes with cbOpened={cbOpened}, GLstatus={GLstatus}");

        if (!cbOpened)
        {
            _logger.Debug("Checkbook not opened, clicking checkbook link");
            await _checkbookLink.ClickAsync();
        }
        _logger.Debug("Clicking View/Edit Checkbook");
        await _viewEditChecbook.ClickAsync();
        _logger.Debug("Selecting company");
        await this.SelectCompany();
        _logger.Debug("Setting GL codes");
        await SetGlCodes(commonContext);
        if(await IsVisibleAsync(_closeBtnX,1000,5)){
            _logger.Debug("Close button found, clicking it");
            await _closeBtnX.ClickAsync();
        }

        _logger.Debug($"Setting up GL code status to {GLstatus}");
        await setupGLcodeStatusTo(GLstatus);
        Task.Delay(3000).Wait();
        
        // Click the "Select All" checkbox in the column dropdown
        _logger.Debug("Clicking Select All columns checkbox in the column dropdown");
        
        _logger.Debug("OpenCheckbookandreturnExmptGLcodes completed");
    }


    public async Task setupGLcodeStatusTo(string status,bool first=true)
    {
        _logger.Debug($"Starting setupGLcodeStatusTo with status={status}, first={first}");
        // Display PO and INA approval fields
        if (first)
        {
            _logger.Debug("First time setup, displaying PO and INA approval fields");
            await DisplayPoAndINAapprovalFields();
        }
        
        // Apply filter for status PO
        _logger.Debug("Applying filter for PO status");
        await SetFilter(3, status);

        // Apply filter for status Invoice
        _logger.Debug("Applying filter for Invoice status");
        await SetFilter(4, status);
    }

    public async Task ClickSelectAllColumnsCheckbox()
    {
        await columnDropDown.EvaluateAsync("node => node.click()");
        SelectallCheckbox.WaitForAsync();
        SelectallCheckbox.EvaluateAsync("node => node.click()");
        await applyButton.ClickAsync();
    }

    private async Task SetFilter(int filterIndex, string status)
    {
        _logger.Debug($"Starting SetFilter with filterIndex={filterIndex}, status={status}");
        await this._clickFilter(filterIndex).WaitForAsync();
        await this._clickFilter(filterIndex).ClickAsync();
        await this._filterDropdown.WaitForAsync();
        await this._filterDropdown.ClickAsync();

        // Determine the option based on status
        int option = status switch
        {
            "Exempt" => 3,
            "Required" => 2,
            "OFF" => 1,
            _ => 0 // Default case, you might want to handle this scenario
        };

        _logger.Debug($"Selected option {option} for status {status}");
        if (option != 0)
        {
            await _filterDropdownoption(option).ClickAsync();
            await _filterBtn.ClickAsync();
            _logger.Debug("Filter applied successfully");
        }
        else
        {
            _logger.Information("Invalid status provided.");
            Console.WriteLine("Invalid status provided.");
        }
        _logger.Debug("SetFilter completed");
    }

    public async Task SetupForecastAmountToLessThanZero()
    {
        _logger.Debug("Starting SetupForecastAmountToLessThanZero");
        _logger.Debug("Displaying remaining forecast amount fields");
        await DisplayRemainingForcastAmtFields();
        _logger.Debug("Applying filter 3 with value 0");
        await ApplyFilter(3, 5, "0");
        _logger.Debug("Applying filter 4 with value 0");
        await ApplyFilter(4, 5, "0");
        _logger.Debug("SetupForecastAmountToLessThanZero completed");
    }
    // Reusable filter setup method
    private async Task ApplyFilter(int filterIndex, int dropdownOptionIndex, string inputValue)
    {
        _logger.Debug($"Starting ApplyFilter with filterIndex={filterIndex}, dropdownOptionIndex={dropdownOptionIndex}, inputValue={inputValue}");
        await this._clickFilter(filterIndex).WaitForAsync();
        _logger.Debug("Filter button found");
        await _clickFilter(filterIndex).ClickAsync();
        _logger.Debug("Filter button clicked");
        await _filterDropdown.WaitForAsync();
        _logger.Debug("Filter dropdown found");
        await _filterDropdown.ClickAsync();
        _logger.Debug("Filter dropdown clicked");
        await _filterDropdownoption(dropdownOptionIndex).ClickAsync();
        _logger.Debug($"Selected dropdown option {dropdownOptionIndex}");
        await _filterInput1.ClearAsync();
        _logger.Debug("Filter input cleared");
        await _filterInput1.FillAsync(inputValue);
        _logger.Debug($"Filter input filled with value: {inputValue}");
        await _filterBtn.ClickAsync();
        _logger.Debug("Filter applied successfully");
    }


    public async Task CaptureGLCode(CommonContext commonContext, ISpecFlowOutputHelper _specFlowOutputHelper)
    {
        _logger.Debug("Starting CaptureGLCode");
        if (!await IsVisibleAsync(_firstGLcode, 1000, 5))
        {
            _logger.Error("No GL Codes present for the selected approval state");
            throw new Exception("No GL Codes present for the selected approval state");
        }
    
        // Fetch the GL code
        commonContext.SpecificGL = await _firstGLcode.InnerTextAsync();
        _logger.Debug($"Captured GL Code: {commonContext.SpecificGL}");
        _specFlowOutputHelper.WriteLine($"GLCode={commonContext.SpecificGL}");

        await Task.Run(()=>{});
        _logger.Debug("CaptureGLCode completed");
    }




    // General helper method to handle checkbox clicks
    private async Task ApplyColumnFilter(IEnumerable<ILocator> checkboxes)
    {
        _logger.Debug("Starting ApplyColumnFilter");
        await columnDropDown.EvaluateAsync("node => node.click()");
        _logger.Debug("Column dropdown clicked");

        // Click each checkbox in the list
        foreach (var checkbox in checkboxes)
        {
            await checkbox.EvaluateAsync("node => node.click()");
            _logger.Debug("Checkbox clicked");
        }

        await applyButton.ClickAsync();
        _logger.Debug("Apply button clicked, column filter applied");
    }

    private async Task HideBudgetFields()
    {
        _logger.Debug("Starting HideBudgetFields");
        // Pass the checkboxes specific to hiding budget fields
        await ApplyColumnFilter(new[] { bdAmtCheckbox, bdRemainCheckbox, bdPerceCheckbox });
        _logger.Debug("Budget fields hidden");
    }

    private async Task DisplayPoAndINAapprovalFields()
    {
        _logger.Debug("Starting DisplayPoAndINAapprovalFields");
        // Pass the checkboxes specific to displaying PO and INA approval fields
        await ApplyColumnFilter(new[] { SelectallCheckbox, poACheckbox, inACheckbox });
        _logger.Debug("PO and INA approval fields displayed");
    }

    private async Task DisplayRemainingForcastAmtFields()
    {
        _logger.Debug("Starting DisplayRemainingForcastAmtFields");
        // Pass the checkboxes specific to displaying remaining forecast fields
        await ApplyColumnFilter(new[] { fcAmtCheckbox, fcRemainCheckbox, fcPerceCheckbox });
        _logger.Debug("Remaining forecast amount fields displayed");
    }


    public async Task ExpandAndOpenCheckBookWithGlMasks(CommonContext commonContext){
        _logger.Debug("Starting ExpandAndOpenCheckBookWithGlMasks");
        await _checkbookLink.ClickAsync();
        _logger.Debug("Checkbook link clicked");
        JObject Jobject = await WaitUntilRequestCompleteResp("GetGlMasks",_viewGlMask,commonContext);
        _logger.Debug("GetGlMasks request completed");
        if((bool?)Jobject["setGlMask"] == true){
            _logger.Debug("GL Mask is set to true");
            var glMasksToken = Jobject["glMasks"];
            if (glMasksToken != null)
            {
                string[] j = glMasksToken?.ToObject<string[]>() ?? Array.Empty<string>();
                List<string> filteredList = j.Where(x => x.Contains("*") || x.Contains("&") || x.Contains("#")).ToList();
                commonContext.GlMask = filteredList.First();
                _logger.Debug($"GL Mask set to: {commonContext.GlMask}");
            }
        }
        await ExpandAndOpenCheckBook(commonContext,true);
        _logger.Debug("ExpandAndOpenCheckBookWithGlMasks completed");
    }

    private string GetType(string status){
        _logger.Debug($"Getting type for status: {status}");
        string result;
        switch(status){
            case "1": result = "Required"; break;
            case "2": result = "Exempt"; break;
            default: result = "Off"; break;
        }
        _logger.Debug($"Status {status} mapped to type: {result}");
        return result;
    }

    public async Task OpenCheckbookNoChecks(CommonContext commonContext){
        _logger.Debug("Starting OpenCheckbookNoChecks");
        await _checkbookLink.ClickAsync();
        _logger.Debug("Checkbook link clicked");
        await _viewEditChecbook.ClickAsync();
        _logger.Debug("View/Edit checkbook clicked");
        await WaitForResponseAsync("api/GLCode/GLBudgetsByPeriod",commonContext);
        _logger.Debug("GLBudgetsByPeriod response received");
        await _closeBtnX.ClickAsync();
        _logger.Debug("Close button clicked");
        await HasDepartments(commonContext);
        _logger.Debug("Department check completed");
        var bdTxt = commonContext.HasDepartments?4:3;
        var txt = await budgetTxt(bdTxt).InnerTextAsync()??"";
        _logger.Debug($"Budget text: {txt}");
        if(txt == "Budget"){
            commonContext.HasBudget = true;
            _logger.Debug("Budget is available, hiding budget fields");
            await HideBudgetFields();
        }
        _logger.Debug("OpenCheckbookNoChecks completed");
    }
    //Open Home and expand checkbook
    public async Task ExpandAndOpenCheckBook(CommonContext commonContext,bool cbOpened = false){
        _logger.Debug("Starting ExpandAndOpenCheckBook");
        if(!cbOpened){
            _logger.Debug("Checkbook not opened, clicking checkbook link");
            await _checkbookLink.ClickAsync();
        }
        _logger.Debug("Clicking view/edit checkbook");
        await _viewEditChecbook.ClickAsync();
        _logger.Debug("Selecting company");
        await this.SelectCompany();
        _logger.Debug("Setting GL codes");
        await SetGlCodes(commonContext);
        if(await IsVisibleAsync(_closeBtnX,1000,5)){
            _logger.Debug("Close button is visible, clicking it");
            await _closeBtnX.ClickAsync();
        }
        _logger.Debug("Checking for departments");
        await HasDepartments(commonContext);
        var bdTxt = commonContext.HasDepartments?4:3;
        _logger.Debug($"Budget text index: {bdTxt}");
        var txt = await budgetTxt(bdTxt).InnerTextAsync()??"";
        _logger.Debug($"Budget text: {txt}");
        if(txt == "Budget"){
            _logger.Debug("Budget is available, setting HasBudget to true");
            commonContext.HasBudget  =true;
            _logger.Debug("Hiding budget fields");
            await HideBudgetFields();
        }
        _logger.Debug("ExpandAndOpenCheckBook completed");
    }

    private async Task HasDepartments(CommonContext commonContext)
    {
        _logger.Debug("Starting HasDepartments check");
        await _departmentDropdown.WaitForAsync();
        _logger.Debug("Department dropdown is ready");
        await _departmentDropdown.EvaluateAsync("node=>node.click()");
        _logger.Debug("Department dropdown clicked");
        await _activeDropdownLoopClick.First.WaitForAsync();
        _logger.Debug("First dropdown item is ready");
        var count = await _activeDropdownLoopClick.CountAsync();
        _logger.Debug($"Department dropdown has {count} items");
        bool hasDepartments = count > 1 ?true:false;
        if(hasDepartments){
           _logger.Debug("Multiple departments found");
           commonContext.DepartmentName =  await _FirstDepartments.InnerTextAsync()??"";
           _logger.Debug($"First department name: {commonContext.DepartmentName}");
        }else{
            _logger.Debug("Only one department found");
            var firstChild = await _activeDropdownLoopClick.First.InnerTextAsync()??"";
            _logger.Debug($"First item text: {firstChild}");
            if(!firstChild.Contains("All Departments")){
                commonContext.DepartmentName =  firstChild;
                _logger.Debug($"Department name set to: {commonContext.DepartmentName}");
            }
        }
        await _departmentDropdown.EvaluateAsync("node=>node.click()");
        _logger.Debug("Department dropdown closed");
        commonContext.HasDepartments=hasDepartments;
        _logger.Debug($"HasDepartments set to: {hasDepartments}");
    }
    private async Task SelectPeriod(string period){
        _logger.Debug($"Selecting period: {period}");
        await _dropdownTreeCb.EvaluateAsync("node=>node.click()");
        _logger.Debug("Period dropdown clicked");
        if(!await IsVisibleAsync(_dropdownTreeCbExpand,1000,5)){
            _logger.Debug("Dropdown tree not expanded, clicking again");
            await _dropdownTreeCb.EvaluateAsync("node=>node.click()");
        }
        _logger.Debug($"Clicking on period: {period}");
        await _dropdownTreeCbTree.Locator(".k-treeview-leaf-text > span:has-text('"+period+"')").ClickAsync(); 
        _logger.Debug("Period selected");
    }
    private async Task FilterGlCode(string glCode,int i){
        _logger.Debug($"Filtering GL code: {glCode} at index {i}");
        await _clickFilter(i).ClickAsync();
        _logger.Debug("Filter clicked");
        await _filterDropdown.ClickAsync();
        _logger.Debug("Filter dropdown clicked");
        await _filterDropdownEquals.ClickAsync();
        _logger.Debug("Equals option selected");
        await _filterInput1.ClearAsync();
        _logger.Debug("Filter input cleared");
        await _filterInput1.FillAsync(glCode);
        _logger.Debug($"Filter input filled with: {glCode}");
        await _filterBtn.ClickAsync();
        _logger.Debug("Filter button clicked");
    }

    private async Task FilterPoNumberPopup(string poNumber){
        _logger.Debug($"Filtering PO number popup for: {poNumber}");
        await _ShowAll.ClickAsync();
        _logger.Debug("Show All clicked");
        Task.Delay(2000).Wait();
        await _clickPopupFilter.ClickAsync();
        _logger.Debug("Popup filter clicked");
        //Task.Delay(2000).Wait();
        //await _ShowAll.ClickAsync();
        Task.Delay(3000).Wait();
        await _filterInput1.ClearAsync();
        _logger.Debug("Filter input cleared");
        await _filterInput1.FillAsync(poNumber);
        _logger.Debug($"Filter input filled with: {poNumber}");
        await _filterBtn.ClickAsync();
        _logger.Debug("Filter button clicked");
    }

    private async Task VerifyTotal(float amt1){
        _logger.Debug($"Verifying total amount: {amt1}");
        float totalCnt = 0;
        foreach(var amt in await _poTotals.AllAsync()){
            var amtText = await amt.TextContentAsync()??"0";
            float amtValue = GetAmount(amtText);
            _logger.Debug($"Found amount: {amtText} -> {amtValue}");
            totalCnt+=amtValue;
        }
        _logger.Debug($"Total calculated: {totalCnt}, expected: {amt1}");
        Assert.That(Math.Round(totalCnt, 2),Is.EqualTo(Math.Round(amt1, 2)));
        _logger.Debug("Total verification passed");
        await _closeBtnX.ClickAsync();
        _logger.Debug("Close button clicked");
    } 

    private async Task FilterPoNGL(int number,string poNumber,string glCode,int i){
        _logger.Debug($"Filtering PO and GL: PO={poNumber}, GL={glCode}, number={number}, i={i}");
        await FilterGlCode(glCode,i);
        _logger.Debug("GL code filtered");
        await _amountText(number).ClickAsync();
        _logger.Debug("Amount text clicked");
        await FilterPoNumberPopup(poNumber);
        _logger.Debug("PO number filtered");
    }
    
    //Verify Amounts on Checkbook grid
     public async Task VerifyPOWithAmounts(CommonContext commonContext, string amountType){
        _logger.Debug($"Starting VerifyPOWithAmounts for amount type: {amountType}");
        int i = commonContext.HasDepartments?2:1;
        _logger.Debug($"Department index: {i}");
        await Task.Run(()=>{});
        foreach(string ps in commonContext.Periods){
            _logger.Debug($"Processing period: {ps}");
            var p = ps;
            if(p.IndexOf("(")!=-1){
                p = p.Split('(', ')')[1];
                _logger.Debug($"Extracted period: {p}");
            }
            await SelectPeriod(p);
            _logger.Debug("Getting PO dictionary");
            Dictionary<string,Dictionary<string,ItemDetails>> poDict = GetDataAsDict(commonContext,true);
            _logger.Debug("Getting invoice dictionary");
            Dictionary<string,Dictionary<string,ItemDetails>> invDict = GetDataAsDict(commonContext,false);
            if(amountType == "planned" || amountType == "committed"){
                _logger.Debug($"Processing {amountType} amounts");
                if(poDict.ContainsKey(p)){
                    _logger.Debug($"Found PO data for period {p}");
                    foreach(var t in poDict[p].Keys){
                        _logger.Debug($"Processing GL code: {t}");
                        int number  = amountType == "planned"?commonContext.HasDepartments?6:5:commonContext.HasDepartments?7:6;
                        _logger.Debug($"Amount column number: {number}");
                        await FilterPoNGL(number,commonContext.PoNumber,t,i);
                        float amount = 0;
                        if(invDict.ContainsKey(p) && invDict[p].ContainsKey(t)){
                            _logger.Debug($"Found invoice data for period {p} and GL {t}");
                            amount-=invDict[p][t].getTotalAmount();
                            _logger.Debug($"Subtracted invoice amount: {amount}");
                        }
                        amount+=poDict[p][t].getTotalAmount();
                        _logger.Debug($"Added PO amount, final amount: {amount}");
                        await VerifyTotal(amount);
                    } 
                } else {
                    _logger.Debug($"No PO data found for period {p}");
                }
            }else{
                _logger.Debug("Processing actual amounts");
                if(invDict.ContainsKey(p)){
                    _logger.Debug($"Found invoice data for period {p}");
                    foreach(var t in invDict[p].Keys){
                        _logger.Debug($"Processing GL code: {t}");
                        int number  = commonContext.HasDepartments?8:7;
                        _logger.Debug($"Amount column number: {number}");
                        await FilterPoNGL(number,commonContext.PoNumber,t,i);
                        _logger.Debug($"Verifying total amount: {invDict[p][t].getTotalAmount()}");
                        await VerifyTotal(invDict[p][t].getTotalAmount());
                    }
                } else {
                    _logger.Debug($"No invoice data found for period {p}");
                }
            }
            
        }
        _logger.Debug("VerifyPOWithAmounts completed");
    }

    //Verify Column Titles
    public async Task VerifyDataGrid(string column)
    {
        _logger.Debug($"Verifying data grid for column: {column}");
        await this.VerifyTitle(column);
        _logger.Debug("Data grid verification completed");
    }

    //Click Dept Dropdown
    public async Task ClickDepartments()
    {
        _logger.Debug("Starting ClickDepartments method");
        int totalGLCodes = await getTotalgridCount("GL");
        _logger.Debug($"Total GL codes: {totalGLCodes}");
        await this._departmentDropdown.WaitForAsync();
        await this._departmentDropdown.ClickAsync();
        _logger.Debug("Department dropdown clicked");
        await this.SelectDepartment(totalGLCodes);
        _logger.Debug("ClickDepartments method completed");
    }

    private async Task SelectDepartment(int totalGLCodes)
    {
        _logger.Debug($"Starting SelectDepartment with totalGLCodes: {totalGLCodes}");
        await this._departmentDropdownClick.WaitForAsync();
        await this._departmentDropdownClick.ClickAsync();
        _logger.Debug("Department dropdown item clicked");
        int count = await getTotalgridCount("GL");
        _logger.Debug($"GL count after department selection: {count}");
        await this._departmentDropdown.ClickAsync();
        await this._departmentDropdownClickOrig.ClickAsync();
        _logger.Debug("SelectDepartment method completed");
    }

    //Preset Filters
    public async Task SelectShowAllVerify()
    {
        _logger.Debug("Starting SelectShowAllVerify method");
        await this._showActiveDropdown.ClickAsync();
        int dropdownCount = await _activeDropdownLoopClick.CountAsync();
        _logger.Debug($"Active dropdown count: {dropdownCount}");
        for(int i =1;i<=dropdownCount;i++){
            if(i==3){
                _logger.Debug($"Skipping dropdown item {i}");
                continue;   
            }
            _logger.Debug($"Clicking dropdown item {i}");
            await _activeDropdownUL.Locator("li:nth-child("+i+")").ClickAsync();
            int count = await getTotalgridCount("GL");
            _logger.Debug($"GL count for dropdown item {i}: {count}");
            //Assert.True(count>0);
            await _showActiveDropdown.ClickAsync();
        }
        _logger.Debug("SelectShowAllVerify method completed");
    }

    //Year View
    public async Task ClickYearView()
    {
        _logger.Debug("Starting ClickYearView method");
        await _yearViewButton.ClickAsync();    
        _logger.Debug("Year view button clicked");
        await _closeBtnX.WaitForAsync();
        await _closeBtnX.ClickAsync();
        _logger.Debug("Close button clicked, ClickYearView method completed");
    }
    
    public async Task VerifyCheckBook(CommonContext commonContext, bool hasReturn = false){
        _logger.Debug($"Starting VerifyCheckBook with hasReturn: {hasReturn}");
        await ExpandAndOpenCheckBook(commonContext);
        Console.WriteLine(hasReturn);
        if(hasReturn){
            _logger.Debug("Verifying PO with planned amounts");
            await VerifyPOWithAmounts(commonContext, "planned");
            if (!string.IsNullOrEmpty(commonContext.PoUrl))
            {
                _logger.Debug($"Navigating to PO URL: {commonContext.PoUrl}");
                await Page.GotoAsync(commonContext.PoUrl,GetPgOptions());
            }
        }else{
            _logger.Debug("Verifying PO with committed amounts");
            await VerifyPOWithAmounts(commonContext, "committed");
        }
        _logger.Debug("VerifyCheckBook method completed");
    }
    //Adding GL Codes selecting diff periods
    public async Task AddGlCodesWithPeriod(CommonContext commonContext){
        _logger.Debug("Starting AddGlCodesWithPeriod method");
        string sMonth = DateTime.Now.ToString("MM");
        string sDay = DateTime.Now.ToString("dd");
        int d  = Int32.Parse(sDay);
        _logger.Debug($"Current date: month {sMonth}, day {sDay}");
        await columnDropDown.ClickAsync();
        var hasBudget = await IsVisibleAsync(bdAmtCheckbox1,1000,5);
        _logger.Debug($"Has budget: {hasBudget}");
        await applyButton.ClickAsync();
        int per = 1;
        //int totalPeriods = await _theadperiods.CountAsync();
        foreach(var row in await _theadperiods.AllAsync()){
            var period = await row.InnerTextAsync();
            period = period.Split('(', ')')[1];
            var periods = period.Split(" - ");
            var yearMonth = periods[0].Split("/");
            var yearMonthEnd = periods[1].Split("/");
            _logger.Debug($"Period {per}: {period}, start month: {yearMonth[0]}, end month: {yearMonthEnd[0]}, end day: {yearMonthEnd[1]}");
            if((yearMonth[0]==sMonth || yearMonthEnd[0] == sMonth) && Int32.Parse(yearMonthEnd[1])>=d){
                _logger.Debug($"Found matching period: {per}");
                break;
            }else{
                per++;
            }
        }
        string baseGl = RandomGenerator.GenerateGlCode(commonContext.GlMask??"####-##",commonContext.GLCodesData);
        _logger.Debug($"Generated base GL code: {baseGl}");
        int totalGLCodes = await getTotalgridCount("GL");
        _logger.Debug($"Total GL codes before adding: {totalGLCodes}");
        Console.WriteLine("per"+ per+""+hasBudget);
        if(hasBudget){
            per = ((per-1) *2)+1;
            _logger.Debug($"Adjusted period for budget: {per}");
        };
        foreach(GlCodes glCodes in commonContext.GLCodesList){
            _logger.Debug("Adding new GL code");
            await _addNewGlCodeButton.ClickAsync();
            string finalVal = baseGl;
            glCodes.GLCode = finalVal;
            glCodes.GLCodeDescription = baseGl;
            _logger.Debug($"Setting GL code: {finalVal}");
            await _glCodeValYear(1,commonContext.HasDepartments?4:3).FillAsync(finalVal);
            await _glCodeDescYear(1,commonContext.HasDepartments?5:4).FillAsync(baseGl);
            if(glCodes.FixedCode == "no"){
                _logger.Debug("Setting fixed code to no");
                await _glCodeFixedYear(1,commonContext.HasDepartments?6:5).ClickAsync();
            }
            if(glCodes.ActiveCode == "no"){
                _logger.Debug("Setting active code to no");
                await _glCodeActiveYear(1,commonContext.HasDepartments?7:6).ClickAsync();
            }
            int k = commonContext.HasDepartments?8+per:7+per;
            _logger.Debug($"Column index for forecast amount: {k}");
            if(commonContext.HasBudget){
                _logger.Debug($"Setting budget amount: {glCodes.BudgetAmount ?? "0"}");
                await _glCodeForcastAmt(1,k).FillAsync(glCodes.BudgetAmount??"0");
                k=k+1;
            }
            _logger.Debug($"Setting forecast amount: {glCodes.ForcastAmount ?? "0"}");
            await _glCodeForcastAmt(1,k).FillAsync(glCodes.ForcastAmount??"0");
            await _glCodeCopyAccross(1,commonContext.HasDepartments?8:7).ClickAsync();
            _logger.Debug("Clicked copy across");
            if(commonContext.HasBudget){
                _logger.Debug("Processing budget copy options");
                await _budgetForcast.ClickAsync();
                await _glCodeCopyAccross(1,commonContext.HasDepartments?8:7).ClickAsync();
                await _onlyBudget.ClickAsync();
                await _glCodeCopyAccross(1,commonContext.HasDepartments?8:7).ClickAsync();
                await _onlyForcast.ClickAsync();
            }
        }
        
        _logger.Debug("Saving GL codes");
        await _saveButton.ClickAsync();
        if(await IsVisibleAsync(hasUnsaved,1000,5)){
            _logger.Debug("Handling unsaved changes dialog");
            await hasUnsaved.ClickAsync();
        }
        if(await IsVisibleAsync(_okBtnConfirm,1000,5)){
            _logger.Debug("Handling first confirmation dialog");
            await _okBtnConfirm.ClickAsync();
        }  
        if(await IsVisibleAsync(_okBtnConfirm,1000,5)){
            _logger.Debug("Handling second confirmation dialog");
            await _okBtnConfirm.ClickAsync();
        }   
        if(commonContext.HasDepartments && !string.IsNullOrEmpty(commonContext.DepartmentName)){
            _logger.Debug($"Adding GL codes to department: {commonContext.DepartmentName}");
            await AddGLCodesToDepartment(commonContext.DepartmentName);
        }                                       
        await _yearViewButton.ClickAsync();
        _logger.Debug("Year view button clicked");
        await _closeBtnX.WaitForAsync();
        await _closeBtnX.ClickAsync();
        _logger.Debug("Close button clicked");
        
        int final = await getTotalgridCount("GL");
        _logger.Debug($"Total GL codes after adding: {final}");
        if(commonContext.HasDepartments && final>totalGLCodes){
            _logger.Debug("GL codes were successfully added");
            commonContext.GLAdded = true;
            await _glCodeRowsNoCopyYear.First.WaitForAsync();
            _logger.Debug($"Filtering for GL code: {baseGl}");
            await FilterAndClick(baseGl, true, commonContext.HasDepartments);
            await _glCodeRowsNoCopyYear.First.WaitForAsync();
            _logger.Debug("Verifying added GL codes");
            foreach(var elem in await _glCodeRowsNoCopyYear.AllAsync()){
                await Assertions.Expect(elem.Locator("td:nth-child(3)")).ToContainTextAsync(baseGl);   
                await Assertions.Expect(elem.Locator("td:nth-child(4)")).ToContainTextAsync(baseGl);  
            }
        }
        _logger.Debug("AddGlCodesWithPeriod method completed");
    }

    //Add New GL Code
    public async Task AddNewGlCodes(CommonContext commonContext){
        _logger.Debug("Starting AddNewGlCodes method");
        string baseGl = RandomGenerator.GenerateGlCode(commonContext.GlMask??"####-##",commonContext.GLCodesData);
        _logger.Debug($"Generated base GL code: {baseGl}");
        int totalGLCodes = await getTotalgridCount("GL");
        _logger.Debug($"Total GL codes before adding: {totalGLCodes}");
        foreach(GlCodes glCodes in commonContext.GLCodesList){
            _logger.Debug("Adding new GL code");
            await _addNewGlCodeButton.ClickAsync();
            string finalVal = baseGl;
            glCodes.GLCode = finalVal;
            glCodes.GLCodeDescription = baseGl;
            commonContext.SpecificGL = finalVal;
            _logger.Debug($"Setting GL code value: {finalVal}");
            await _glCodeVal(1,commonContext.HasDepartments?4:3).FillAsync(finalVal);
            await _glCodeDesc(1,commonContext.HasDepartments?5:4).FillAsync(baseGl);
            commonContext.ExemptGL=finalVal;
            _logger.Debug($"Setting forecast amount: {glCodes.ForcastAmount ?? "1"}");
            await _glCodeForecast(1,commonContext.HasDepartments?6:5).FillAsync(glCodes.ForcastAmount ?? "1");
            if(glCodes.FixedCode == "no"){
                _logger.Debug("Setting GL code as not fixed");
                await _glCodeFixed(1,commonContext.HasDepartments?14:13).ClickAsync();
            }
            if(glCodes.ActiveCode == "no"){
                _logger.Debug("Setting GL code as not active");
                await _glCodeActive(1,commonContext.HasDepartments?15:14).ClickAsync();
            }
        }
        _logger.Debug("Saving GL codes");
        await _saveButton.ClickAsync();
        if(await IsVisibleAsync(hasUnsaved,1000,5)){
            _logger.Debug("Handling unsaved changes dialog");
            await hasUnsaved.ClickAsync();
        }  
        if(await IsVisibleAsync(_okBtnConfirm,1000,5)){
            _logger.Debug("Handling first confirmation dialog");
            await _okBtnConfirm.ClickAsync();
        }
        if(await IsVisibleAsync(_okBtnConfirm,1000,5)){
            _logger.Debug("Handling second confirmation dialog");
            await _okBtnConfirm.ClickAsync();
        }
        if(commonContext.HasDepartments && !string.IsNullOrEmpty(commonContext.DepartmentName)){
            _logger.Debug($"Adding GL codes to department: {commonContext.DepartmentName}");
            await AddGLCodesToDepartment(commonContext.DepartmentName);
        }  
        _logger.Debug("Waiting for GL codes to be processed");
        Task.Delay(10000).Wait();
        int final = await getTotalgridCount("GL");
        _logger.Debug($"Total GL codes after adding: {final}");
        if(commonContext.HasDepartments && final>totalGLCodes){
           _logger.Debug("GL codes were successfully added");
           commonContext.GLAdded = true;
            await _glCodeRows.First.WaitForAsync();
            _logger.Debug($"Filtering for GL code: {baseGl}");
            await FilterAndClick(baseGl, true,commonContext.HasDepartments);
            await _glCodeRows.First.WaitForAsync();
            _logger.Debug("Verifying added GL codes");
            foreach(var elem in await _glCodeRows.AllAsync()){
                await Assertions.Expect(elem.Locator("td:nth-child(3)")).ToContainTextAsync(baseGl);   
                await Assertions.Expect(elem.Locator("td:nth-child(4)")).ToContainTextAsync(baseGl); 
                // await Assertions.Expect(elem.Locator("td:nth-child(5)")).ToContainTextAsync("$20.00");  
                // await Assertions.Expect(elem.Locator("td:nth-child(6)")).ToContainTextAsync("1");
            } 
        }
        _logger.Debug("AddNewGlCodes method completed");
    }

    private async Task FilterAndClick(string  value, bool isEqual, bool _hasDepartments){
        _logger.Debug($"FilterAndClick: value={value}, isEqual={isEqual}, hasDepartments={_hasDepartments}");
        await _clickFilter(_hasDepartments?2:1).ClickAsync();
        if(isEqual){
            await _filterDropdown.ClickAsync();
            await _filterDropdownEquals.ClickAsync();
        }
        await _filterInput1.ClearAsync();
        await _filterInput1.FillAsync(value);
        await _filterBtn.ClickAsync();
        _logger.Debug("Filter applied");
    }

    //Adjust Forecast Amounts
    public async Task AdjustForecastAmts(CommonContext commonContext){
        _logger.Debug("Starting AdjustForecastAmts method");
        #pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
        GlCodes element = commonContext.GLCodesList.Find(e => e.FixedCode == "no");

        #pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.
        if (element!=null && commonContext.HasDepartments && commonContext.GLAdded){
            _logger.Debug($"Adjusting forecast for GL code: {element.GLCode}");
            await FilterAndClick(element.GLCode??"0", true, commonContext.HasDepartments);
            await _glCodeSelector(1).EvaluateAsync("node=>node.click()");
            await _editButton.ClickAsync(); 
            await _adjustByPercentButton.ClickAsync();   
            _logger.Debug("Setting adjustment percentage to 200%");
            await _adjustByPercentInput.FillAsync("200");
            await _caliculateButton.ClickAsync();
            await _saveButton.ClickAsync();
            _logger.Debug("Verifying adjusted forecast amounts");
            await FilterAndClick(element.GLCode??"0", true,commonContext.HasDepartments);
            await _glCodeSelector(1).EvaluateAsync("node=>node.click()");
            await _glCodeRows.First.WaitForAsync();
            foreach(var elem in await _glCodeRows.AllAsync()){
                if(commonContext.HasDepartments){
                    await Assertions.Expect(elem.Locator("td:nth-child(5)")).ToContainTextAsync("2.00"); 
                }else{
                    await Assertions.Expect(elem.Locator("td:nth-child(4)")).ToContainTextAsync("2.00"); 
                }    
            }
        }
        _logger.Debug("AdjustForecastAmts method completed");
    }

    private async Task AdjustJournel(GlCodes element, bool isAdd, bool _hasDepartments){
        _logger.Debug($"AdjustJournel: isAdd={isAdd}, hasDepartments={_hasDepartments}");
        await _glCodeJournelButton(1,_hasDepartments?15:11).ClickAsync(); 
        await _adjustBalanceGridInput.FillAsync("1");
        if(isAdd){
            _logger.Debug("Adding journal entry");
            await _adjustBalanceGridSelect.ClickAsync();
            await _addClick.ClickAsync();
        }
        await _textArea.FillAsync("Avendra");
        await _updateButton.ClickAsync();
        _logger.Debug("Journal adjustment completed");
    }

    //Add New Journal Entry
    public async Task AddJournalEntry(CommonContext commonContext){
        _logger.Debug("Starting AddJournalEntry method");
#pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
        GlCodes element = commonContext.GLCodesList.Find(e => e.FixedCode == "yes");
#pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.
        if (element!=null  && commonContext.HasDepartments && commonContext.GLAdded){
            _logger.Debug($"Adding journal entry for GL code: {element.GLCode}");
            await FilterAndClick(element.GLCode??"0", true, commonContext.HasDepartments);
            await _glCodeSelector(1).EvaluateAsync("node=>node.click()");
            await _editButton.ClickAsync();     
            await AdjustJournel(element,false,commonContext.HasDepartments);
            await AdjustJournel(element,true,commonContext.HasDepartments);
            _logger.Debug("Verifying journal entries");
            foreach(var elem in await _glCodeRows.AllAsync()){
                if(commonContext.HasDepartments){
                    await Assertions.Expect(elem.Locator("td:nth-child(9)")).ToContainTextAsync("0.00"); 
                }else{
                    await Assertions.Expect(elem.Locator("td:nth-child(8)")).ToContainTextAsync("0.00"); 
                }  
            }
            await _glCodeViewButton(1,commonContext.HasDepartments?16:12).ClickAsync();
            await _glTrans.First.WaitForAsync();
            await Assertions.Expect(_glTrans).ToHaveCountAsync(2);   
            await _closeBtnX.ClickAsync();
            _logger.Debug("Checking transaction logs");
            await _viewallLogsButton.ClickAsync();
            await _glTransFull.ClickAsync();
            await _filterInput1.ClearAsync();
            await _filterInput1.FillAsync(element.GLCode??"0");
            await _filterBtn.ClickAsync();
            await _glTrans.First.WaitForAsync();
            await Assertions.Expect(_glTrans).ToHaveCountAsync(2);  
            await _closeBtnX.ClickAsync();
            await _cancelButton.ClickAsync();  
        }
        _logger.Debug("AddJournalEntry method completed");
        await Task.Run(()=>{});
    }

    //Edit Gl codes
    public async Task EditGlCodes(CommonContext commonContext, bool isYearView)
    {
        _logger.Debug($"Starting EditGlCodes method: isYearView={isYearView}");
        #pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
        GlCodes element = commonContext.GLCodesList.Find(e => e.FixedCode == "yes");
        #pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.
        if (element!=null  && commonContext.HasDepartments && commonContext.GLAdded)
        {
            _logger.Debug($"Editing GL code: {element.GLCode}");
            await _editButton.ClickAsync(); 
            if(!isYearView)
            {
                _logger.Debug("Editing in regular view");
                await _glCodeDesc(1,commonContext.HasDepartments?5:4).FillAsync("sss");
                await _glCodeForecast(1,commonContext.HasDepartments?6:5).FillAsync("1");
            }
            else
            {
                _logger.Debug("Editing in year view");
                await _glCodeDescYear(1,commonContext.HasDepartments?5:4).FillAsync("sss");
            }
            await _saveButton.ClickAsync();
        }
        _logger.Debug("EditGlCodes method completed");
        await Task.Run(()=>{});
    }

    //Delete GL Codes
    public async Task DeleteGLCodes(CommonContext commonContext, bool isYearView)
    {
        _logger.Debug($"Starting DeleteGLCodes method: isYearView={isYearView}");
        if(commonContext.HasDepartments && commonContext.GLAdded)
        {
            _logger.Debug("Clearing filters");
            await _clearFilters.ClickAsync();
            #pragma warning disable CS8604 // Possible null reference argument.
            _logger.Debug($"Filtering for GL code description: {commonContext.GLCodesList[0].GLCodeDescription}");
            await FilterAndClick(commonContext.GLCodesList[0].GLCodeDescription, true, commonContext.HasDepartments);
            #pragma warning restore CS8604 // Possible null reference argument.
            var rowView = isYearView?_glCodeRowsNoCopyYear:_glCodeRows;
            await rowView.First.WaitForAsync();
            _logger.Debug("Selecting GL codes for deletion");
            foreach(var elem in await rowView.AllAsync())
            {
                await elem.Locator("td:nth-child(1) input").EvaluateAsync("node=>node.click()");
            }
            await _deleteButton.ClickAsync();
            await _approveBtnConfirm.ClickAsync();
            _logger.Debug("GL codes deleted");
        }
        _logger.Debug("DeleteGLCodes method completed");
        await Task.Run(()=>{});
       
    }

    //UNDelete Gl Codes
    public async Task UnDeleteGLCodes(CommonContext commonContext, bool isYearView)
    {
        _logger.Debug($"Starting UnDeleteGLCodes method: isYearView={isYearView}");
        if(commonContext.HasDepartments && commonContext.GLAdded)
        {
            _logger.Debug("Showing deleted GL codes");
            await _showActiveDropdown.ClickAsync();
            await _activeDropdownDelete.ClickAsync();
            #pragma warning disable CS8604 // Possible null reference argument.
            _logger.Debug($"Filtering for GL code description: {commonContext.GLCodesList[0].GLCodeDescription}");
            await FilterAndClick(commonContext.GLCodesList[0].GLCodeDescription, true,commonContext.HasDepartments);
            #pragma warning restore CS8604 // Possible null reference argument.
            var rowView = isYearView?_glCodeRowsNoCopyYear:_glCodeRows;
            await rowView.First.WaitForAsync();
            _logger.Debug("Selecting GL codes for undelete");
            foreach(var elem in await rowView.AllAsync())
            {
                await elem.Locator("td:nth-child(1) input").EvaluateAsync("node=>node.click()");
            }
            await _unDeleteButton.ClickAsync();
            await _approveBtnConfirm.ClickAsync();
            _logger.Debug("GL codes undeleted");
        }
        _logger.Debug("UnDeleteGLCodes method completed");
        await Task.Run(()=>{});
    }

    public async Task VerifyPlannedAmt(CommonContext commonContext)
    {
        _logger.Debug("Starting VerifyPlannedAmt method");
        var ple = commonContext.HasDepartments?2:1;
        await _GLCodeFilter(ple).ClickAsync();
        _logger.Debug($"Filtering for GL code: {commonContext.GlCodes[0]}");
        await _filterInput1.FillAsync(commonContext.GlCodes[0]);
        if(await _filterBtn.IsEnabledAsync()){
            await _filterBtn.ClickAsync();
            Task.Delay(2000).Wait();
            var pl = commonContext.HasDepartments?6:5;
            _logger.Debug("Clicking planned amount link");
            await _plannedAmtLink(pl).ClickAsync();
            Task.Delay(2000).Wait();
            _logger.Debug("Clicking Show All button");
            await _ShowAll.ClickAsync();
            _logger.Debug("Clicking planned PO filter");
            await _plannedPOFilter.ClickAsync();
            _logger.Debug($"Filling filter input with PO number: {commonContext.PoNumber}");
            await _filterInput1.FillAsync(commonContext.PoNumber);
            _logger.Debug("Clicking filter button");
            await _filterBtn.ClickAsync();
            Task.Delay(2000).Wait();
            _logger.Debug("Locating frame element");
            ILocator frameElementHandle = Page.Locator("#v4Container");
            await frameElementHandle.WaitForAsync();
            _logger.Debug("Getting transaction grid rows");
            var rows = frameElementHandle.Locator("#holdAmountTransactionGrid tbody tr");
            string[] expectedStatuses = { "PO Accepted", "PO Submitted", "PO Declined", "PO Submitted", "PO Recalled", "PO Submitted" };
            _logger.Debug($"Found {await rows.CountAsync()} rows to verify");
            for (int i = 0; i < Math.Min(await rows.CountAsync(), expectedStatuses.Length); i++)
            {
                _logger.Debug($"Checking row {i+1}");
                var statusElement = rows.Nth(i+1).Locator("td:nth-child(5) span");
                var statusText = statusElement != null ? await statusElement.TextContentAsync() : "Status not found";
                _logger.Debug($"Row {i+1} status: {statusText}");
                //var statusMessage = statusText.Trim().Equals(expectedStatuses[i], StringComparison.OrdinalIgnoreCase) ? 
                                   // $"Row {i + 1} status is correct." : 
                                   // $"Row {i + 1} status is incorrect. Expected: {expectedStatuses[i]}, Found: {statusText}";
                
                var amountElement = rows.Nth(i+1).Locator("td:nth-child(6) span");
                var amountText = amountElement != null ? await amountElement.TextContentAsync() : "Amount not found";
                _logger.Debug($"Row {i+1} raw amount: {amountText}");
                var cleanedAmountText = Regex.Replace(amountText?.Trim() ?? string.Empty, @"[^\d.-]", "");
                if (decimal.TryParse(cleanedAmountText, NumberStyles.Any, CultureInfo.InvariantCulture, out var amount))
                {
                    _logger.Debug($"Row {i+1} parsed amount: {amount}");
                    if (commonContext.PeriodsItemPrices.Count >= 2)
                    {
                        bool isNegativeStatus = statusText != null && (statusText.Trim().Equals("PO Accepted", StringComparison.OrdinalIgnoreCase) ||
                                        statusText.Trim().Equals("PO Declined", StringComparison.OrdinalIgnoreCase) ||
                                        statusText.Trim().Equals("PO Recalled", StringComparison.OrdinalIgnoreCase));

                        decimal expectedAmount = isNegativeStatus ? 
                                    -Math.Abs(Convert.ToDecimal(commonContext.PeriodsItemPrices[0])) : 
                                    Math.Abs(Convert.ToDecimal(commonContext.PeriodsItemPrices[0]));
                        _logger.Debug($"Row {i+1} expected amount: {expectedAmount}");
                        /*var amountMessage = amount == expectedAmount ? 
                                        $"Row {i + 1} amount is correct." : 
                                        $"Row {i + 1} amount is incorrect. Expected: {expectedAmount}, Found: {amount}";*/
                    
                    }
                    else
                    {
                        _logger.Debug("Error: 'commonContext.PeriodsItemPrices' does not contain enough elements.");
                        Console.WriteLine("Error: 'commonContext.PeriodsItemPrices' does not contain enough elements.");
                    }
                }
                else
                {
                    _logger.Debug($"Row {i+1} amount is invalid. Found: {amountText}");
                    Console.WriteLine($"Row {i + 1} amount is invalid. Found: {amountText}");
                }
            }
            _logger.Debug("Closing grid");
            await _closeGrid.ClickAsync();
        }
        _logger.Debug("VerifyPlannedAmt method completed");
        await Task.Run(()=>{});
    }

    public async Task VerifyCommittedAmt(CommonContext commonContext)
    {
        _logger.Debug("Starting VerifyCommittedAmt method");
        var pl = commonContext.HasDepartments?7:6;
        _logger.Debug($"Using column position: {pl}");
        await _committedAmtLink(pl).ClickAsync();
        _logger.Debug("Clicked committed amount link");
        Task.Delay(2000).Wait();
        await _ShowAll.ClickAsync();
        _logger.Debug("Clicked Show All button");
        await _committedPOFilter.ClickAsync();
        _logger.Debug("Clicked committed PO filter");
        await _filterInput1.FillAsync(commonContext.PoNumber);
        _logger.Debug($"Filled filter with PO number: {commonContext.PoNumber}");
        if(await _filterBtn.IsEnabledAsync()){
            _logger.Debug("Filter button is enabled, clicking it");
            await _filterBtn.ClickAsync();
            Task.Delay(2000).Wait();
            _logger.Debug("Waiting for frame element");
            ILocator frameElementHandle = Page.Locator("#v4Container");
            await frameElementHandle.WaitForAsync();
            var rows = frameElementHandle.Locator("#committedAmountTransactionGrid tbody tr");
            string[] expectedStatuses = { "PO Closed", "Credit Memo Completed", "Invoice Completed", "PO Accepted" };
            _logger.Debug($"Found {await rows.CountAsync()} rows in the grid");

            for (int i = 0; i < Math.Min(await rows.CountAsync(), expectedStatuses.Length); i++)
            {
                _logger.Debug($"Processing row {i+1}");
                var statusElement = rows.Nth(i+1).Locator("td:nth-child(5) span");
                var statusText = statusElement != null ? await statusElement.TextContentAsync() : "Status not found";
                _logger.Debug($"Row {i+1} status: {statusText}");
                if (statusText != null && (statusText.Contains("Invoice Completed") || statusText.Contains("Credit Memo Completed")))
                {
                    _logger.Debug($"Cleaning status text: {statusText}");
                    statusText = Regex.Replace(statusText, @"\d", "").Trim();
                    _logger.Debug($"Cleaned status text: {statusText}");
                }
                /*var statusMessage = statusText.Trim().Equals(expectedStatuses[i], StringComparison.OrdinalIgnoreCase) ? 
                                    $"Row {i + 1} status is correct." : 
                                    $"Row {i + 1} status is incorrect. Expected: {expectedStatuses[i]}, Found: {statusText}";*/
                
            }
            _logger.Debug("Closing grid");
            await _closeGrid.ClickAsync();
        }
        else {
            _logger.Debug("Filter button is not enabled");
        }
        _logger.Debug("VerifyCommittedAmt method completed");
        await Task.Run(()=>{});
    }

    public async Task VerifyActualizedAmt(CommonContext commonContext)
    {
        _logger.Debug("Starting VerifyActualizedAmt method");
        var pl = commonContext.HasDepartments?8:7;
        _logger.Debug($"Using column position: {pl}");
        await _actualizedAmtLink(pl).ClickAsync();
        _logger.Debug("Clicked actualized amount link");
        Task.Delay(2000).Wait();
        await _actualizedPOFilter.ClickAsync();
        _logger.Debug("Clicked actualized PO filter");
        await _filterInput1.FillAsync(commonContext.PoNumber);
        _logger.Debug($"Filled filter with PO number: {commonContext.PoNumber}");
        if(await _filterBtn.IsEnabledAsync()){
            _logger.Debug("Filter button is enabled, clicking it");
            await _filterBtn.ClickAsync();
            Task.Delay(2000).Wait();
            _logger.Debug("Waiting for frame element");
            ILocator frameElementHandle = Page.Locator("#v4Container");
            await frameElementHandle.WaitForAsync();
            var rows = frameElementHandle.Locator("#actualizedTransactionGrid tbody tr");
            string[] expectedStatuses = { "Credit Memo Completed", "Invoice Completed" };
            _logger.Debug($"Found {await rows.CountAsync()} rows in the grid");

            for (int i = 0; i < Math.Min(await rows.CountAsync(), expectedStatuses.Length); i++)
            {
                _logger.Debug($"Processing row {i+1}");
                var statusElement = rows.Nth(i+1).Locator("td:nth-child(5) span");
                var statusText = statusElement != null ? await statusElement.TextContentAsync() : "Status not found";
                _logger.Debug($"Row {i+1} status: {statusText}");
                if (statusText != null && (statusText.Contains("Invoice Completed") || statusText.Contains("Credit Memo Completed")))
                {
                    _logger.Debug($"Cleaning status text: {statusText}");
                    statusText = Regex.Replace(statusText, @"\d", "").Trim();
                    _logger.Debug($"Cleaned status text: {statusText}");
                }
                /*var statusMessage = statusText.Trim().Equals(expectedStatuses[i], StringComparison.OrdinalIgnoreCase) ? 
                                    $"Row {i + 1} status is correct." : 
                                    $"Row {i + 1} status is incorrect. Expected: {expectedStatuses[i]}, Found: {statusText}";*/
                
            }
            _logger.Debug("Closing grid");
            await _closeGrid.ClickAsync();
        }
        else {
            _logger.Debug("Filter button is not enabled");
        }
        _logger.Debug("VerifyActualizedAmt method completed");
    }
    public async Task ExemptAllGLCodes(){
        _logger.Debug("Starting ExemptAllGLCodes method");
        await _selectAll.ClickAsync();
        _logger.Debug("Clicked select all");
        await _bulkManage.ClickAsync();
        _logger.Debug("Clicked bulk manage");
        
        var labels = await labelLocator.AllAsync();
        _logger.Debug($"Found {labels.Count} labels");
        foreach(var lab in labels){
            await lab.ClickAsync();
            _logger.Debug("Clicked a label");
        }
        
        var users = await assignAllUsers.AllAsync();
        _logger.Debug($"Found {users.Count} users to assign");
        foreach(var lab in users){
            await lab.ClickAsync();
            _logger.Debug("Clicked assign user");
        }
        
        _logger.Debug("Saving changes");
        await sidePaneSave.ClickAsync();
        await _okButton.ClickAsync();
        _logger.Debug("Clicked OK button");
        await sidePaneCancel.ClickAsync();
        _logger.Debug("Clicked cancel button");
        _logger.Debug("ExemptAllGLCodes method completed");
    }

    private async Task CommonSelectPO(bool isRequired){
        _logger.Debug("Starting CommonSelectPO method");
        if(isRequired) {
            _logger.Debug("Clicking Always Exempt PO from Approval");
            await _AlwaysExemptPOfromApproval.ClickAsync();
        }
        else {
            _logger.Debug("Clicking Always Required PO from Approval");
            await _AlwaysRequiredPOfromApproval.ClickAsync();
        }
        _logger.Debug("Clicking select all checkbox of unassigned in PO approval");
        await _selectallcheckboxofunassignedinpoapproval.ClickAsync();
        _logger.Debug("Clicking Double Arrow button in PO approval");
        await _DoubleArrwbtninpoapproval.ClickAsync();
        _logger.Debug("Clicking side pane save");
        await sidePaneSave.ClickAsync();
        _logger.Debug("Clicking side pane cancel");
        await sidePaneCancel.ClickAsync();
        _logger.Debug("Completed CommonSelectPO method");
    }
    public async Task ExemptGLCodeinPOWorkflow()
    {
        _logger.Debug("Starting ExemptGLCodeinPOWorkflow method");
        _logger.Debug("Clicking edit button");
        await _editButton.ClickAsync();
        _logger.Debug("Scrolling to Invoice Approval Column");
        await _InvoiceApprovalColumn.ScrollIntoViewIfNeededAsync();
        _logger.Debug("Getting Off Hyperlink PO text");
        bool isRequired = await _OffHyperlinkPO.InnerTextAsync()=="Required"?true:false;
        _logger.Debug($"Current PO status isRequired: {isRequired}");
        _logger.Debug("Clicking Off Hyperlink PO");
        await _OffHyperlinkPO.ClickAsync();
        _logger.Debug("Calling CommonSelectPO");
        await CommonSelectPO(isRequired);
        _logger.Debug("Getting updated Off Hyperlink PO text");
        var text = await _OffHyperlinkPO.InnerTextAsync();
        _logger.Debug($"New PO status text: {text}");
        Assert.That(text,Is.EqualTo(isRequired?"Exempt":"Required"));
        _logger.Debug("Clicking Off Hyperlink PO again");
        await _OffHyperlinkPO.ClickAsync();
        _logger.Debug("Calling CommonSelectPO with opposite value");
        await CommonSelectPO(!isRequired);
        _logger.Debug("Getting final Off Hyperlink PO text");
        text = await _OffHyperlinkPO.InnerTextAsync();
        _logger.Debug($"Final PO status text: {text}");
        Assert.That(text,Is.EqualTo(isRequired?"Required":"Exempt"));
        _logger.Debug("Completed ExemptGLCodeinPOWorkflow method");
    }

    private async Task CommonSelectInvoice(bool isRequired){
        _logger.Debug("Starting CommonSelectInvoice method");
        if(isRequired) {
            _logger.Debug("Clicking Always Exempt Invoice from Approval");
            await _AlwaysExemptInvoicefromApproval.ClickAsync();
        }
        else {
            _logger.Debug("Clicking Always Required Invoice from Approval");
            await _AlwaysRequiredInvoicefromApproval.ClickAsync();
        }
        _logger.Debug("Clicking select all checkbox of unassigned in invoice approval");
        await _selectallcheckboxofunassignedininvoiceapproval.ClickAsync();
        _logger.Debug("Clicking Double Arrow button in invoice approval");
        await _DoubleArrwbtnininvoiceapproval.ClickAsync();
        _logger.Debug("Clicking side pane save");
        await sidePaneSave.ClickAsync();
        _logger.Debug("Clicking side pane cancel");
        await sidePaneCancel.ClickAsync();
        _logger.Debug("Completed CommonSelectInvoice method");
    }
    public async Task ExemptGLCodeinInvoiceWorkflow()
    {
        _logger.Debug("Starting ExemptGLCodeinInvoiceWorkflow method");
        _logger.Debug("Clicking edit button");
        await _editButton.ClickAsync();
        _logger.Debug("Scrolling to Invoice Approval Column");
        await _InvoiceApprovalColumn.ScrollIntoViewIfNeededAsync();
        _logger.Debug("Getting Off Hyperlink Invoice text");
        bool isRequired = await _OffHyperlinkInvoice.InnerTextAsync()=="Required"?true:false;
        _logger.Debug($"Current Invoice status isRequired: {isRequired}");
        _logger.Debug("Clicking Off Hyperlink Invoice");
        await _OffHyperlinkInvoice.ClickAsync();
        _logger.Debug("Calling CommonSelectInvoice");
        await CommonSelectInvoice(isRequired);
        _logger.Debug("Getting updated Off Hyperlink Invoice text");
        var text = await _OffHyperlinkInvoice.InnerTextAsync();
        _logger.Debug($"New Invoice status text: {text}");
        Assert.That(text,Is.EqualTo(isRequired?"Exempt":"Required"));
        _logger.Debug("Clicking Off Hyperlink Invoice again");
        await _OffHyperlinkInvoice.ClickAsync();
        _logger.Debug("Calling CommonSelectInvoice with opposite value");
        await CommonSelectInvoice(!isRequired);
        _logger.Debug("Getting final Off Hyperlink Invoice text");
        text = await _OffHyperlinkInvoice.InnerTextAsync();
        _logger.Debug($"Final Invoice status text: {text}");
        Assert.That(text,Is.EqualTo(isRequired?"Required":"Exempt"));
        _logger.Debug("Completed ExemptGLCodeinInvoiceWorkflow method");
    }    
}
