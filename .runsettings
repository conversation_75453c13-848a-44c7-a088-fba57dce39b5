<?xml version='1.0' encoding='utf-8'?>
<RunSettings>
	<RunConfiguration>
		<ResultsDirectory>./TestResults</ResultsDirectory>
	</RunConfiguration>
	<NUnit>
		<NumberOfTestWorkers>2</NumberOfTestWorkers>
		<ProcessModel>Multiple</ProcessModel>
	</NUnit>
	<NUnit>
		<TestOutputXml>./TestResults</TestOutputXml>
	</NUnit>
	<TestRunParameters>
		<!-- Environment Configuration -->
		<Parameter name="EnvUrl" value="https://dev2.ibuyefficient.com/" />

		<!-- User Credentials -->
		<Parameter name="UserName" value="DEMOIND/AD1"/>
		<Parameter name="Password" value="awdev1234" />
		<Parameter name="UserName1" value="DEMOIND/NP" />
		<Parameter name="Password1" value="awsdev1234"/>
		<Parameter name="Receiving_Off_UserName" value="DEMOIND/AD3" />
		<Parameter name="Receiving_Off_Password" value="awsdev1234" />
		<Parameter name="UserName_president" value="DEMOEAST/BW" />
		<Parameter name="Password_president" value="awsdev1234" />
		<Parameter name="UserName_stackular" value="DEMOIND/AD1" />
		<Parameter name="Password_stackular" value="awsdev1234" />  
		<Parameter name="Format_UserName" value="DEMOIND/AD3" />
		<Parameter name="Format_Password" value="awsdev1234" />
		<Parameter name="Superadmin_UserName" value="AVENDRA/SUPER" />
		<Parameter name="Superadmin_Password" value="awsdev1234" />
		<Parameter name="Management_UserName" value="ACDC/DEMO" />
		<Parameter name="Management_Password" value="awsdev1234" />

		<!-- Email and SFTP Configuration -->
		<Parameter name="email_pass" value="pgse fwro zczt cruj" />
		<Parameter name="email" value="<EMAIL>" />
		<Parameter name="sftp_url" value="s-18eec2ac299542ffa.server.transfer.us-east-1.amazonaws.com" />
		<Parameter name="sftp_user" value="4686090685" />
		<Parameter name="sftp_pass" value="password" />

		<!-- Browser Configuration -->
		<Parameter name="Browser" value="Chromium" />
		<Parameter name="Headless" value="false" />
		<Parameter name="StartTracing" value="false" />
		<Parameter name="StoreScreenshots" value="true" />

		<!-- Test Organization Parameters -->
		<!-- Uncomment and set values to filter tests -->
		<Parameter name="RunCategory" value="Regression" />
		<Parameter name="RunPriority" value="P0" />
		<Parameter name="SkipStatus" value="Flaky,Deprecated" />
	</TestRunParameters>
</RunSettings>
