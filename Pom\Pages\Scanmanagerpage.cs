using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Net.Http.Headers;
using System;
using System.Text.RegularExpressions;
using System.Diagnostics;
using System.Threading;
using Microsoft.Playwright;
using NUnit.Framework;
using TechTalk.SpecFlow;
using SpecFlowProject.Utils;
using SpecFlowProject.BusinessObjects;
using SpecFlowProject.Hooks;
using Spire.Pdf;
using Spire.Pdf.General.Find;
using Newtonsoft.Json.Linq;
namespace SpecFlowProject.Pom.Pages;

public class ScanManagerPage : Base
{
    public ScanManagerPage(IPage page) : base(page) { 
        _logger.Debug("ScanManagerPage initialized");
    }
    private ILocator receiving => Page.Locator("h2:has-text('Receiving')");
    private ILocator scanManager => Page.Locator("a[href='/Buyer/Receiving/ScanManagerReceipts']");
    private ILocator company => _iframe.Locator(".__selectCompanyDropdown");
     private ILocator companySelect => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(1)");
    private ILocator companyStackular => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(1)");
    private ILocator CompanyImportTest => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(2)");
    private ILocator companyisStackular => Page.FrameLocator("#v4Container").Locator(".k-list ul li:nth-child(1)");
    private ILocator companyName => _iframe.Locator(".__selectCompanyDropdown span span");
    private ILocator fileNameFilterBtn => _iframe.Locator(".k-grid-header th:nth-child(3) div span");
    private ILocator filterDropdown => _iframe.Locator(".k-filter-menu .k-dropdownlist");
    private ILocator filterDropdownEquals => _iframe.Locator(".k-list ul li:nth-child(3)");
    private ILocator filterInput => _iframe.Locator(".k-filter-menu input");
    private ILocator filterButton => _iframe.Locator(".k-filter-menu button:has-text('Filter')");
    private ILocator previewButton => _iframe.Locator(".k-grid-table tr:nth-child(1) td:nth-child(2) i:nth-child(1)");
    private ILocator renameRow => _iframe.Locator(".k-grid-table tr:nth-child(1) td:nth-child(3) span");
    private ILocator renameFile => _iframe.Locator(".k-grid-table tr:nth-child(1) td:nth-child(3) input:nth-child(1)");
    private ILocator dateColumn => _iframe.Locator(".k-grid-table tr:nth-child(1) td:nth-child(5)");
    private ILocator clearFilterBtn => Page.FrameLocator("#v4Container").GetByRole(AriaRole.Button, new() { Name = "Clear Filters" });
    private ILocator uploadButton => Page.FrameLocator("#v4Container").GetByRole(AriaRole.Button, new() { Name = "Upload Document" });
    private ILocator uploadButtonInOCR => Page.FrameLocator("#v4Container").GetByRole(AriaRole.Button, new() { Name = "Upload and Scan File(s)" });
    private ILocator uploadPdf => _iframe.Locator("input[title='No files selected']");
    private ILocator _errorPopup => _iframe.Locator(".k-window-content td:nth-child(2)");
    private ILocator uploadSuccessMsg => _iframe.Locator(".k-window-content.k-dialog-content>div:nth-child(1)");
    private ILocator uploadClose => _iframe.Locator(".flex.justify-end.gap-2>button");
    private ILocator invalidFormatPopup => _iframe.Locator(".k-window-content.k-dialog-content .k-grid-container table tr");
    private ILocator deleteButton => Page.FrameLocator("#v4Container").GetByRole(AriaRole.Button, new() { Name = "Delete" });
    private ILocator deleteConfirmation => _iframe.Locator(".flex.justify-end.gap-2 button:nth-child(1)");
    private ILocator deleteRow => _iframe.Locator(".k-grid-container tr:nth-child(1) td:nth-child(1) input");
    private ILocator deleteBulk => _iframe.Locator(".k-grid-header-wrap th:nth-child(1) input");
    private ILocator gridContainer => _iframe.Locator(".k-grid-container");
    private ILocator Recordstab => _iframe.GetByRole(AriaRole.Button, new() { Name = "Records" });
    private ILocator OCRqueuebtn => _iframe.GetByRole(AriaRole.Button, new() { Name = "OCR Queue" });
    private ILocator selectarow => _iframe.Locator("//table[@class='k-grid-table']//tr[1]//td[1]//input");
    private ILocator ReassigntoCmpybtn  => Page.FrameLocator("#v4Container").GetByRole(AriaRole.Button, new() { Name = "Reassign to Company" });
    private ILocator Uploadbtnpopup => Page.FrameLocator("#v4Container").Locator(".k-dialog").GetByRole(AriaRole.Button, new() { Name = "Upload", Exact = true });
    private ILocator FirstrowInTble => _iframe.Locator(".k-grid-table tbody tr:first-child");
    private ILocator Firstrow => _iframe.Locator("//table[@class='k-grid-table']//tr[1]//td[3]");
    private ILocator reassigndropdown => _iframe.Locator(".flex.flex-row.items-center .k-dropdownlist button span");
    private ILocator Reassignbtn => Page.FrameLocator("#v4Container").Locator(".k-dialog").GetByRole(AriaRole.Button, new() { Name = "Reassign", Exact = true });

    private ILocator Okbtn => Page.FrameLocator("#v4Container").Locator(".k-dialog").GetByRole(AriaRole.Button, new() { Name = "OK", Exact = true });

    private ILocator PresetFltrbtn => Page.FrameLocator("#v4Container").Locator(".k-widget #view-drop-down-list");
    private ILocator ShowAll => _iframe.Locator(".k-list ul li:nth-child(1)");
    private ILocator ShowActive => _iframe.Locator(".k-list ul li:nth-child(2)");
    private ILocator ShowAttached => _iframe.Locator(".k-list ul li:nth-child(3)");
    private ILocator ShowUnattached => _iframe.Locator(".k-list ul li:nth-child(4)");
    private ILocator ShowDeleted => _iframe.Locator(".k-list ul li:nth-child(5)");
    private ILocator Undeletebtn => Page.FrameLocator("#v4Container").GetByRole(AriaRole.Button, new() { Name = "Undelete", Exact = true });
    private ILocator UndeleteConformationPopup => _iframe.Locator("//div[@class='k-window-title k-dialog-title']");
    private ILocator UndeleteConformation => _iframe.Locator(".flex.justify-end.gap-2 button:nth-child(1)");
    private ILocator RunOCRScanbtn => Page.FrameLocator("#v4Container").GetByRole(AriaRole.Button, new() { Name = "Run OCR Scan", Exact = true });
    private ILocator RunScanconfrmation => Page.FrameLocator("#v4Container").Locator(".k-dialog").GetByRole(AriaRole.Button, new() { Name = "Run Scan", Exact = true });
    private ILocator Logoffbtn => Page.Locator("a:has-text('Log Off')");
    private ILocator PresetFilterQueuetab => _iframe.Locator("//div[@class='flex items-center justify-between w-full']//span//span[@class='k-button-icon k-icon k-i-caret-alt-down']");
    private ILocator ShowAllocrQueue => _iframe.Locator(".k-list ul li:nth-child(1)");
    private ILocator OCRScanReviewCount => Page.Locator("#dashboard-notifications a:nth-child(4)>div>div:nth-child(1)");
    private ILocator ReviewCount => _iframe.Locator(".flex.items-center.justify-between.w-full span span span");
    private ILocator ShowInProgressfilter => _iframe.Locator(".k-list ul li:nth-child(2)");
    private ILocator ShowNeedsReviewFilter => _iframe.Locator(".k-list ul li:nth-child(3)");
    private ILocator ShowFailedFilter => _iframe.Locator(".k-list ul li:nth-child(4)");
    private ILocator OCRQueueMsg => _iframe.Locator("//div[@class='flex flex-col [&>span]:font-medium']");
    private ILocator SourcecolAttachment => _iframe.Locator("//table[@class='k-grid-table']//tr[1]//td[7][text()='Local Upload']");
    private ILocator SourcecolQueuetab => _iframe.Locator("//table[@class='k-grid-table']//tr[1]//td[6][normalize-space(text()) = 'Local Upload (OCR)']");
    private ILocator gridClick => _iframe.Locator("#master-center-pane");
    private ILocator RowNAContent => _iframe.Locator("#homeNavItems .iconifiedHome)");
    private ILocator ShowUnattacchedText => _iframe.Locator(".flex.items-center.gap-1.flex-wrap .k-dropdownlist span span");
    private ILocator toastermsg => _iframe.Locator("div:has-text('File(s) are Deleted successfully')").Nth(0);
    private ILocator Deletedtag => _iframe.Locator("//table[@class='k-grid-table']//tr[1]//td[3]//span[2]");
    private ILocator InvoiceLink => _iframe.Locator("//table[@class='k-grid-table']//tr[1]//td[4]//button");
    private ILocator HomePage => _iframe.Locator(".ui-accordion h2:nth-child(1)");
    private ILocator Dashboard => _iframe.Locator("#homeNavItems .iconifiedHome");
    private ILocator Unattachcount => Page.Locator("#dashboard-notifications a:nth-child(3)>div>div:nth-child(1)");
    private ILocator RecordCount => _iframe.Locator(".k-pager-info.k-label");
    private ILocator NotNowButton => _iframe.GetByRole(AriaRole.Button, new() { Name = "Not Now" });
    private ILocator UnattachedInvoiceTileTextValidation => Page.Locator("#dashboard-notifications a:nth-child(3) >div>div:nth-child(2)");
    private ILocator OCRScanReviewTileTextValidation => Page.Locator("#dashboard-notifications a:nth-child(4)>div>div:nth-child(2)");
    private ILocator DevEmail => _iframe.Locator("//div[@class='[&>p]:text-sm [&>p]:mb-2'] //p[4]//span");
    private ILocator Reviewbtn => _iframe.Locator(".k-grid-table tbody tr:nth-child(1) td:last-child button");
    private ILocator ReviewScanPannel => _iframe.Locator(".slide-pane__content .flex .font-bold");
    private ILocator SearchDocbtn => _iframe.Locator(".flex.flex-wrap.mt-2 div:nth-child(2) button");
    private ILocator POTextField => _iframe.Locator("//input[@value='33318347671']");
    private ILocator DocTextField => _iframe.Locator(".flex.flex-col.gap-2.justify-between div:nth-child(4) div:nth-child(2) span input");

    private ILocator Supplier =>_iframe.Locator(".slide-pane__content .k-dropdownlist");

    private ILocator SupplierName => Page.FrameLocator("#v4Container").Locator("div.k-list-content ul li:nth-child(2) label:first-child");

    private ILocator Abandonbtn => _iframe.Locator(".flex.flex-wrap.mt-2 div:nth-child(4) button");
    private ILocator SelectPOpopup => _iframe.Locator(".k-window-titlebar.k-dialog-titlebar div:nth-child(2) button span");
    private ILocator POFieldClear => _iframe.Locator(".flex.flex-col.gap-2.justify-between div:nth-child(3) div:nth-child(2) span input");
    private ILocator DocumentField => _iframe.Locator(".flex.flex-col.gap-2.justify-between div:nth-child(4) div:nth-child(2) span input");
    private ILocator Mergebtn => _iframe.Locator(".slide-pane__content button:has-text('Merge')");

    private ILocator Cancelbtn => _iframe.Locator(".slide-pane__content button:has-text('Cancel')");
    private ILocator MergeRecodPopup => _iframe.Locator(".k-window-title.k-dialog-title");
    private ILocator MergeConfirmbtn => _iframe.Locator(".k-dialog-buttongroup .flex button:nth-child(1)");
    private ILocator MergeDocNo => _iframe.Locator(".k-widget .k-window-content span");
    private ILocator DeleteOCRbutMoveimagebtn => _iframe.Locator(".k-widget.k-window.k-dialog .flex button:nth-child(2) span");
    private ILocator DeleteOCRDeleteimagebtn => _iframe.Locator(".k-widget.k-window.k-dialog .flex button:nth-child(1) span");
    private ILocator ConfirmDeletebtn => _iframe.Locator(".k-widget.k-window.k-dialog .flex button:nth-child(1) span");
    private ILocator AbandonDeletepopup => _iframe.Locator(".k-widget.k-window .k-window-titlebar .k-window-title");
    private ILocator UploadandScanFilesbtn => _iframe.Locator(".k-dialog-buttongroup .flex div:nth-child(1) button span");

    private ILocator footerCheckOCR => Page.Locator("#footerContainer>.scan-info");
    private ILocator CreateButton => _iframe.Locator(".flex.mt-2 div:nth-child(1) button");
    private ILocator _invPopupBtn => _iframe.Locator(".k-window-actions.k-dialog-actions button");
    private ILocator _invoiceNumber => _iframe.Locator("div > div > div.grid> div:nth-child(1) > div > span > span > input");
    private ILocator _invoiceNumberOCRBadge => _iframe.Locator("p:has-text('Invoice #') + span div:has-text('OCR')");
    public ILocator RecordPONum => _iframe.Locator("div > div:nth-child(1) > div > span:nth-child(135)");
    public ILocator RecordExactMatchPONum => _iframe.Locator("div > div:nth-child(1) > div > span:nth-child(30)");
    public ILocator DocPONum => _iframe.Locator("div:nth-child(2) > div > p:nth-child(2)");
    private ILocator DocumentOCRBadge => _iframe.Locator("p:has-text('PO #') + span >.ml-auto div:has-text('OCR')");
    private ILocator RecordInvoiceDate => _iframe.Locator("div > span:nth-child(21)");
    private ILocator RecordExactMatchInvoiceDate => _iframe.Locator("div > span:nth-child(9)");
    private ILocator DocInvoiceDate => _iframe.Locator("div:nth-child(6) > div > span input");
    private ILocator DateOCRBadge => _iframe.Locator("div:nth-child(6) > div > p > div > div");
    private ILocator DocDiscount => _iframe.Locator("div:nth-child(13) > div input ");
    private ILocator RecordDiscount => _iframe.Locator("div > span:nth-child(119)");
    private ILocator RecordDiscountExactMatch => _iframe.Locator("div > span:nth-child(66)");
    private ILocator DocShipping => _iframe.Locator("div:nth-child(14) > div input");
    private ILocator RecordShipping => _iframe.Locator("div > span:nth-child(117)");
    private ILocator RecordShippingExactMatch => _iframe.Locator("div > span:nth-child(62)");
    private ILocator DocTax => _iframe.Locator("div:nth-child(15) > div input");
    private ILocator DocGrandTotal => _iframe.Locator("div:nth-child(16) > div input");
    private ILocator RecordGrandTotal => _iframe.Locator("div > span:nth-child(125)");
    private ILocator RecordGrandTotalExactMatch => _iframe.Locator("div > span:nth-child(74)");
    private ILocator RecordTax => _iframe.Locator("div > span:nth-child(113)");
    private ILocator RecordTaxExactMatch => _iframe.Locator("div > span:nth-child(70)");
    private ILocator GrandTotalOCRBadge => _iframe.Locator("div:nth-child(16) > div > div > div");
    private ILocator DocTaxOCRBadge => _iframe.Locator("div:nth-child(15) > div > div > div");
    private ILocator DocShippingOCRBadge => _iframe.Locator("div:nth-child(14) > div > div > div");
    private ILocator DocDiscountOCRBadge => _iframe.Locator("div:nth-child(13) > div > div > div");
    private ILocator DocLineItemQty => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(2) td:nth-child(6) input");
    private ILocator DocLineItemQtyExactMatch => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(1) td:nth-child(6) input");
    private ILocator RecordLineItemQty => _iframe.Locator("div > span:nth-child(163)");
    private ILocator RecordLineItemQtyExactMatch => _iframe.Locator("div > span:nth-child(54)");
    private ILocator DocLineItemQtyOCRBadge => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(2) td:nth-child(6) > div > div > div");
    private ILocator DocLineItemQtyExactOCRBadge => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(1) td:nth-child(6) > div > div > div");
    private ILocator DocLineItemName => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(2) td:nth-child(8) input");
    private ILocator DocLineItemNameExactMatch => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(1) td:nth-child(8) span");
    private ILocator RecordItemName => _iframe.Locator("div > span:nth-child(157)");
    private ILocator RecordItemNameExaxtMatch => _iframe.Locator("div > span:nth-child(48)");
    private ILocator DocLineItemNameOCRBadge => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(2) td:nth-child(8) > div > div > div");
    private ILocator DocLineItemNameExactOCRBadge => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(1) td:nth-child(8) > div > div > div");
    private ILocator DocLineItemDesc => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(2) td:nth-child(9) input");
    private ILocator DocLineItemDescExactMatch => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(1) td:nth-child(9) span");
    private ILocator RecordItemDesc => _iframe.Locator("div > span:nth-child(159)");
    private ILocator RecordItemDescExactMatch => _iframe.Locator("div > span:nth-child(50)");
    private ILocator DocLineItemDescOCRBadge => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(2) td:nth-child(9) > div > div > div");
    private ILocator DocLineItemDescExactMatchOCRBadge => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(1) td:nth-child(9) > div > div > div");
    private ILocator DocLineItemUOM => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(2) td:nth-child(11) input");
    private ILocator DocLineItemUOMExactMatch => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(1) td:nth-child(11) input");
    private ILocator RecordItemUOM => _iframe.Locator("div > span:nth-child(161)");
    private ILocator RecordItemUOMExactMatch => _iframe.Locator("div > span:nth-child(52)");
    private ILocator DocLineItemUOMOCRBadge => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(2) td:nth-child(11) > div > div > div");
    private ILocator DocLineItemUOMExactMatchOCRBadge => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(1) td:nth-child(11) > div > div > div");
    private ILocator DocLineItemPrice => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(2) td:nth-child(12) input");
    private ILocator DocLineItemPriceExactMatch => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(1) td:nth-child(12) input");
    private ILocator RecordItemPrice => _iframe.Locator("div > span:nth-child(165)");
    private ILocator RecordItemPriceExactMatch => _iframe.Locator("div > span:nth-child(56)");
    private ILocator DocLineItemPriceOCRBadge => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(2) td:nth-child(12) > div > div > div");
    private ILocator DocLineItemPriceExactMatchOCRBadge => _iframe.Locator("#invoiceLineItemGrid tr:nth-child(1) td:nth-child(12) > div > div > div");
    private ILocator UploadAndScanBtn => _iframe.Locator("div.k-actions > div > div:nth-child(1) > button");

    private ILocator mainInvoice => _iframe.Locator("main.flex");
    private ILocator invoiceDetails => mainInvoice.Locator(">div:nth-child(3)>div:last-child");

    private ILocator invoiceDetailsInput => invoiceDetails.Locator("input");

    private ILocator invoiceDetailsReadonly => invoiceDetails.Locator(".p-1 p[title]");

    private ILocator invoiceDetailsOcrTooltip => invoiceDetails.Locator("div[data-tooltip-html]");

    private ILocator invoiceLineItems => mainInvoice.Locator(">div:nth-child(5)>div:last-child .k-grid-table tbody tr");

    private ILocator scanPanelButtons => _iframe.Locator(".k-pane.k-scrollable button.text-sm");

    private ILocator _receivedDate(int i) => _iframe.Locator("div:nth-child("+i+") > div > span > span > button > span");
    private ILocator _receivedDateSelect => _iframe.Locator(".k-calendar-nav-today");

    private ILocator _itemDesc(int i) => _iframe.Locator(".k-widget >.k-pane >div>main >div:nth-child(4) table tbody tr td:nth-child("+i+") input");

    private ILocator _itemTotal(int i) => _iframe.Locator(".k-widget >.k-pane >div>main>div:nth-child(4) table tbody tr.k-master-row:last-child td:nth-child("+i+")");
    private ILocator _itemTotalInput => _iframe.Locator(".k-widget >.k-pane >div>main >div:nth-child(3) div:nth-child(2) .grid input").Last;

    private ILocator _confirmPopupBtn => _iframe.Locator(".flex.justify-end.gap-2 button:nth-child(1)");

    //Expand Receiving Module
    public async Task ExpandReceiving()
    {
        _logger.Debug("Attempting to expand Receiving module");
        if(!await IsVisibleAsync(receiving,1000,5)){
            _logger.Error("Receiving Module is not visible for the user");
            Assert.Fail("Receiving Module is not visible for the user");
        }
        await receiving.ClickAsync();
        _logger.Debug("Receiving module expanded successfully");
    }
    //Verify Search Document button
    public async Task SearchDocPONotFound(CommonContext commonContext,Context context)
    {
        if(await ReviewPanelIsReadyAsync()){
            var Doccontent = (await DocumentField.TextContentAsync())?.Trim() ?? string.Empty;
            var (newPage, actualText) = await VerifyAndHandleFrameAsync(Page, SearchDocbtn, "#v4Container",commonContext,context);
            Assert.That(Doccontent, Is.EqualTo(actualText));
            /*await _nextBtn.ClickAsync();
            await _iframe.Locator(".k-window-content .k-grid-table tr:nth-child(1) td:nth-child(1)").ClickAsync();
            await _iframe.Locator(".flex.justify-center.gap-2.mb-1 button:nth-child(1) span").ClickAsync();
            //Verify Existing OCR Document popup
            await VerifyAndCloseOCRDocumentPopup(_iframe);*/
        }else{
            Assert.Fail("Review panel is not ready");
        }
    }

    //Expand Home Module and click on Dashboard
    public async Task ExpandHome()
    {
        _logger.Debug("Expanding Home module and clicking on Dashboard");
        //await HomePage.ClickAsync();
        await Dashboard.ClickAsync();
        _logger.Debug("Dashboard clicked, clicking Not Now button");
        await NotNowButton.ClickAsync();
        _logger.Debug("Home module expanded successfully");
        //await Unattachcount.ClickAsync();
    }

    public async Task<bool> HasOCr()
    {
        _logger.Debug("Checking if OCR is enabled");
        var text = await footerCheckOCR.InnerTextAsync() ?? "";
        var hasOcr = text.Contains("OCR: ON");
        _logger.Debug("OCR status: {Status}", hasOcr ? "ON" : "OFF");
        return hasOcr;
    }
    //Get the count of the unattached records present in the tile is exact match to the table grid
    public async Task TileCountMatchesTableRecords()
    {
        //Extract the text of the tile in dashboard
        string UnattacchedInvoiceTextValidation = await UnattachedInvoiceTileTextValidation.TextContentAsync()??"";
        Assert.That(UnattacchedInvoiceTextValidation, Is.EqualTo("Unattached Invoice/Credit Images"));
        //var await Unattachcount.ClickAsync();
        string UnattachtileText = await Unattachcount.TextContentAsync()??"";

        //  Click on the tile to navigate to Records tab
        await Unattachcount.ClickAsync();
        // Verify the filter is set to 'Show Unattached'
        string filterValue = await ShowUnattacchedText.TextContentAsync()??"";
        Assert.That(filterValue, Is.EqualTo("Show Unattached"));
        await WaitUntilLoaderHidden();
        await Task.Delay(4000);
        int divText = await getTotalgridCount("Records");
        Assert.That(UnattachtileText.Trim(), Is.EqualTo(divText+""));
    }
    //Click on ScanManager
    public async Task ScanmanagerPage()
    {
        _logger.Debug("Navigating to Scan Manager page");
        await scanManager.ClickAsync();
        _logger.Debug("Scan Manager page loaded");
    }

    //Select Company
#pragma warning disable CS0108 // Member hides inherited member; missing new keyword
    public async Task SelectCompany()
#pragma warning restore CS0108 // Member hides inherited member; missing new keyword
    {
        try
        {
            if (await IsVisibleAsync(company,1000,5))
            {
                await company.ClickAsync();
                await companySelect.ClickAsync();
            }
        }
        catch (Exception) { }
    }

    //Select Stackular Company
    public async Task SelectStackularCompany()
    {
        _logger.Debug("Attempting to select Stackular company");
        if (await IsVisibleAsync(company,1000,5))
        {
            _logger.Debug("Company dropdown is visible, clicking");
            await company.ClickAsync();
            _logger.Debug("Selecting Stackular company from dropdown");
            await companyStackular.ClickAsync();
            _logger.Debug("Stackular company selected successfully");
        }
        else
        {
            _logger.Debug("Company dropdown not visible, skipping selection");
        }
    }

    //need to select when Stackular is the 3rd Company
    public async Task SelectisStackularCompany()
    {
        _logger.Debug("Attempting to select Stackular (3rd position) company");
        if (await IsVisibleAsync(company,1000,5))
        {
            _logger.Debug("Company dropdown is visible, clicking");
            await company.ClickAsync();
            _logger.Debug("Selecting Stackular (3rd position) company from dropdown");
            await companyisStackular.ClickAsync();
            _logger.Debug("Stackular (3rd position) company selected successfully");
        }
        else
        {
            _logger.Debug("Company dropdown not visible, skipping selection");
        }
    }

    //need to select CompanyImportTest company from dropdown
    public async Task SelectCompanyImportTest()
    {
        _logger.Debug("Attempting to select CompanyImportTest");
        if (await IsVisibleAsync(company,1000,5))
        {
            _logger.Debug("Company dropdown is visible, clicking");
            await company.ClickAsync();
            _logger.Debug("Selecting CompanyImportTest from dropdown");
            await CompanyImportTest.ClickAsync();
            _logger.Debug("CompanyImportTest selected successfully");
        }
        else
        {
            _logger.Debug("Company dropdown not visible, skipping selection");
        }
    }

    //Verify Stackluar company is displaying or not
    public async Task VerifyStackluarCompany()
    {
        _logger.Debug("Verifying Stackular company display");
        if (await IsVisibleAsync(company,1000,5))
        {
            _logger.Debug("Company dropdown is visible, checking class");
            await Assertions.Expect(companyName).ToHaveClassAsync("k-input-value-text");
            _logger.Debug("Stackular company verification completed");
        }
        else
        {
            _logger.Debug("Company dropdown not visible, verification skipped");
        }
    }

    //Verify Avendra demo independant company is displaying or not
    public async Task VerifyAvendraCompany()
    {
        _logger.Debug("Verifying Avendra company display");
        await Assertions.Expect(companyName).ToContainTextAsync("Aven");
        _logger.Debug("Avendra company verification completed");
    }

    //Verify the table grid header names
    public async Task VerifyDataGrid(string column)
    {
        _logger.Debug("Verifying data grid header: {Column}", column);
        await this.VerifyTitle(column);
        _logger.Debug("Data grid header verification completed");
    }

    //Upload .pdf file

    public async Task UploadFile(string filename)
    {
        _logger.Debug("Uploading file: {Filename}", filename);
        await uploadButton.ClickAsync();
        _logger.Debug("Upload button clicked, setting input files");
        await uploadPdf.SetInputFilesAsync(filename);
        _logger.Debug("File selected for upload");

        // Check if the PDF permission is enabled and if the file is a PDF
        bool isPdfPermissionOn = await IsUploadButtonDisabled();
        _logger.Debug("PDF permission status: {Status}", isPdfPermissionOn ? "ON" : "OFF");
        
        // Click the upload button if it's visible
        if(!isPdfPermissionOn){
            if (await IsVisibleAsync(Uploadbtnpopup, 1000, 1))
            {
                _logger.Debug("Upload button popup is visible, clicking");
                await Uploadbtnpopup.ClickAsync();
                // Wait a moment for any messages to appear
                _logger.Debug("Waiting for messages to appear");
                await Task.Delay(1000);
            }
        }

        // Check for the appropriate message based on permission and file type
        if (isPdfPermissionOn)
        {
            // If PDF permission is ON and file is not PDF, we should see an error message
            _logger.Debug("PDF permission is ON, checking for file format error message");

            // Wait for the invalid format popup to be visible
            if (await IsVisibleAsync(invalidFormatPopup, 5000, 3))
            {
                _logger.Debug("File format error message displayed as expected");
                // Get the error message text for logging
                string errorMessage = await invalidFormatPopup.TextContentAsync() ?? "";
                _logger.Debug("Error message: {Message}", errorMessage);

                // Check if the error message contains "File format is not supported"
                if (errorMessage.Contains("File format is not supported"))
                {
                    // Close the dialog
                    _logger.Debug("Closing error dialog");
                    await uploadClose.ClickAsync();

                    // Fail the test case as requested
                    _logger.Error("Test failed: 'File format is not supported' error was displayed");
                    Assert.Fail("Test failed: 'File format is not supported' error was displayed. Further test execution stopped as requested.");
                }
            }
            else
            {
                // If we don't see the error message, that's unexpected
                _logger.Error("Expected file format error message was not displayed");
                Assert.Fail("Expected file format error message was not displayed when uploading non-PDF file with PDF-only permission.");
            }
        }
        else
        {
            // If PDF permission is OFF or file is PDF, we should see success message
            _logger.Debug("PDF permission is OFF or file is PDF, checking for upload success message");
            await Assertions.Expect(uploadSuccessMsg).ToBeVisibleAsync();
            _logger.Debug("Upload success message is visible");
        }

        // Close the dialog regardless of success or error
        _logger.Debug("Closing upload dialog");
        await uploadClose.ClickAsync();
        _logger.Debug("File upload process completed");
    }
    public async Task UploadPdf()
    {
        _logger.Debug("Uploading PDF file: iBuyEfficient_Receipt.pdf");
        await UploadFile(".\\UploadFiles\\iBuyEfficient_Receipt.pdf");
        _logger.Debug("PDF file upload completed");
    }

    //Upload .png file
    public async Task UploadPng()
    {
        _logger.Debug("Uploading PNG file: image.png");
        await UploadFile(".\\UploadFiles\\image.png");
        _logger.Debug("PNG file upload completed");
    }

    //Upload .jpg file
    public async Task UploadJpg()
    {
        _logger.Debug("Uploading JPG file: file_example.jpg");
        await UploadFile(".\\UploadFiles\\file_example.jpg");
        _logger.Debug("JPG file upload completed");
    }
    //Upload .jpeg file
    public async Task UploadJpeg()
    {
        _logger.Debug("Uploading JPEG file: sample.jpeg");
        await UploadFile(".\\UploadFiles\\sample.jpeg");
        _logger.Debug("JPEG file upload completed");
    }

    //Upload .bmp file
    public async Task UploadBmp()
    {
        _logger.Debug("Uploading BMP file: sample.bmp");
        await UploadFile(".\\UploadFiles\\sample.bmp");
        _logger.Debug("BMP file upload completed");
    }

    //Upload .tiff file
    public async Task UploadTiff()
    {
        _logger.Debug("Uploading TIFF file: Laptop.tiff");
        await UploadFile(".\\UploadFiles\\Laptop.tiff");
        _logger.Debug("TIFF file upload completed");
    }

    //Upload .xlsx file
    public async Task UploadXlsx()
    {
        _logger.Debug("Uploading XLSX file: viewreceipts.xlsx");
        await uploadButton.ClickAsync();
        try
        {
            _logger.Debug("Setting input files for XLSX upload");
            await uploadPdf.SetInputFilesAsync(".\\UploadFiles\\viewreceipts.xlsx");
            if (await IsVisibleAsync(Uploadbtnpopup,1000,1))
            {
                _logger.Debug("Upload button popup is visible, clicking");
                await Uploadbtnpopup.ClickAsync();
                _logger.Debug("Upload button clicked");
            }
            _logger.Debug("XLSX file upload completed");
        }
        catch (Exception ex)
        {
            _logger.Warning("Exception occurred during XLSX upload: {ErrorMessage}", ex.Message);
        }
    }
    //
    //Upload .xlsx file in OCR using Upload and Scan file button
    public async Task UploadXlsxFile()
    {
        _logger.Debug("Uploading XLSX file in OCR: viewreceipts.xlsx");
        await uploadButton.ClickAsync();
        try
        {
            _logger.Debug("Setting input files for XLSX OCR upload");
            await uploadPdf.SetInputFilesAsync(".\\UploadFiles\\viewreceipts.xlsx");
            _logger.Debug("Clicking upload button for OCR scan");
            await Uploadbtnpopup.ClickAsync();
            _logger.Debug("XLSX file OCR upload completed");
        }
        catch (Exception ex)
        {
            _logger.Warning("Exception occurred during XLSX OCR upload: {ErrorMessage}", ex.Message);
        }
    }
    //Find a record in review state click on Search doc btn and po# is exact match
    public async Task SearchPOIsExactMatch(CommonContext commonContext,Context context)
    {
        _logger.Debug("Searching for PO with exact match");
        if(await ReviewPanelIsReadyAsync()){
            _logger.Debug("Review panel is ready, getting document content");
            var Doccontent = (await DocumentField.TextContentAsync())?.Trim() ?? string.Empty;
            _logger.Debug("Document content: {Content}", Doccontent);
            
            _logger.Debug("Verifying and handling frame");
            var (newPage, actualText) = await VerifyAndHandleFrameAsync(Page, SearchDocbtn, "#v4Container",commonContext,context);
            Assert.That(Doccontent, Is.EqualTo(actualText));
            
            var frameLocator = newPage.FrameLocator("#v4Container");
            if(await IsVisibleAsync(_closeBtnX,1000,1)){
                _logger.Debug("Close button X is visible, clicking");
                await _closeBtnX.ClickAsync();
            }
            
            _logger.Debug("Clicking next button");
            await _nextBtn.EvaluateAsync("node=>node.click()");

            //Verify Existing OCR Document popup
            _logger.Debug("Verifying and closing OCR document popup");
            await VerifyAndCloseOCRDocumentPopup(frameLocator);
            _logger.Debug("PO exact match search completed");
        }else{
            _logger.Error("Review panel is not ready");
            Assert.Fail("Review panel is not ready");
        }
    }
    //Upload large size .jpg file
    public async Task UploadLargeJpg()
    {
        _logger.Debug("Uploading large JPG file: large.jpg");
        await uploadButton.ClickAsync();
        await uploadPdf.SetInputFilesAsync(".\\UploadFiles\\large.jpg");
        if (await IsVisibleAsync(Uploadbtnpopup,1000,1))
        {
            _logger.Debug("Upload button popup is visible, clicking");
            await Uploadbtnpopup.ClickAsync();
        }
        _logger.Debug("Large JPG file upload completed");
    }
    //Upload large size .jpg file in OCR
    public async Task UploadLargeJpgFile()
    {
        _logger.Debug("Uploading large JPG file in OCR: large.jpg");
        await uploadButton.ClickAsync();
        await uploadPdf.SetInputFilesAsync(".\\UploadFiles\\large.jpg");
        _logger.Debug("Clicking upload button for OCR scan");
        await Uploadbtnpopup.ClickAsync();
        _logger.Debug("Large JPG file OCR upload completed");
    }
    //upload .jpg file in OCR
    public async Task UploadJpgfile()
    {
        _logger.Debug("Uploading JPG file in OCR: file_example.jpg");
        await UploadFile(".\\UploadFiles\\file_example.jpg");
        _logger.Debug("JPG file OCR upload completed");
    }
    //upload .pdf file in OCR

    public async Task UploadAndScanFiles(string filename)
    {
        _logger.Debug("Uploading and scanning file: {Filename}", filename);
        await uploadButton.ClickAsync();
        await uploadPdf.SetInputFilesAsync(filename);

        // Check if the upload button is disabled (Only .PDF formats accepts permission is ON)
        bool isButtonDisabled = await IsUploadAndScanFilesButtonDisabled();
        _logger.Debug("Upload and scan button disabled status: {Status}", isButtonDisabled);

        if (isButtonDisabled && !filename.ToLower().EndsWith(".pdf"))
        {
            _logger.Warning("Upload button is disabled for non-PDF files. 'Only .PDF formats accepts' permission is ON.");

            // Try to click the button anyway and check for error message
            _logger.Debug("Attempting to click upload button despite being disabled");
            await UploadandScanFilesbtn.ClickAsync();

            // Wait for the invalid format popup to be visible
            if (await IsVisibleAsync(invalidFormatPopup, 5000, 3))
            {
                _logger.Debug("File format error message displayed as expected");
                // Get the error message text for logging
                string errorMessage = await invalidFormatPopup.TextContentAsync() ?? "";
                _logger.Debug("Error message: {Message}", errorMessage);

                // Check if the error message contains "File format is not supported"
                if (errorMessage.Contains("File format is not supported"))
                {
                    // Close the dialog
                    _logger.Debug("Closing error dialog");
                    await uploadClose.ClickAsync();

                    // Fail the test case as requested
                    _logger.Error("Test failed: 'File format is not supported' error was displayed");
                    Assert.Fail("Test failed: 'File format is not supported' error was displayed. Further test execution stopped as requested.");
                }
            }
        }
        else
        {
            // Either the button is enabled or we're uploading a PDF file, so we can proceed
            _logger.Debug("Clicking upload and scan files button");
            await UploadandScanFilesbtn.ClickAsync();
            // Only check for success message if PDF permission is OFF or file is PDF
            if (!isButtonDisabled || filename.ToLower().EndsWith(".pdf")) {
                _logger.Debug("Waiting for upload success message");
                await uploadSuccessMsg.WaitForAsync();
                await Assertions.Expect(uploadSuccessMsg).ToBeVisibleAsync();
                _logger.Debug("Upload success message is visible");
            }
        }

        _logger.Debug("Closing upload dialog");
        await uploadClose.ClickAsync();
        _logger.Debug("File upload and scan completed");
    }
    public async Task UploadAutoSetGLCodeFile()
    {
        await UploadAndScanFiles(".\\UploadFiles\\UpdatedTestPDF.pdf");
    }
    public async Task UploadPDFFile()
    {
        await UploadAndScanFiles(".\\UploadFiles\\iBuyEfficient_Receipt.pdf");
    }
    public async Task UploadPDFHavingDifferentLineItems()
    {
        await UploadAndScanFiles(".\\UploadFiles\\FileHavingDifferentLineItems.pdf");
    }
    public async Task UploadPDFHavingSameLineItems()
    {
        await UploadAndScanFiles(".\\UploadFiles\\SameLineItemsFile.pdf");
    }
    //upload .png file in OCR
    public async Task Uploadpngfile()
    {
        await UploadFile(".\\UploadFiles\\image.png");
    }
    //upload .jpeg file in OCR
    public async Task Uploadjpegfile()
    {
        await UploadFile(".\\UploadFiles\\sample.jpeg");
    }
    //upload .bmp file in OCR
    public async Task UploadBmpfile()
    {
        await UploadFile(".\\UploadFiles\\sample.bmp");
    }
    //upload .tiff file in OCR
    public async Task UploadTifffile()
    {
        await UploadFile(".\\UploadFiles\\Laptop.tiff");
    }
    //Verify the source column is Localupload for uploaded file using Upload button
    public async Task VerifySourceColinRecordsTab()
    {
        await Task.Run(() =>{

        });

    }
    //upload .jpg file in ocr
    public async Task UploadJpgfileOcr()
    {
        await uploadButton.ClickAsync();
        // Set the file path for upload
        await uploadPdf.SetInputFilesAsync(".\\UploadFiles\\file_example.jpg");

        // Check if the uploadButtonInOCR is disabled (which happens when "Only .PDF formats accepts" permission is ON)
        bool isButtonDisabled = await IsUploadAndScanFilesButtonDisabled();

        if (isButtonDisabled) {
            Console.WriteLine("Upload button is disabled. 'Only .PDF formats accepts' permission is ON.");
        } else {
            Console.WriteLine("Upload button is enabled. Proceeding with upload.");
            // Button is enabled, so we can click it
            await uploadButtonInOCR.ClickAsync();
            await uploadSuccessMsg.WaitForAsync();
        }
        // Only check for success message if PDF permission is OFF
        if (!isButtonDisabled) {
            await Assertions.Expect(uploadSuccessMsg).ToBeVisibleAsync();
        }

        await uploadClose.ClickAsync();
        await Recordstab.ClickAsync();
    }
    //Verify the source column is Localupload(OCR) for uploaded file using Upload and scan files button
    public async Task VerifySourceColinQueuetab()
    {
        if (await IsVisibleAsync(SourcecolQueuetab,1000,1))
        {
            await Assertions.Expect(SourcecolQueuetab).ToHaveTextAsync("Local Upload (OCR)");
        }
    }

    public async Task SearchUploadedFile(string filename)
    {
        _logger.Debug("Searching for uploaded file: {Filename}", filename);
        await Firstrow.WaitForAsync();
        _logger.Debug("First row is visible, clicking file name filter button");
        await fileNameFilterBtn.ClickAsync();
        _logger.Debug("Clicking filter input field");
        await filterInput.ClickAsync();
        _logger.Debug("Filling filter input with filename");
        await filterInput.FillAsync(filename);
        _logger.Debug("Clicking filter button");
        await filterButton.ClickAsync();
        _logger.Debug("Waiting for filtered results to appear");
        await Firstrow.WaitForAsync();
        _logger.Debug("Search for {Filename} completed", filename);
    }
    public async Task SearchDifferentLineItemmPDFFile()
    {
        _logger.Debug("Searching for PDF file with different line items");
        await SearchUploadedFile("FileHavingDifferentLineItems.pdf");
        _logger.Debug("Search for different line items PDF completed");
    }
    public async Task SearchSameLineItemmPDFFile()
    {
        _logger.Debug("Searching for PDF file with same line items");
        await SearchUploadedFile("SameLineItemsFile.pdf");
        _logger.Debug("Search for same line items PDF completed");
    }
    public async Task SearchUploadedpdf()
    {
        _logger.Debug("Searching for iBuyEfficient_Receipt.pdf");
        await SearchUploadedFile("iBuyEfficient_Receipt.pdf");
        _logger.Debug("Search for iBuyEfficient_Receipt.pdf completed");
    }
    public async Task SearchUploadedFile()
    {
        _logger.Debug("Searching for default receipt file");
        await SearchUploadedFile("iBuyEfficient_Receipt.pdf");
        _logger.Debug("Search for default receipt file completed");
    }
    public async Task SearchUploadedAutoSetGLCodeFile()
    {
        _logger.Debug("Searching for UpdatedTestPDF.pdf (Auto Set GL Code file)");
        await SearchUploadedFile("UpdatedTestPDF.pdf");
        _logger.Debug("Search for Auto Set GL Code file completed");
    }
    public async Task ReassignToCompany()
    {
        _logger.Debug("Attempting to reassign to company");
        if (await IsVisibleAsync(selectarow,1000,1))
        {
            _logger.Debug("Row selection checkbox is visible, clicking");
            await selectarow.ClickAsync();
            if (await IsVisibleAsync(ReassigntoCmpybtn,1000,1))
            {
                _logger.Debug("Reassign to Company button is visible, clicking");
                await ReassigntoCmpybtn.ClickAsync();
                _logger.Debug("Reassign to Company button clicked");
            }
            else
            {
                _logger.Debug("Reassign to Company button is not visible");
            }
        }
        else
        {
            _logger.Debug("Row selection checkbox is not visible");
        }
    }
    //Click on Preview button and close the preview popup
    public async Task PreviewPopup()
    {
        _logger.Debug("Opening preview popup");
        await previewButton.ClickAsync();
        _logger.Debug("Preview opened, closing popup");
        await uploadClose.ClickAsync();
        _logger.Debug("Preview popup closed");
    }

    //Rename the file
    public async Task RenameFile()
    {
        _logger.Debug("Renaming file");
        await renameRow.EvaluateAsync("node=>node.click()");
        _logger.Debug("Rename field activated, clearing current name");
        await renameFile.ClearAsync();
        _logger.Debug("Setting new file name to Test.pdf");
        await renameFile.FillAsync("Test.pdf");
        _logger.Debug("Clicking date column to save rename");
        await dateColumn.ClickAsync();
        _logger.Debug("File renamed to Test.pdf");
    }
    //Validating renamed file present in the table grid
    public async Task VerifyRenamedFile()
    {
        _logger.Debug("Verifying renamed file is present in table");
        await Firstrow.WaitForAsync();
        _logger.Debug("Clearing existing filters");
        await clearFilterBtn.ClickAsync();
        _logger.Debug("Clicking file name filter button");
        await fileNameFilterBtn.ClickAsync();
        _logger.Debug("Clicking filter input field");
        await filterInput.ClickAsync();
        _logger.Debug("Filling filter with Test.pdf");
        await filterInput.FillAsync("Test.pdf");
        if (await IsVisibleAsync(filterButton,1000,1))
        {
            _logger.Debug("Filter button is visible, clicking");
            await filterButton.ClickAsync();
            _logger.Debug("Verifying renamed file appears in results");
            await Assertions.Expect(renameRow).ToContainTextAsync("Test.pdf");
            _logger.Debug("Renamed file verification successful");
        }
        else
        {
            _logger.Warning("Filter button was not visible within timeout");
        }
    }
    //Delete a record
    public async Task DeleteRow()
    {
        _logger.Debug("Deleting a record");
        _logger.Debug("Clicking file name filter button");
        await fileNameFilterBtn.ClickAsync();
        _logger.Debug("Clicking filter input field");
        await filterInput.ClickAsync();
        _logger.Debug("Filling filter with Laptop.tiff");
        await filterInput.FillAsync("Laptop.tiff");
        if (await filterButton.IsEnabledAsync())
        {
            _logger.Debug("Filter button is enabled, clicking");
            await filterButton.ClickAsync();
            _logger.Debug("Selecting row to delete");
            await deleteRow.ClickAsync();
            _logger.Debug("Clicking delete button");
            await deleteButton.ClickAsync();
            if (await IsVisibleAsync(deleteConfirmation,1000,1))
            {
                _logger.Debug("Confirming deletion");
                await deleteConfirmation.ClickAsync();
                _logger.Debug("Record deletion confirmed");
            }
            else
            {
                _logger.Debug("Delete confirmation dialog not visible");
            }
        }
        else
        {
            _logger.Debug("Filter button is not enabled");
        }
    }
    //Filter deleted record
    public async Task FilterDeletedRow()
    {
        _logger.Debug("Filtering for deleted record");
        _logger.Debug("Clicking file name filter button");
        await fileNameFilterBtn.ClickAsync();
        _logger.Debug("Clicking filter input field");
        await filterInput.ClickAsync();
        _logger.Debug("Filling filter with Laptop.tiff");
        await filterInput.FillAsync("Laptop.tiff");
        _logger.Debug("Checking if filter button is enabled");
        await filterButton.IsEnabledAsync();
        _logger.Debug("Clicking filter button");
        await filterButton.ClickAsync();
        _logger.Debug("Checking if deleted tag is visible");
        if (await IsVisibleAsync(Deletedtag,1000,1))
        {
            _logger.Debug("Deleted tag is visible, selecting row");
            await selectarow.ClickAsync();
            _logger.Debug("Row selected");
        }
        else
        {
            _logger.Debug("Deleted tag not visible");
        }
    }
    //Deleting bulk records
    public async Task DeleteBulk()
    {
        _logger.Debug("Deleting bulk records");
        _logger.Debug("Clicking bulk delete checkbox");
        await deleteBulk.ClickAsync();
        if (await deleteButton.IsEnabledAsync())
        {
            _logger.Debug("Delete button is enabled, clicking");
            await deleteButton.ClickAsync();
            if (await IsVisibleAsync(deleteConfirmation,1000,1))
            {
                _logger.Debug("Confirming bulk deletion");
                await deleteConfirmation.ClickAsync();
                _logger.Debug("Bulk deletion confirmed");
            }
            else
            {
                _logger.Debug("Delete confirmation dialog not visible");
            }
        }
        else
        {
            _logger.Debug("Delete button is not enabled");
        }
    }
    //Click on ClearFilters button
    public async Task ClearFilters()
    {
        _logger.Debug("Clearing all filters");
        await clearFilterBtn.ClickAsync();
        _logger.Debug("Filters cleared");
    }
    //Retrieve Invalid format row data from the table on the webpage.
    public async Task<List<string>> verifyInvalidError()
    {
        _logger.Debug("Extracting row data to verify invalid error");
        var result = await base.ExtractRowDataAsync();
        _logger.Debug("Row data extracted, count: {Count}", result.Count);
        return result;
    }
    //Retrive Large file row data from the table on the webpage
    public async Task<List<string>> VerifyLargeFileSizeError()
    {
        _logger.Debug("Extracting row data to verify large file size error");
        var result = await base.ExtractRowDataAsync();
        _logger.Debug("Row data extracted, count: {Count}", result.Count);
        return result;
    }
    //Click on Ocrqueue tab
    public async Task OCRqueue()
    {
        _logger.Debug("Navigating to OCR Queue tab");
        await OCRqueuebtn.ClickAsync();
        _logger.Debug("OCR Queue tab opened");
    }
    //If the company is a nonocr company then OCR Queue must have this validation message
    public async Task OCRqueueMsgValidate()
    {
        _logger.Debug("Validating OCR queue message for non-OCR company");
        if (await IsVisibleAsync(OCRqueuebtn,1000,1))
        {
            _logger.Debug("OCR Queue button is visible, clicking");
            await OCRqueuebtn.ClickAsync();
            _logger.Debug("OCR Queue tab opened");
        }
        else
        {
            _logger.Debug("OCR Queue button is not visible");
        }
    }
    //Select Stackular to reassign
    public async Task StackularCompany()
    {
        _logger.Debug("Selecting Stackular company for reassignment");
        if (await IsVisibleAsync(reassigndropdown,1000,1))
        {
            _logger.Debug("Reassign dropdown is visible, clicking");
            await reassigndropdown.ClickAsync();
            _logger.Debug("Selecting Stackular from dropdown");
            await companyStackular.ClickAsync();
            _logger.Debug("Clicking Reassign button");
            await Reassignbtn.ClickAsync();
            _logger.Debug("Clicking OK button");
            await Okbtn.ClickAsync();
            _logger.Debug("Reassignment to Stackular completed");
        }
        else
        {
            _logger.Debug("Reassign dropdown is not visible");
        }
    }
    //Select ShowAttached option from preset filter dropdown
    public async Task SelectShowAttached()
    {
        _logger.Debug("Selecting 'Show Attached' filter option");
        _logger.Debug("Clicking preset filter dropdown button");
        await PresetFltrbtn.ClickAsync();
        _logger.Debug("Selecting 'Show Attached' option");
        await ShowAttached.ClickAsync();
        _logger.Debug("Waiting for first row to be visible");
        await Firstrow.WaitForAsync();
        _logger.Debug("Selecting first row");
        await selectarow.ClickAsync();
        _logger.Debug("Opening invoice in new page");
        var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await InvoiceLink.ClickAsync();
        });
        var newPageTitle = await newPage.TitleAsync();
        _logger.Debug("New page opened with title: {Title}", newPageTitle);
        _logger.Debug("Bringing original page to front");
        await Page.BringToFrontAsync();
        _logger.Debug("'Show Attached' filter applied and invoice opened successfully");
    }
    //Select ShowUnattached option from preset filter dropdown
    public async Task SelectShowUnattached()
    {
        _logger.Debug("Selecting 'Show Unattached' filter option");
        _logger.Debug("Clicking preset filter dropdown button");
        await PresetFltrbtn.ClickAsync();
        _logger.Debug("Selecting 'Show Unattached' option");
        await ShowUnattached.ClickAsync();
        _logger.Debug("Waiting for first row to be visible");
        await Firstrow.WaitForAsync();
        _logger.Debug("Selecting first row");
        await selectarow.ClickAsync();
        _logger.Debug("'Show Unattached' filter applied and row selected");
    }
    //Select ShowAll option from preset filter dropdown
    public async Task SelectShowAll()
    {
        _logger.Debug("Selecting 'Show All' filter option");
        if (await IsVisibleAsync(PresetFltrbtn,1000,1))
        {
            _logger.Debug("Preset filter dropdown button is visible, clicking");
            await PresetFltrbtn.ClickAsync();
            _logger.Debug("Selecting 'Show All' option");
            await ShowAll.ClickAsync();
            _logger.Debug("Waiting for first row to be visible");
            await Firstrow.WaitForAsync();
            _logger.Debug("Selecting first row");
            await selectarow.ClickAsync();
            _logger.Debug("'Show All' filter applied and row selected");
        }
        else
        {
            _logger.Debug("Preset filter dropdown button is not visible");
        }
    }

    //Select ShowDeleted option from preset filter dropdown
    public async Task SelectShowDeleted()
    {
        _logger.Debug("Selecting 'Show Deleted' filter option");
        _logger.Debug("Clicking preset filter dropdown button");
        await PresetFltrbtn.ClickAsync();
        if(await ShowDeleted.IsVisibleAsync()){
            _logger.Debug("'Show Deleted' option is visible, clicking");
            await ShowDeleted.ClickAsync();
            _logger.Debug("Waiting for first row to be visible");
            await Firstrow.WaitForAsync();
            _logger.Debug("'Show Deleted' filter applied successfully");
        }
        else
        {
            _logger.Debug("'Show Deleted' option is not visible");
        }
    }
    //Select first row from the table and click on Undelete
    public async Task UndeleteBtn()
    {
        _logger.Debug("Attempting to undelete selected record");
        _logger.Debug("Checking if Undelete button is enabled");
        await Undeletebtn.IsEnabledAsync();
        _logger.Debug("Clicking Undelete button");
        await Undeletebtn.ClickAsync();
        _logger.Debug("Clicking confirmation button");
        await UndeleteConformation.ClickAsync();
        _logger.Debug("Record undelete operation completed");
    }
    //RunOCRScan btn
    public async Task RunOCRScan()
    {
        _logger.Debug("Attempting to run OCR scan");
        if (await IsVisibleAsync(selectarow,1000,5))
        {
            _logger.Debug("Row selection checkbox is visible, clicking");
            await selectarow.ClickAsync();
            if (await RunOCRScanbtn.IsEnabledAsync())
            {
                _logger.Debug("Run OCR Scan button is enabled, clicking");
                await RunOCRScanbtn.ClickAsync();
                _logger.Debug("Clicking Run Scan confirmation button");
                await RunScanconfrmation.ClickAsync();
                _logger.Debug("OCR scan initiated successfully");
            }
            else
            {
                _logger.Debug("Run OCR Scan button is not enabled");
            }
        }
        else
        {
            _logger.Debug("Row selection checkbox is not visible");
        }
    }
    //Preset filter in OCRQueue Tab and select ShowReview
    public async Task SelectShowNeedsReview()
    {
        _logger.Debug("Selecting 'Show Needs Review' filter in OCR Queue tab");
        if (await IsVisibleAsync(PresetFilterQueuetab,1000,5))
        {
            _logger.Debug("Preset filter dropdown is visible, clicking");
            await PresetFilterQueuetab.ClickAsync();
            _logger.Debug("Selecting 'Show Needs Review' option");
            await ShowNeedsReviewFilter.ClickAsync();
            _logger.Debug("'Show Needs Review' filter applied");
        }
        else
        {
            _logger.Debug("Preset filter dropdown is not visible");
        }
    }
    //Preset filter in OCRQueue Tab and select ShowInprogress
    public async Task SelectShowInprogress()
    {
        _logger.Debug("Selecting 'Show In Progress' filter in OCR Queue tab");
        _logger.Debug("Clicking preset filter dropdown");
        await PresetFilterQueuetab.ClickAsync();
        _logger.Debug("Selecting 'Show In Progress' option");
        await ShowInProgressfilter.ClickAsync();
        _logger.Debug("'Show In Progress' filter applied");
    }
    //Preset filter in OCRQueue Tab and select ShowFailed
    public async Task SelectShowFailed()
    {
        _logger.Debug("Selecting 'Show Failed' filter in OCR Queue tab");
        if (await IsVisibleAsync(PresetFilterQueuetab,1000,5))
        {
            _logger.Debug("Preset filter dropdown is visible, clicking");
            await PresetFilterQueuetab.ClickAsync();
            _logger.Debug("Selecting 'Show Failed' option");
            await ShowFailedFilter.ClickAsync();
            _logger.Debug("'Show Failed' filter applied");
        }
        else
        {
            _logger.Debug("Preset filter dropdown is not visible");
        }
    }
    //Preset filter in OCRQueue Tab and select ShowAll
    public async Task SelectShowAllQueue()
    {
        _logger.Debug("Selecting 'Show All' filter in OCR Queue tab");
        if (await IsVisibleAsync(PresetFilterQueuetab,1000,5))
        {
            _logger.Debug("Preset filter dropdown is visible, clicking");
            await PresetFilterQueuetab.ClickAsync();
            _logger.Debug("Selecting 'Show All' option");
            await ShowAllocrQueue.ClickAsync();
            _logger.Debug("'Show All' filter applied");
        }
        else
        {
            _logger.Debug("Preset filter dropdown is not visible");
        }
    }
    //Filter the undeleted file
    public async Task SearchUndeletedFile()
    {
        _logger.Debug("Searching for undeleted file: Laptop.tiff");
        await SearchUploadedFile("Laptop.tiff");
        _logger.Debug("Search for undeleted file completed");
    }
    //Get the count of the  OCR Scan to Review records present in the tile is exact match to the Ocrqueue tab
    public async Task TileCountMatchesOcrqueue(CommonContext commonContext)
    {
        //Extract the text of the tile in dashboard
        string OCRScanReviewTextValidation = await OCRScanReviewTileTextValidation.TextContentAsync()??"";
        Assert.That(OCRScanReviewTextValidation, Is.EqualTo("OCR Scans to Review"));

        string OCRScanReviewtileText = await OCRScanReviewCount.TextContentAsync()??"";
        //  Click on the tile to navigate to OCR Queue tab
        await OCRScanReviewCount.ClickAsync();
        if (commonContext.HasOCR)
        {
            string ReviewText = await ReviewCount.TextContentAsync()??"";
            Assert.That(ReviewText, Is.EqualTo("Show Needs Review"));
            // Extract count from the div
            string divTextIsReview = await RecordCount.TextContentAsync()??"";
            // Use regex to extract the number after "of"
            await WaitUntilLoaderHidden();
            int divText = await getTotalgridCount("Records");
            Assert.That(divTextIsReview.Trim(), Is.EqualTo(divText+""));
        }
    }
    //Find a record in review state click on Search doc btn and po# is null
    public async Task SearchPOIsNull(CommonContext commonContext,Context context)
    {
        if(await ReviewPanelIsReadyAsync()){
            var Doccontent = (await DocumentField.TextContentAsync())?.Trim() ?? string.Empty;
            var (newPage, actualText) = await VerifyAndHandleFrameAsync(Page, SearchDocbtn, "#v4Container",commonContext,context);
            Assert.That(Doccontent, Is.EqualTo(actualText));
            var frameLocator = newPage.FrameLocator("#v4Container");
            if(await IsVisibleAsync(_closeBtnX,1000,5)){
                await _closeBtnX.ClickAsync();
            }
            await _nextBtn.EvaluateAsync("node=>node.click()");
            await _closeBtnX.ClickAsync();
            var text = await frameLocator.Locator("label:has-text('PO #') + input").InputValueAsync();
            Assert.That(text, Is.Empty);
        }else{
            Assert.Fail("Review panel is not ready");
        }

    }
    private async Task<(IPage newPage, string actualText)> VerifyAndHandleFrameAsync(IPage page, ILocator triggerButton, string frameSelector,CommonContext commonContext,Context _context)
    {
        var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await triggerButton.ClickAsync();
        });
        IPage oldPage = Page;
        _context.Page = newPage;
        Page = newPage;
        await oldPage.CloseAsync();
        // Trigger action and wait for the new page
        await Task.Delay(1000);
        // Click the button inside the frame
        if(await IsVisibleAsync(_closeBtnX,1000,5)){
            await _closeBtnX.ClickAsync();
        }
        await Task.Delay(1000);
        if(await IsVisibleAsync(_closeBtnX,1000,5)){
            await _closeBtnX.ClickAsync();
        }
        // Check if the checkbox has the "disabled" attribute
        var isDisabled = await _iframe.Locator("//div[@class='flex justify-center']//input[@type='checkbox']").IsDisabledAsync();
        Assert.IsTrue(isDisabled, "The checkbox is editable");
        if(await IsVisibleAsync(_iframe.Locator("span:has-text('Clear Selection')"),1000,5) && await _iframe.Locator("span:has-text('Clear Selection')").IsEnabledAsync()){
            await _iframe.Locator("span:has-text('Clear Selection')").EvaluateAsync("node=>node.click()");
        }
        // Fetch Document# text field content
        var inputElement = _iframe.Locator("//input[@placeholder='Enter invoice or credit memo #']");
        var actualText = (await inputElement.TextContentAsync())?.Trim() ?? string.Empty;

        Console.WriteLine($"Doc# in Invoice Details page is Exact match with the Doc# in Review Scan pannel");

        // Return the fetched content
        return (newPage, actualText);
    }

    public async Task VerifyAndCloseOCRDocumentPopup(IFrameLocator frameLocator)
    {
        // Verify Existing OCR Document popup
        ILocator ExistingOCRDocumentpopup = frameLocator.Locator(".k-window-titlebar.k-dialog-titlebar");
        if (await IsVisibleAsync(ExistingOCRDocumentpopup,1000,5))
        {
            await frameLocator.Locator(".k-window-titlebar.k-dialog-titlebar div:nth-child(2) button span").ClickAsync();
        }
    }
    //Verify the source column is Localupload for uploaded file using Upload button
    public async Task VerifySourceColinRecordspage()
    {
        if (await IsVisibleAsync(Deletedtag,1000,5))
        {
            await selectarow.ClickAsync();
        }
        if (await IsVisibleAsync(SourcecolAttachment,1000,5))
        {
            await Assertions.Expect(SourcecolAttachment).ToHaveTextAsync("Local Upload");
        }
    }
    //Click on queue tab
    public async Task QueueSection()
    {
        await OCRqueuebtn.ClickAsync();
    }
    //merge the document when it is Nonocr generated exact match in the selected company
    public async Task MergeNonocrRecord()
    {
        _logger.Debug("Attempting to merge non-OCR generated exact match record");
        if(await ReviewPanelIsReadyAsync()){
            _logger.Debug("Review panel is ready, getting document field value");
            string value1 = await DocTextField.InputValueAsync();
            _logger.Debug("Document field value: {Value}", value1);
            
            if(await IsVisibleAsync(Mergebtn,1000,5) && await Mergebtn.IsEnabledAsync()){
                _logger.Debug("Merge button is visible and enabled, clicking");
                await Mergebtn.ClickAsync();
                
                _logger.Debug("Getting merge document number");
                string mergecontent = (await MergeDocNo.TextContentAsync())?.Trim() ?? string.Empty;
                _logger.Debug("Merge document number: {Number}", mergecontent);
                
                _logger.Debug("Verifying document numbers match");
                Assert.That(value1, Is.EqualTo(mergecontent));
                
                _logger.Debug("Clicking merge confirm button");
                await MergeConfirmbtn.ClickAsync();
                
                _logger.Debug("Navigating to Records tab");
                await Recordstab.ClickAsync();
                
                _logger.Debug("Waiting for first row to be visible");
                await Firstrow.WaitForAsync();
                _logger.Debug("Merge operation completed successfully");
            } else {
                _logger.Debug("Merge button is not visible or not enabled, clicking Cancel");
                await Cancelbtn.ClickAsync();
                _logger.Debug("Merge operation cancelled");
            }
        } else {
            _logger.Error("Review panel is not ready");
            Assert.Fail("Review panel is not ready");
        }
    }
    //upload .pdf file in merge documents
    public async Task AttachedPDFfile()
    {
        _logger.Debug("Uploading PDF file for merge documents: DND4898598083.pdf");
        await UploadAndScanFiles(".\\UploadFiles\\DND4898598083.pdf");
        _logger.Debug("PDF file for merge documents uploaded successfully");
    }
    public async Task MergeForInvalidRecord()
    {
        _logger.Debug("Verifying merge button is disabled for invalid record");
        if(await ReviewPanelIsReadyAsync()){
            _logger.Debug("Review panel is ready, checking merge button");
            if(await IsVisibleAsync(Mergebtn,1000,5)){
                _logger.Debug("Merge button is visible, verifying it is disabled");
                bool isDisabled = await Mergebtn.IsDisabledAsync();
                _logger.Debug("Merge button disabled status: {Status}", isDisabled);
                Assert.IsTrue(isDisabled, "The button is Enabled.");
                _logger.Debug("Verified merge button is disabled as expected");
            } else {
                _logger.Debug("Merge button is not visible");
            }
        } else {
            _logger.Error("Review panel is not ready");
            Assert.Fail("Review panel is not ready");
        }
    }
    //Filter the merge .pdf file
    public async Task FilteredPdfDoc()
    {
        _logger.Debug("Filtering for merge PDF document: DND4898598083.pdf");
        await SearchUploadedFile("DND4898598083.pdf");
        _logger.Debug("Filter for merge PDF document applied");
    }
    //Click on the Filtered merge .pdf file
    public async Task FilteredMerge()
    {
        _logger.Debug("Attempting to filter and merge PDF file");
        if(await ReviewPanelIsReadyAsync()){
            _logger.Debug("Review panel is ready, clicking invoice link to open in new page");
            var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
            {
                await InvoiceLink.ClickAsync();
            });
            var newPageTitle = await newPage.TitleAsync();
            _logger.Debug("New page opened with title: {Title}", newPageTitle);
            
            var frameLocator = newPage.FrameLocator("#v4Container");
            _logger.Debug("Closing dialog in new page");
            await frameLocator.Locator(".k-window-titlebar.k-dialog-titlebar div:nth-child(2) button span").ClickAsync();
            
            _logger.Debug("Clicking fourth button in flex wrap container");
            await frameLocator.Locator("//div[@class='flex flex-wrap']//button[4]").ClickAsync();
            
            // List of buttons to validate
            _logger.Debug("Validating buttons visibility and enablement");
            var buttonsToValidate = new (string Name, string Description, bool Exact)[]
            {
                ("Upload", "The Upload button", false),
                ("Attach", "The Attach button", true),
                ("Un-attach", "The Unattach button", false),
                ("Delete", "The Delete button", false)
            };

            // Iterate through the buttons and validate visibility and enablement
            foreach (var (name, description, exact) in buttonsToValidate)
            {
                var btn = name == "Delete"
                    ? frameLocator.GetByRole(AriaRole.Button, new() { Name = name }).Nth(3) // Select the fourth "Delete" button
                    : frameLocator.GetByRole(AriaRole.Button, new() { Name = name, Exact = exact });
                if (await IsVisibleAsync(btn,1000,5) && await btn.IsEnabledAsync())
                {
                    _logger.Debug("{Description} is visible and enabled", description);
                }
                else
                {
                    _logger.Debug("{Description} is not visible or not enabled", description);
                }
            }
            _logger.Debug("Button validation completed");
        }
        else
        {
            _logger.Error("Review panel is not ready");
        }
    }
    // Click on Review button when visible
    public async Task FileInReviewState()
    {
        _logger.Debug("Waiting for Review button to be visible (timeout: 30s)");
        var resp = await IsVisibleAsync(Reviewbtn,30000,20);
        if(resp){
            _logger.Debug("Review button is visible, verifying it contains 'Review' text");
            await Assertions.Expect(Reviewbtn).ToContainTextAsync("Review");
            _logger.Debug("Clicking Review button");
            await Reviewbtn.ClickAsync();
            _logger.Debug("Review button clicked successfully");
        }else{
            _logger.Error("Review button not visible after timeout");
            Assert.Fail("Review panel is not ready");
        }
    }
    //upload Invalid PO# .pdf file for merge documents
    public async Task AttachedPDF()
    {
        _logger.Debug("Uploading invalid PO# PDF file: DNDInvalidPO.pdf");
        await UploadAndScanFiles(".\\UploadFiles\\DNDInvalidPO.pdf");
        _logger.Debug("Invalid PO# PDF file uploaded successfully");
    }

    //Verify DeleteOCRbutMoveimage in abandon btn
    public async Task DeleteOCRandMoveimage()
    {
        _logger.Debug("Attempting to delete OCR but move image");
        if(await ReviewPanelIsReadyAsync()){
            _logger.Debug("Review panel is ready, clicking Abandon button");
            await Abandonbtn.ClickAsync();
            _logger.Debug("Clicking 'Delete OCR but Move image' button");
            await DeleteOCRbutMoveimagebtn.ClickAsync();
            _logger.Debug("Navigating to Records tab");
            await Recordstab.ClickAsync();
            _logger.Debug("Delete OCR but move image operation completed");
        }else{
            _logger.Error("Review panel is not ready");
            Assert.Fail("Review panel is not ready");
        }
    }
    // method to ensure Abandon button is visible and enabled
    private async Task<bool> ReviewPanelIsReadyAsync()
    {
        return await IsVisibleAsync(ReviewScanPannel,30000,1);
    }
    //Verify DeleteOCRbutMoveimage in abandon btn
    public async Task DeleteOCRAndDeleteImage()
    {
        _logger.Debug("Attempting to delete OCR and delete image");
        if(await ReviewPanelIsReadyAsync()){
            _logger.Debug("Review panel is ready, clicking Abandon button");
            await Abandonbtn.ClickAsync();
            _logger.Debug("Clicking 'Delete OCR and Delete image' button");
            await DeleteOCRDeleteimagebtn.ClickAsync();
            _logger.Debug("Navigating to Records tab");
            await Recordstab.ClickAsync();
            _logger.Debug("Delete OCR and delete image operation completed");
        }else{
            _logger.Error("Review panel is not ready");
            Assert.Fail("Review panel is not ready");
        }
    }
    //Verify ConfirmDelete in abandon btn for ocr in selected cmpy
    public async Task OCRConfirmDelete()
    {
        _logger.Debug("Attempting to confirm delete in abandon button for OCR");
        if(await ReviewPanelIsReadyAsync()){
            _logger.Debug("Review panel is ready, clicking Abandon button");
            await Abandonbtn.ClickAsync();
            if (await IsVisibleAsync(AbandonDeletepopup,1000,5))
            {
                _logger.Debug("Abandon delete popup is visible, clicking confirm delete button");
                await ConfirmDeletebtn.ClickAsync();
                _logger.Debug("Delete confirmed");
            }
            else
            {
                _logger.Debug("Abandon delete popup is not visible");
            }
            _logger.Debug("Waiting for 3 seconds");
            await Task.Delay(3000);
            _logger.Debug("Navigating to Records tab");
            await Recordstab.ClickAsync();
            _logger.Debug("OCR confirm delete operation completed");
        }else{
            _logger.Error("Review panel is not ready");
            Assert.Fail("Review panel is not ready");
        }
    }
    //upload .pdf file in OCR
    public async Task Uploadedpdffile()
    {
        _logger.Debug("Uploading PDF file in OCR: PONull.pdf");
        await UploadAndScanFiles(".\\UploadFiles\\PONull.pdf");
        _logger.Debug("PDF file in OCR uploaded successfully");
    }
    //Filter the .pdf file
    public async Task SearchForUploadedpdf()
    {
        _logger.Debug("Filtering for uploaded PDF: PONull.pdf");
        _logger.Debug("Clicking file name filter button");
        await fileNameFilterBtn.ClickAsync();
        _logger.Debug("Clicking filter input field");
        await filterInput.ClickAsync();
        _logger.Debug("Filling filter with PONull.pdf");
        await filterInput.FillAsync("PONull.pdf");
        _logger.Debug("Clicking filter button");
        await filterButton.ClickAsync();
        _logger.Debug("Waiting for first row to be visible");
        await Firstrow.WaitForAsync();
        _logger.Debug("Filter for PONull.pdf applied successfully");
    }
    //upload .pdf file for POExact match
    public async Task UploadedPdfDocument()
    {
        _logger.Debug("Uploading PDF document: POExactMatch.pdf");
        await UploadAndScanFiles(".\\UploadFiles\\POExactMatch.pdf");
        _logger.Debug("PDF document uploaded successfully");
    }
    //Filter the .pdf file
    public async Task FilterPdf()
    {
        _logger.Debug("Filtering for PDF file: POExactMatch.pdf");
        _logger.Debug("Clicking file name filter button");
        await fileNameFilterBtn.ClickAsync();
        _logger.Debug("Clicking filter input field");
        await filterInput.ClickAsync();
        _logger.Debug("Filling filter with POExactMatch.pdf");
        await filterInput.FillAsync("POExactMatch.pdf");
        _logger.Debug("Clicking filter button");
        await filterButton.ClickAsync();
        _logger.Debug("Waiting for first row to be visible");
        await Firstrow.WaitForAsync();
        _logger.Debug("Filter for POExactMatch.pdf applied successfully");
    }
    //upload .pdf file for POMultiple match
    public async Task UploadedPdfRecord()
    {
        _logger.Debug("Uploading PDF for multiple PO match: POMultiple.pdf");
        await UploadAndScanFiles(".\\UploadFiles\\POMultiple.pdf");
        _logger.Debug("Multiple PO match PDF uploaded successfully");
    }
    //Filter the .pdf record
    public async Task SearchUploadedpdfRecord()
    {
        _logger.Debug("Searching for uploaded PDF record: POMultiple.pdf");
        await SearchUploadedFile("POMultiple.pdf");
        _logger.Debug("Search for POMultiple.pdf completed");
    }
    //Filter the .pdf file
    public async Task FilteredPdf()
    {
        _logger.Debug("Filtering for PDF file: DNDInvalidPO.pdf");
        _logger.Debug("Clicking file name filter button");
        await fileNameFilterBtn.ClickAsync();
        _logger.Debug("Clicking filter input field");
        await filterInput.ClickAsync();
        _logger.Debug("Filling filter with DNDInvalidPO.pdf");
        await filterInput.FillAsync("DNDInvalidPO.pdf");
        _logger.Debug("Clicking filter button");
        await filterButton.ClickAsync();
        _logger.Debug("Waiting for first row to be visible");
        await Firstrow.WaitForAsync();
        _logger.Debug("Filter for DNDInvalidPO.pdf applied successfully");
    }
    //Logoff
    public async Task Logoff()
    {
        _logger.Debug("Logging off from the application");
        await Logoffbtn.ClickAsync();
        _logger.Debug("Logoff initiated");
    }

    private async Task<JObject?> CreateFrameAsync(ILocator triggerButton, Context _context,bool ocrResp = false,CommonContext? commonContext=null){
        _logger.Debug("Creating frame by clicking trigger button");
        JObject? resp = null;
        var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await triggerButton.ClickAsync();
        });
        IPage oldPage = Page;
        _context.Page = newPage;
        Page = newPage;
        _logger.Debug("New page created, closing old page");
        await oldPage.CloseAsync();
        
        if(ocrResp && commonContext !=null){
            _logger.Debug("OCR response requested, extracting URL parameters");
            var url = Page.Url;
            var a = url.IndexOf("=")+1;
		    var b = url.IndexOf("&");
		    var c = b-a;
            var sub = url.Substring(a,c);
            _logger.Debug("Waiting for OCR response with parameter: {Param}", sub);
            resp = await WaitForResponseAsyncObj("GetOcrResponse",commonContext);
            _logger.Debug("OCR response received");
        }
        
        // Trigger action and wait for the new page
        _logger.Debug("Waiting for page to stabilize");
        await Task.Delay(1000);
        
        // Click the button inside the frame if visible
        if(await IsVisibleAsync(_closeBtnX,1000,5)){
            _logger.Debug("Close button X is visible, clicking");
            await _closeBtnX.ClickAsync();
        }
        
        _logger.Debug("Frame creation completed");
        return resp;
    }

    public async Task ConfirmAndCreateDocumentDifferentLineItems(Context context)
    {
        _logger.Debug("Starting confirmation and document creation for different line items");
        await FileInReviewState();
        if(await ReviewPanelIsReadyAsync()){
            _logger.Debug("Review panel is ready, clicking document text field");
            await DocTextField.ClickAsync();
            _logger.Debug("Generating random document number");
            await RandomNumber();
            string documentNumber = await DocTextField.InputValueAsync();
            _logger.Debug("Generated document number: {Number}", documentNumber);
            
            _logger.Debug("Waiting 5 seconds for page stability");
            await Task.Delay(5000);
            
            _logger.Debug("Selecting supplier");
            await Supplier.ClickAsync();
            await SupplierName.ClickAsync();
            
            _logger.Debug("Waiting for Create button to be visible");
            await CreateButton.WaitForAsync(new LocatorWaitForOptions
            {
                State = WaitForSelectorState.Visible
            });
            
            _logger.Debug("Creating frame by clicking Create button");
            await CreateFrameAsync(CreateButton,context);

            _logger.Debug("Validating document values");
            await ValidateValues(_invoiceNumber, documentNumber, "The document number is not the same.");
            await ValidateOCRVisibility(_invoiceNumberOCRBadge);
            //await ValidateText(DocPONum, RecordPONum, "The PO Number is not the same.");
            await ValidateOCRVisibility(DocumentOCRBadge);
            await ValidateDate(DocInvoiceDate, RecordInvoiceDate, "The Invoice Date is not the same.");
            await ValidateOCRVisibility(DateOCRBadge);
            await ValidateCurrency(DocDiscount, RecordDiscount, "The Discount is not the same.");
            await ValidateOCRVisibility(DocDiscountOCRBadge);
            await ValidateCurrency(DocShipping, RecordShipping, "The Shipping is not the same.");
            await ValidateOCRVisibility(DocShippingOCRBadge);
            await ValidateCurrency(DocTax, RecordTax, "The Taxes is not the same.");
            await ValidateOCRVisibility(DocTaxOCRBadge);
            await ValidateCurrency(DocGrandTotal, RecordGrandTotal, "The Total is not the same.");
            await ValidateOCRVisibility(GrandTotalOCRBadge);
            await ValidateCurrency(DocLineItemQty, RecordLineItemQty, "The Item Quantity is not the same.");
            await ValidateOCRVisibility(DocLineItemQtyOCRBadge);
            await ValidateLineItems(DocLineItemName, RecordItemName, "The Item Name is not the same.");
            await ValidateOCRVisibility(DocLineItemNameOCRBadge);
            await ValidateLineItems(DocLineItemDesc, RecordItemDesc, "The Item Description is not the same.");
            await ValidateOCRVisibility(DocLineItemDescOCRBadge);
            await ValidateLineItems(DocLineItemUOM, RecordItemUOM, "The Item UOM is not the same.");
            await ValidateOCRVisibility(DocLineItemUOMOCRBadge);
            await ValidateCurrency(DocLineItemPrice, RecordItemPrice, "The Item Price is not the same.");
            await ValidateOCRVisibility(DocLineItemPriceOCRBadge);
            _logger.Debug("Document validation completed successfully");
        }else{
            _logger.Error("Review panel is not ready");
            Assert.Fail("Review panel is not ready");
        }
    }
    public async Task ConfirmAndCreateDocumentSameLineItems(Context context)
    {
        _logger.Debug("Starting confirmation and document creation for same line items");
        await FileInReviewState();
        if(await ReviewPanelIsReadyAsync()){
            _logger.Debug("Review panel is ready, clicking document text field");
            await DocTextField.ClickAsync();
            _logger.Debug("Generating random document number");
            await RandomNumber();
            string documentNumber = await DocTextField.InputValueAsync();
            _logger.Debug("Generated document number: {Number}", documentNumber);
            
            _logger.Debug("Selecting supplier");
            await Supplier.ClickAsync();
            await SupplierName.ClickAsync();
            
            _logger.Debug("Waiting for Create button to be visible");
            await CreateButton.WaitForAsync(new LocatorWaitForOptions
            {
                State = WaitForSelectorState.Visible
            });
            
            _logger.Debug("Creating frame by clicking Create button");
            await CreateFrameAsync(CreateButton,context);
            
            _logger.Debug("Validating document values");
            await ValidateValues(_invoiceNumber, documentNumber, "The document number is not the same.");
            await ValidateOCRVisibility(_invoiceNumberOCRBadge);
            //await ValidateText(DocPONum, RecordExactMatchPONum, "The PO Number is not the same.");
            await ValidateOCRVisibility(DocumentOCRBadge);
            await ValidateDate(DocInvoiceDate, RecordExactMatchInvoiceDate, "The Invoice Date is not the same.");
            await ValidateOCRVisibility(DateOCRBadge);
            await ValidateCurrency(DocDiscount, RecordDiscountExactMatch, "The Discount is not the same.");
            await ValidateOCRVisibility(DocDiscountOCRBadge);
            await ValidateCurrency(DocShipping, RecordShippingExactMatch, "The Shipping is not the same.");
            await ValidateOCRVisibility(DocShippingOCRBadge);
            await ValidateCurrency(DocTax, RecordTaxExactMatch, "The Taxes is not the same.");
            await ValidateOCRVisibility(DocTaxOCRBadge);
            await ValidateCurrency(DocGrandTotal, RecordGrandTotalExactMatch, "The Total is not the same.");
            await ValidateOCRVisibility(GrandTotalOCRBadge);
            await ValidateCurrency(DocLineItemQtyExactMatch, RecordLineItemQtyExactMatch, "The Item Quantity is not the same.");
            await ValidateOCRVisibility(DocLineItemQtyExactOCRBadge);
            await ValidateText(DocLineItemNameExactMatch, RecordItemNameExaxtMatch, "The Item Name is not the same.");
            await ValidateOCRVisibility(DocLineItemNameExactOCRBadge);
            await ValidateText(DocLineItemDescExactMatch, RecordItemDescExactMatch, "The Item Description is not the same.");
            await ValidateOCRVisibility(DocLineItemDescExactMatchOCRBadge);
            await ValidateLineItems(DocLineItemUOMExactMatch, RecordItemUOMExactMatch, "The Item UOM is not the same.");
            await ValidateOCRVisibility(DocLineItemUOMExactMatchOCRBadge);
            await ValidateCurrency(DocLineItemPriceExactMatch, RecordItemPriceExactMatch, "The Item Price is not the same.");
            await ValidateOCRVisibility(DocLineItemPriceExactMatchOCRBadge);
            _logger.Debug("Document validation completed successfully");
        }else{
            _logger.Error("Review panel is not ready");
            Assert.Fail("Review panel is not ready");
        }
    }
    private async Task RandomNumber()
    {
        _logger.Debug("Generating random document number");
        Random random = new Random();
        int randomNumber = random.Next(10000, 99999);
        _logger.Debug("Random number generated: {Number}", randomNumber);
        await DocTextField.FillAsync(randomNumber.ToString());
        _logger.Debug("Document field filled with random number");
    }
    private async Task ValidateValues(ILocator locator, string expectedValue, string message)
    {
        _logger.Debug("Validating input value against expected value");
        var actualValue = await locator.InputValueAsync()??"";
        _logger.Debug("Actual value: '{Actual}', Expected value: '{Expected}'", actualValue, expectedValue);
        Assert.That(actualValue, Is.EqualTo(expectedValue), message);
        _logger.Debug("Value validation passed");
    }
    private async Task ValidateText(ILocator element1, ILocator element2, string errorMessage)
    {
        _logger.Debug("Validating text content between two elements");
        string value1 = await element1.TextContentAsync()??"";
        string value2 = await element2.TextContentAsync()??"";
        _logger.Debug("Original values - Element 1: '{Value1}', Element 2: '{Value2}'", value1, value2);
        
        value1 = value1.Replace("PO: ", "").Trim();
        value2 = value2.Replace("PO: ", "").Trim();
        _logger.Debug("Cleaned values - Element 1: '{Value1}', Element 2: '{Value2}'", value1, value2);
        
        Assert.That(value1, Is.EqualTo(value2), errorMessage);
        _logger.Debug("Text validation passed");
    }
    private async Task ValidateDate(ILocator dateElement1, ILocator dateElement2, string errorMessage)
    {
        _logger.Debug("Validating date between two elements");
        string rawDate1 = await dateElement1.InputValueAsync()??"";
        string rawDate2 = await dateElement2.TextContentAsync()??"";
        _logger.Debug("Raw dates - Element 1: '{RawDate1}', Element 2: '{RawDate2}'", rawDate1, rawDate2);

        // Remove any prefix like "Date: " and trim spaces
        string cleanDate1 = ExtractDate(rawDate1);
        string cleanDate2 = ExtractDate(rawDate2);
        _logger.Debug("Cleaned dates - Element 1: '{CleanDate1}', Element 2: '{CleanDate2}'", cleanDate1, cleanDate2);

        // Convert to DateTime for consistent formatting
        DateTime date1 = DateTime.Parse(cleanDate1);
        DateTime date2 = DateTime.Parse(cleanDate2);
        _logger.Debug("Parsed dates - Element 1: '{Date1}', Element 2: '{Date2}'", date1.ToString("MM/dd/yyyy"), date2.ToString("MM/dd/yyyy"));
        
        Assert.That(date1.ToString("MM/dd/yyyy"), Is.EqualTo(date2.ToString("MM/dd/yyyy")), errorMessage);
        _logger.Debug("Date validation passed");
    }
    private string ExtractDate(string rawDate)
    {
        _logger.Debug("Extracting date from raw string: '{RawDate}'", rawDate);
        string extractedDate = rawDate.Replace("Date:", "").Trim();
        _logger.Debug("Extracted date: '{ExtractedDate}'", extractedDate);
        return extractedDate;
    }
    private async Task ValidateCurrency(ILocator element1, ILocator element2, string errorMessage)
    {
        _logger.Debug("Validating currency values between two elements");
        string value1 = (await element1.InputValueAsync()??"").Replace("$", "");
        string value2 = (await element2.TextContentAsync()??"").Replace("$", "");
        _logger.Debug("Raw values after removing $ - Element 1: '{Value1}', Element 2: '{Value2}'", value1, value2);
        
        // Convert to decimal and format to 2 decimal places
        decimal decimalValue1 = decimal.Parse(value1);
        decimal decimalValue2 = decimal.Parse(value2);
        _logger.Debug("Parsed decimal values - Element 1: {Value1}, Element 2: {Value2}", decimalValue1, decimalValue2);
        
        string formattedValue1 = decimalValue1.ToString("0.00");
        string formattedValue2 = decimalValue2.ToString("0.00");
        _logger.Debug("Formatted values (2 decimal places) - Element 1: '{Value1}', Element 2: '{Value2}'", formattedValue1, formattedValue2);
        
        Assert.That(formattedValue1, Is.EqualTo(formattedValue2), errorMessage);
        _logger.Debug("Currency validation passed");
    }
    private async Task ValidateLineItems(ILocator element1, ILocator element2, string errorMessage)
    {
        _logger.Debug("Validating line item values between two elements");
        string value1 = await element1.InputValueAsync()??"";
        string value2 = await element2.TextContentAsync()??"";
        _logger.Debug("Line item values - Element 1: '{Value1}', Element 2: '{Value2}'", value1, value2);
        
        Assert.That(value1, Is.EqualTo(value2), errorMessage);
        _logger.Debug("Line item validation passed");
    }
    private async Task ValidateOCRVisibility(ILocator badge)
    {
        bool isVisible = await badge.CountAsync() > 0;
        _logger.Debug("OCR Badge visibility: {Visibility}", isVisible ? "Visible" : "Not visible");
        Console.WriteLine(isVisible ? "OCR Badge is visible." : "OCR Badge is not visible.");
    }

    public async Task UpdateInvoiceInPDF(string inputPdfPath, string outputPdfPath, string oldText, string newText)
    {
        _logger.Debug("Starting PDF update operation");
        _logger.Debug("Input PDF path: {InputPath}", inputPdfPath);
        _logger.Debug("Output PDF path: {OutputPath}", outputPdfPath);
        _logger.Debug("Text to replace: '{OldText}' with '{NewText}'", oldText, newText);
        
        await Task.Run(()=>{});
        
        try {
            _logger.Debug("Loading PDF document");
            PdfDocument doc = new PdfDocument(inputPdfPath);
            _logger.Debug("PDF document loaded successfully with {PageCount} pages", doc.Pages.Count);
            
            #pragma warning disable CS0618 // Type or member is obsolete
            PdfTextFind[]? result = null;
            #pragma warning restore CS0618 // Type or member is obsolete
            
            int replacementCount = 0;
            int pageIndex = 1;
            foreach (PdfPageBase page in doc.Pages)
            {
                _logger.Debug("Processing page {PageNumber}", pageIndex);
                #pragma warning disable CS0618 // Type or member is obsolete
                result = page.FindText(oldText, TextFindParameter.WholeWord).Finds;
                _logger.Debug("Found {Count} instances of text to replace on page {PageNumber}", result.Length, pageIndex);
                
                foreach (PdfTextFind find in result)
                {
                    _logger.Debug("Replacing text at position X:{X}, Y:{Y}", find.Position.X, find.Position.Y);
                    find.ApplyRecoverString(newText);
                    replacementCount++;
                }
                #pragma warning restore CS0618 // Type or member is obsolete
            }
            
            _logger.Debug("Total replacements made: {Count}", replacementCount);
            _logger.Debug("Saving modified PDF to output path");
            doc.SaveToFile(outputPdfPath, FileFormat.PDF);
            _logger.Debug("PDF saved successfully");
        }
        catch (Exception ex)
        {
            _logger.Error("Error updating PDF: {ErrorMessage}", ex.Message);
            throw;
        }
    }

    private void AssertFun(string ocrval, string ocrconf, string docval, string docconf){
        _logger.Debug("Asserting OCR values match document values");
        _logger.Debug("OCR value: '{OcrValue}', OCR confidence: '{OcrConfidence}'", ocrval, ocrconf);
        _logger.Debug("Document value: '{DocValue}', Document confidence: '{DocConfidence}'", docval, docconf);
        
        Assert.That(docval, Does.Contain(ocrval), "Document value does not contain OCR value");
        Assert.That(docconf, Does.Contain(ocrconf), "Document confidence does not contain OCR confidence");
        
        _logger.Debug("Assertion passed successfully");
    }

    private string ConvertPercent(string value){
        _logger.Debug("Converting percentage value: '{Value}'", value);
        
        value = value.Replace(System.Globalization.CultureInfo.CurrentCulture.NumberFormat.PercentSymbol, "");
        _logger.Debug("After removing percent symbol: '{Value}'", value);
        
        string result = value.Substring(0, value.IndexOf('.', 0));
        _logger.Debug("After extracting integer part: '{Result}'", result);
        
        return result;
    }
    public async Task VerifyInvInRecords(string fileName,CommonContext commonContext,Context context){
        _logger.Debug("Starting verification of invoice in records for file: {FileName}", fileName);
        
        _logger.Debug("Waiting for table to be ready");
        await WaitUntilNotVisibleInTable(FirstrowInTble,30000,20);
        
        _logger.Debug("Navigating to Records tab");
        await Recordstab.ClickAsync();
        
        _logger.Debug("Waiting for page to stabilize");
        await Task.Delay(3000);
        
        _logger.Debug("Filtering for uploaded file");
        await FilterUploadedFile(fileName);
        
        _logger.Debug("Getting PO number from first row");
        var poNum = await FirstrowInTble.Locator("td:nth-child(4)").InnerTextAsync()??"";
        _logger.Debug("PO number from table: {PONumber}", poNum);
        //Assert.That(poNum, Is.EqualTo(commonContext.PoNumber));
        
        _logger.Debug("Creating frame and getting OCR response");
        JObject? resp = await CreateFrameAsync(FirstrowInTble.Locator("td:nth-child(4)"),context,true,commonContext);
        
        if (resp != null && resp["invoice"] != null)
        {
            _logger.Debug("OCR response contains invoice data, validating fields");
            var root = resp["invoice"];
            
            _logger.Debug("Validating invoice number");
            AssertFun(root?["invoiceNumber"]?["value"]?.ToString()??"",root?["invoiceNumber"]?["confidence"]?.ToString()??"",await invoiceDetailsInput.Nth(0).InputValueAsync()??"",await invoiceDetailsOcrTooltip.Nth(0).GetAttributeAsync("data-tooltip-html")??"");
            
            _logger.Debug("Validating purchase order");
            AssertFun(root?["purchaseOrder"]?["value"]?.ToString()??"",root?["purchaseOrder"]?["confidence"]?.ToString()??"",await invoiceDetailsReadonly.Nth(0).InnerTextAsync()??"",await invoiceDetailsOcrTooltip.Nth(1).GetAttributeAsync("data-tooltip-html")??"");
            
            //AssertFun(root?["supplierName"]?["value"]?.ToString()??"",root?["supplierName"]?["confidence"]?.ToString()??"",await invoiceDetailsReadonly.Nth(1).InnerTextAsync()??"",await invoiceDetailsOcrTooltip.Nth(2).GetAttributeAsync("data-tooltip-html")??"");
            
            _logger.Debug("Validating invoice date");
            AssertFun(root?["invoiceDate"]?["value"]?.ToString()??"",root?["invoiceDate"]?["confidence"]?.ToString()??"",await invoiceDetailsInput.Nth(1).InputValueAsync()??"",await invoiceDetailsOcrTooltip.Nth(2).GetAttributeAsync("data-tooltip-html")??"");
            
            _logger.Debug("Validating total discount");
            AssertFun(root?["totalDiscount"]?["value"]?.ToString()??"",root?["totalDiscount"]?["confidence"]?.ToString()??"",await invoiceDetailsInput.Nth(5).InputValueAsync()??"",await invoiceDetailsOcrTooltip.Nth(3).GetAttributeAsync("data-tooltip-html")??"");
            
            _logger.Debug("Validating total shipping");
            AssertFun(root?["totalShipping"]?["value"]?.ToString()??"",root?["totalShipping"]?["confidence"]?.ToString()??"",await invoiceDetailsInput.Nth(6).InputValueAsync()??"",await invoiceDetailsOcrTooltip.Nth(4).GetAttributeAsync("data-tooltip-html")??"");
            
            _logger.Debug("Validating total tax");
            AssertFun(root?["totalTax"]?["value"]?.ToString()??"",root?["totalTax"]?["confidence"]?.ToString()??"",await invoiceDetailsInput.Nth(7).InputValueAsync()??"",await invoiceDetailsOcrTooltip.Nth(5).GetAttributeAsync("data-tooltip-html")??"");
            
            _logger.Debug("Validating amount due");
            AssertFun(root?["amountDue"]?["value"]?.ToString()??"",root?["amountDue"]?["confidence"]?.ToString()??"",await invoiceDetailsInput.Nth(8).InputValueAsync()??"",await invoiceDetailsOcrTooltip.Nth(6).GetAttributeAsync("data-tooltip-html")??"");
            
            _logger.Debug("Checking for line items in OCR response");
            JArray? array = root?["items"] as JArray;
            if (array != null)
            {
                _logger.Debug("Found {Count} line items in OCR response", array.Count);
                foreach (JObject item in array)
                {
                    var itemroot = item?["item"];
                    var val = itemroot?["itemNumber"]?["value"]?.ToString()??"";
                    var conf = itemroot?["itemNumber"]?["confidence"]?.ToString()??"";
                    
                    _logger.Debug("Processing line item with number: {ItemNumber}", val);
                    var rows = await invoiceLineItems.AllAsync();
                    var row = rows.FirstOrDefault(row => row.InnerTextAsync().Result.Contains(val));
                    
                    if(row!=null){
                        _logger.Debug("Found matching row in UI for item number: {ItemNumber}", val);
                        
                        _logger.Debug("Validating item number");
                        AssertFun(val,ConvertPercent(conf),await row.Locator("td:nth-child(8)>div>div:nth-child(1)").InnerTextAsync()??"",await row.Locator("td:nth-child(8)>div>div:nth-child(2) >div").GetAttributeAsync("data-tooltip-html")??"");
                        
                        _logger.Debug("Validating item quantity");
                        AssertFun(Math.Round(Convert.ToDecimal(itemroot?["itemQuantity"]?["value"]?.ToString()),2)+""??"",ConvertPercent(itemroot?["itemQuantity"]?["confidence"]?.ToString()??""),await row.Locator("td:nth-child(6)>div>div:nth-child(1) input").InputValueAsync()??"",await row.Locator("td:nth-child(6)>div>div:nth-child(2)>div").GetAttributeAsync("data-tooltip-html")??"");
                        
                        _logger.Debug("Validating item price");
                        AssertFun(itemroot?["itemPrice"]?["value"]?.ToString()??"",ConvertPercent(itemroot?["itemPrice"]?["confidence"]?.ToString()??""),await row.Locator("td:nth-child(12)>div>div:nth-child(1) input").InputValueAsync()??"",await row.Locator("td:nth-child(12)>div>div:nth-child(2)>div").GetAttributeAsync("data-tooltip-html")??"");
                        
                        _logger.Debug("Validating item UOM");
                        AssertFun(itemroot?["uom"]?["value"]?.ToString()??"",ConvertPercent(itemroot?["uom"]?["confidence"]?.ToString()??""),await row.Locator("td:nth-child(11)>div>div:nth-child(1) input").InputValueAsync()??"",await row.Locator("td:nth-child(11)>div>div:nth-child(2)>div").GetAttributeAsync("data-tooltip-html")??"");
                    }
                    else {
                        _logger.Warning("No matching row found in UI for item number: {ItemNumber}", val);
                    }
                }
            }
            
            _logger.Debug("Checking scan panel buttons count");
            var c = await scanPanelButtons.CountAsync();
            _logger.Debug("Scan panel buttons count: {Count}", c);
            Assert.That(c, Is.EqualTo(0));
            
            _logger.Debug("Setting received date (7)");
            await _receivedDate(7).ClickAsync();
            await _receivedDateSelect.ClickAsync();
            
            _logger.Debug("Filling item descriptions with PO number");
            foreach (var input in await _itemDesc(3).AllAsync())
            {
                await input.FillAsync(commonContext.PoNumber);
            }
            
            _logger.Debug("Setting received date (6)");
            await _receivedDate(6).ClickAsync();
            await _receivedDateSelect.ClickAsync();

            _logger.Debug("Checking and setting app ID if empty");
            var appId = await invoiceDetailsInput.Nth(3).InputValueAsync()??"";
            if(string.Empty == appId){
                _logger.Debug("App ID is empty, filling with default value");
                await invoiceDetailsInput.Nth(3).FillAsync("123456");
            }
            
            _logger.Debug("Verifying tooltip contains 'Edited'");
            Assert.That(await invoiceDetailsOcrTooltip.Nth(2).GetAttributeAsync("data-tooltip-html")??"",Does.Contain("Edited"));
            
            _logger.Debug("Checking and setting ext if empty");
            var ext = await invoiceDetailsInput.Nth(4).InputValueAsync()??"";
            if(string.Empty == ext){
                _logger.Debug("Ext is empty, filling with default value");
                await invoiceDetailsInput.Nth(4).FillAsync("123456");
            }
            
            _logger.Debug("Updating item total input");
            var totalText = await _itemTotal(9).TextContentAsync() ?? string.Empty;
            var amount = GetAmount(totalText);
            _logger.Debug("Calculated amount from total text: {Amount}", amount);
            await _itemTotalInput.FillAsync(amount + "");
            
            _logger.Debug("Waiting for UI to stabilize");
            Thread.Sleep(2000);
            
            _logger.Debug("Clicking save button");
            await _saveBtn.ClickAsync();
            
            _logger.Debug("Waiting for UI to stabilize after save");
            Thread.Sleep(2000);
            
            _logger.Debug("Checking if complete button is visible");
            if(!await IsVisibleAsync(_completeBtn,1000,5)){
                _logger.Error("Complete button not visible - permission 743 may be needed");
                Assert.Fail("Complete button not visible please enable perm 743");
            }
            
            _logger.Debug("Clicking complete button");
            await _completeBtn.ClickAsync();
            
            _logger.Debug("Checking for confirmation popup");
            if(await IsVisibleAsync(_confirmPopupBtn,1000,5)){
                _logger.Debug("Confirmation popup visible, clicking confirm");
                await _confirmPopupBtn.ClickAsync();
            }
            
            _logger.Debug("Checking if close PO button is visible");
            if(!await IsVisibleAsync(_closePOBtn,1000,5)){
                _logger.Error("Close PO button not visible - permission 698 may be needed");
                Assert.Fail("enable 698 perm for this user");
            }
            
            _logger.Debug("Clicking close PO button");
            await _closePOBtn.ClickAsync();
            
            _logger.Debug("Waiting for confirm button");
            await _confirmBtn.WaitForAsync();
            
            _logger.Debug("Clicking confirm button");
            await _confirmBtn.ClickAsync();
            
            _logger.Debug("Waiting for confirm popup button");
            await _confirmPopupBtn.WaitForAsync();
            
            _logger.Debug("Clicking confirm popup button");
            await _confirmPopupBtn.ClickAsync();
            
            _logger.Debug("Invoice verification and processing completed successfully");
        }
        else {
            _logger.Warning("OCR response is null or does not contain invoice data");
        }
    }
    /// <summary>
    /// Checks if the upload button is disabled, which happens when "Only .PDF formats accepts" permission is ON
    /// </summary>
    /// <returns>True if the button is disabled, false otherwise</returns>
    public async Task<bool> IsUploadButtonDisabled()
    {
        _logger.Debug("Checking if upload button is disabled");
        try
        {
            // Check if the button has the 'disabled' attribute or class
            string? classAttribute = await uploadButton.GetAttributeAsync("class");
            string? disabledAttribute = await uploadButton.GetAttributeAsync("disabled");

            // Check if the button is actually disabled in the DOM
            bool isDisabled = await uploadButton.EvaluateAsync<bool>("el => el.disabled");

            // Log the findings for debugging
            _logger.Debug("Upload button class: {Class}", classAttribute);
            _logger.Debug("Upload button disabled attribute: {Attribute}", disabledAttribute);
            _logger.Debug("Upload button is disabled in DOM: {IsDisabled}", isDisabled);
            Console.WriteLine($"Upload button class: {classAttribute}");
            Console.WriteLine($"Upload button disabled attribute: {disabledAttribute}");
            Console.WriteLine($"Upload button is disabled in DOM: {isDisabled}");

            // Return true if any of the checks indicate the button is disabled
            bool result = isDisabled || disabledAttribute != null || (classAttribute != null && classAttribute.Contains("disabled"));
            _logger.Debug("Upload button disabled status: {Status}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.Error("Error checking if upload button is disabled: {ErrorMessage}", ex.Message);
            Console.WriteLine($"Error checking if upload button is disabled: {ex.Message}");
            // If there's an error, assume the button is not disabled
            return false;
        }
    }

    public async Task<bool> IsUploadAndScanFilesButtonDisabled()
    {
        _logger.Debug("Checking if upload and scan files button is disabled");
        try
        {
            // Check if the button has the 'disabled' attribute or class
            string? classAttribute = await uploadButtonInOCR.GetAttributeAsync("class");
            string? disabledAttribute = await uploadButtonInOCR.GetAttributeAsync("disabled");

            // Check if the button is actually disabled in the DOM
            bool isDisabled = await uploadButtonInOCR.EvaluateAsync<bool>("el => el.disabled");

            // Log the findings for debugging
            _logger.Debug("Upload and scan button class: {Class}", classAttribute);
            _logger.Debug("Upload and scan button disabled attribute: {Attribute}", disabledAttribute);
            _logger.Debug("Upload and scan button is disabled in DOM: {IsDisabled}", isDisabled);
            Console.WriteLine($"Upload button class: {classAttribute}");
            Console.WriteLine($"Upload button disabled attribute: {disabledAttribute}");
            Console.WriteLine($"Upload button is disabled in DOM: {isDisabled}");

            // Return true if any of the checks indicate the button is disabled
            bool result = isDisabled || disabledAttribute != null || (classAttribute != null && classAttribute.Contains("disabled"));
            _logger.Debug("Upload and scan button disabled status: {Status}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.Error("Error checking if upload and scan button is disabled: {ErrorMessage}", ex.Message);
            Console.WriteLine($"Error checking if upload button is disabled: {ex.Message}");
            // If there's an error, assume the button is not disabled
            return false;
        }
    }

    public async Task FilterUploadedFile(string fileName){
        _logger.Debug("Filtering for uploaded file: {FileName}", fileName);
        _logger.Debug("Clicking file name filter button");
        await fileNameFilterBtn.ClickAsync();
        
        _logger.Debug("Clicking filter input field");
        await filterInput.ClickAsync();
        
        _logger.Debug("Filling filter input with filename");
        await filterInput.FillAsync(fileName);
        
        _logger.Debug("Clicking filter button");
        await filterButton.ClickAsync();
        
        _logger.Debug("Waiting for first row to be visible");
        await Firstrow.WaitForAsync();
        
        _logger.Debug("Filter for {FileName} applied successfully", fileName);
    }
    public async Task UseUpdateInvoiceInPDF(CommonContext commonContext)
    {
        _logger.Debug("Starting PDF update and upload process");
        
        string inputPdfPath = ".\\UploadFiles\\origPO.pdf";
        string outputPdfPath = ".\\UploadFiles\\newPO.pdf";
        string oldText = "34657632590";
        
        _logger.Debug("Input PDF path: {InputPath}", inputPdfPath);
        _logger.Debug("Output PDF path: {OutputPath}", outputPdfPath);
        _logger.Debug("Text to replace: '{OldText}'", oldText);
        
        if (commonContext.PoNumber == null)
        {
            _logger.Error("PO Number is null in CommonContext");
            throw new ArgumentNullException(nameof(commonContext.PoNumber));
        }
        
        string newText = commonContext.PoNumber;
        _logger.Debug("New PO Number to insert: '{NewText}'", newText);
        
        _logger.Debug("Calling UpdateInvoiceInPDF method");
        await UpdateInvoiceInPDF(inputPdfPath, outputPdfPath, oldText, newText);
        
        _logger.Debug("PDF updated successfully, clicking upload button");
        await uploadButton.ClickAsync();
        
        _logger.Debug("Setting input file to modified PDF");
        await uploadPdf.SetInputFilesAsync(".\\UploadFiles\\newPO.pdf");
        
        _logger.Debug("Clicking Upload and Scan button");
        await UploadAndScanBtn.ClickAsync();
        
        _logger.Debug("Waiting for upload success message");
        await uploadSuccessMsg.WaitForAsync();
        
        _logger.Debug("Verifying success message is visible");
        await Assertions.Expect(uploadSuccessMsg).ToBeVisibleAsync();
        
        _logger.Debug("Closing upload dialog");
        await uploadClose.ClickAsync();
        
        _logger.Debug("PDF update and upload process completed successfully");
    }
}
