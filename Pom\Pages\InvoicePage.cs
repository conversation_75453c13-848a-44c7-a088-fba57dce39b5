using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.Playwright;
using SpecFlowProject.BusinessObjects;
using System.Globalization;
namespace SpecFlowProject.Pom.Pages;
using System.Text.RegularExpressions;
using DocumentFormat.OpenXml.Office2010.PowerPoint;
using SpecFlowProject.Utils;


public class InvoicePage: Base
{
    public InvoicePage(IPage page) : base(page)
    {

    }
    private ILocator _receivedDate => _iframe.Locator("div:nth-child(7) > div > span > span > button > span");
    private ILocator _invDate => _iframe.Locator("div:nth-child(6) > div > span > span > button > span");
    private ILocator _receivedDateSelect => _iframe.Locator(".k-calendar-nav-today");
    private ILocator _invoiceInput => _iframe.Locator(".k-widget >.k-pane >div>main> div:nth-child(3) div:nth-child(2) .grid input").First;

    private ILocator _itemDesc(int i) => _iframe.Locator(".k-widget >.k-pane >div>main >div:nth-child(4) table tbody tr td:nth-child("+i+") input");
    private ILocator _itemtotal(int i) => _iframe.Locator(".k-widget >.k-pane >div>main >div:nth-child(4) table tbody tr.k-master-row:last-child td:nth-child("+i+")");
    private ILocator _itemsAmt(int i,int j) => _iframe.Locator("main >div:nth-child(4) table tbody tr:nth-child("+i+") td:nth-child("+j+")");
    private ILocator _itemsDiscount(int i,int j) => _iframe.Locator("main >div:nth-child(4) table tbody tr:nth-child("+i+") td:nth-child("+j+")");
    private ILocator _itemsShipping(int i,int j) => _iframe.Locator("main >div:nth-child(4) table tbody tr:nth-child("+i+") td:nth-child("+j+")");
    private ILocator _itemsTax(int i,int j) => _iframe.Locator("main >div:nth-child(4) table tbody tr:nth-child("+i+") td:nth-child("+j+")");
    private ILocator _invoiceItems => _iframe.Locator(".k-widget >.k-pane >div>main> div:nth-child(4) #invoiceGLSummaryGrid>.k-grid-container>.k-grid-content>div>table>tbody>tr.k-master-row:not(:last-child)"); 
    private ILocator _itemQuantity => _iframe.Locator(".k-widget >.k-pane >div>main> div:nth-child(5) table tbody tr td:nth-child(6) input");

    

    private ILocator _itemTaxes => _iframe.Locator(".k-widget >.k-pane >div>main> div:nth-child(5) table tbody tr td:nth-child(5) #invoiceSwitch .k-switch.k-switch-on");
    private ILocator _itemTotal(int i) => _iframe.Locator(".k-widget >.k-pane >div>main>div:nth-child(4) table tbody tr.k-master-row:last-child td:nth-child("+i+")");
    private ILocator _itemTotalInput => _iframe.Locator(".k-widget >.k-pane >div>main >div:nth-child(3) div:nth-child(2) .grid input").Last;
    private ILocator _enterPO => _iframe.Locator("main > div > div:nth-child(2) > input"); 
    private ILocator _tableData => _iframe.Locator("table tbody tr:nth-child(1) td:nth-child(2)");
    private ILocator _invPopupBtn => _iframe.Locator(".k-window-actions.k-dialog-actions button"); 
    private ILocator _documentNumber => _iframe.Locator(".w-full input");
    private ILocator _firstDivOpen => _iframe.Locator("div > main > div.rounded-lg:nth-child(2)");
    private ILocator _createPO => _iframe.GetByRole(AriaRole.Button, new() {Name = "Create New Supplier"});
    private ILocator _searchSupplierName => _iframe.Locator(".k-window-content.k-dialog-content div input");
    private ILocator _selectSupplier => _iframe.Locator(".k-widget.k-window.k-dialog .k-widget.k-grid tbody tr td:nth-child(1)");
    private ILocator _createInvoiceBtn => _iframe.Locator(".flex.justify-center.gap-2.mb-1 button:nth-child(1)");
    private ILocator _createSupplier => _iframe.Locator(".flex.justify-center.gap-2.mb-1 button:nth-child(2)");
    private ILocator _deleteBtn => _iframe.Locator("div > main > div > div:first-child button:has-text('Delete')");
    private ILocator _unDeleteBtn => _iframe.Locator("button:has-text('Undelete')");
    private ILocator _submitApprovalGrid(int i) => _iframe.Locator(".k-window-content.k-dialog-content .k-grid-container tr:nth-child("+i+") td:nth-child(1)");
    private ILocator _submitForApprovalBtn => _iframe.Locator("button:has-text('Submit for Approval')");
    private ILocator _sendForSubmitApproval => _iframe.Locator(".flex.flex-row.items-center.justify-end.gap-2 button:nth-child(1)");
    private ILocator _submitApprovalGridselected(int i) =>_iframe.Locator(".k-window-content.k-dialog-content .k-grid-container tr:nth-child("+i+")");
    private ILocator _recallBtn => _iframe.Locator("button:has-text('Recall')");
    private ILocator _recallConfirm => _iframe.Locator(".k-dialog-buttongroup button:has-text('Recall')");
    private ILocator _actionRequired => Page.Locator("#invoiceManagerNavItems a[href='/Buyer/Invoices/ActionRequiredInvoices']");
    private ILocator _docFilter => _iframe.Locator(".k-widget.k-grid.actionRequiredGrid th:nth-child(3) span div span");
    private ILocator _selectDoc => _iframe.Locator(".k-widget.k-grid.actionRequiredGrid tbody tr:nth-child(1) td:nth-child(3) button");
    private ILocator _approveBtn => _iframe.Locator("button:has-text('Approve')");
    private ILocator _approveBtnConfirm => _iframe.Locator(".flex.justify-end.gap-2 button:nth-child(1)");
    private ILocator _cancelInvoiceBtn => _iframe.Locator("button:has-text('Invoice')");
    private ILocator _cancelPOBtn => _iframe.Locator("button:has-text('Cancel PO')");
    private ILocator _changePO => _iframe.Locator("button:has-text('Change PO Assignment')");

    private ILocator _chnagePONext => _iframe.Locator(".k-window-content nav > div > div > button"); 
    private ILocator _applyPO => _iframe.Locator("button:has-text('Apply to this PO')");
    private ILocator _addLineItemBtn => _iframe.Locator("button:has-text('Add Line Item')");
    private ILocator _setZeroQtyBtn => _iframe.Locator("button:has-text('Set Zero Quantity')");
    private ILocator _bulkAssignBtn => _iframe.Locator("button:has-text('Bulk Assign GL Codes')");
    private ILocator _splitLineItemBtn => _iframe.Locator("button:has-text('Split Line Item')");
    private ILocator _splitByDropdown => _iframe.Locator("#view-drop-down-list > span > span:has-text('Percentage')");
    private ILocator _splitByQuantity => _iframe.Locator(".k-list ul li:nth-child(2)");
    private ILocator _splitLineItemGLCode2 => _iframe.Locator("table > tbody > tr.k-master-row.k-alt > td:nth-child(2) > span > span > span");
    private ILocator _splitLineItemGLCode1 => _iframe.Locator("table > tbody > tr:nth-child(1) > td:nth-child(2) > span > span > span");
    private ILocator _bulkAssignBtnDropdwn => _iframe.Locator(".k-widget.k-window.k-dialog div p span .k-input-value-text");
    private ILocator _splitLineItem1 => _iframe.Locator(".k-widget.k-window.k-dialog tr:nth-child(1) td:nth-child(3) input");
    private ILocator _splitLineItem2 => _iframe.Locator(".k-widget.k-window.k-dialog tr:nth-child(2) td:nth-child(3) input");
    private ILocator _totalAmount => _iframe.Locator(".k-widget.k-window.k-dialog tfoot td:nth-child(3) span");
    private ILocator _confirmSplit => _iframe.Locator(".flex.justify-end.items-center button:nth-child(2)");
    private ILocator _selectbulkGLCode => _iframe.Locator(".k-list ul li:nth-child(3)");
    private ILocator _assignSelectedGLCode => _iframe.Locator(".flex.flex-row.justify-end.gap-2 button:nth-child(1)");
    private ILocator _selectedGLCode => _iframe.Locator(".k-window-content.k-dialog-content span span span");
    private ILocator _combineLineItemsBtn => _iframe.Locator("button:has-text('Combine Line Items')");
    private ILocator _combineLineItemsQty => _iframe.Locator(".k-window-content table > tbody > tr > td:nth-child(1)   input");
    private ILocator _combineLineItemsName => _iframe.Locator(".k-window-content table > tbody > tr > td:nth-child(2)  input");
    private ILocator _combineLineItemDesc => _iframe.Locator(".k-window-content div:nth-child(3)  div.k-grid-container table > tbody > tr > td:nth-child(3)  input");
    private ILocator _combineLineItemGL => _iframe.Locator(".k-window-content table > tbody > tr > td:nth-child(4) > span span span");
    private ILocator _combineLineItemSelectedGLCode => _iframe.Locator(".k-list ul li:nth-child(3)");
    private ILocator _combineLineItemUOM => _iframe.Locator(".k-window-content table > tbody > tr > td:nth-child(5) input");
    private ILocator _combineLineItemPrice => _iframe.Locator(".k-window-content div:nth-child(3) div.k-grid-container table > tbody > tr > td:nth-child(6)  input");
    private ILocator _combineLineItemApplyBtn => _iframe.Locator(".flex.justify-end.gap-2 button:nth-child(1)");
    private ILocator _combinedlineItem1Qty => _iframe.Locator("table > tbody > tr:nth-child(3) > td:nth-child(6)  input");
    private ILocator _combinedlineItem2Qty => _iframe.Locator("table > tbody > tr:nth-child(2) > td:nth-child(6)  input");
    private ILocator _deleteOK => _iframe.Locator(".flex.justify-end.gap-2 button:nth-child(1)");
    private ILocator _rowsInInvoice => _iframe.Locator(".k-widget main div:nth-child(5) table tbody tr");
    private ILocator _combineQty1 =>_iframe.Locator(".k-dialog-wrapper div:nth-child(5)  div.k-grid-container  table > tbody > tr:nth-child(1) > td:nth-child(1)");
    private ILocator _combineQty2 => _iframe.Locator(".k-dialog-wrapper div:nth-child(5) div.k-grid-container  table > tbody > tr:nth-child(1) > td:nth-child(1)");
    private ILocator _newlineLitemName1(int i) => _iframe.Locator("table > tbody > tr:nth-child(3) > td:nth-child("+i+") > span > span > input");
   
    private ILocator _currentDocQtyItem2(int i) => _iframe.Locator("#invoiceLineItemGrid tbody > tr:nth-child(2) td:nth-child("+i+") input");
    private ILocator _currentDocQtyItem4(int i) => _iframe.Locator("#invoiceLineItemGrid tbody > tr:nth-child(4) td:nth-child("+i+") input");
    private ILocator _newlineLitemDesc1(int i) => _iframe.Locator("tbody > tr:nth-child(3) > td:nth-child("+i+") > span > span input");
   
    private ILocator _newlineLitemGLCode1(int i) => _iframe.Locator("tbody > tr:nth-child(3) > td:nth-child("+i+") > span > span");
    private ILocator _newlineLitemGLCodeSelect => _iframe.Locator(".k-list ul li:nth-child(3)");
    private ILocator _newlineLitemGLCode2(int i) => _iframe.Locator("tbody > tr:nth-child(4) > td:nth-child("+i+") > span > span");
    private ILocator _newlineLitem1UOM(int i) => _iframe.Locator("table > tbody > tr:nth-child(3) > td:nth-child("+i+") > span > span > input");
    
    private ILocator _selectLineItem1 => _iframe.Locator("div:nth-child(1) > table > tbody > tr:nth-child(1) > td:nth-child(2) input");
    private ILocator _selectLineItem2 => _iframe.Locator("div:nth-child(1) > table > tbody > tr:nth-child(2) > td:nth-child(2) input");
    private ILocator _selectLineItem3 => _iframe.Locator("div:nth-child(1) > table > tbody > tr:nth-child(3) > td:nth-child(2) input");
    private ILocator _selectLineItem4 => _iframe.Locator("div:nth-child(1) > table > tbody > tr:nth-child(4) > td:nth-child(2) input");
    private ILocator _tbodyInInvoice => _iframe.Locator(".k-widget main div:nth-child(5) table tbody");
    private ILocator _invoiceNotes1(int i) => _iframe.Locator("#invoiceGLSummaryGrid td:nth-child("+i+") input");
    
    private ILocator _rowsInSubmitForApproval => _iframe.Locator(".k-window-content.k-dialog-content .k-grid-container tr");
    private ILocator _GLSubTotal => _iframe.Locator("div:nth-child(4) div:nth-child(1) tr:nth-child(2) > td:nth-child(4) > div:nth-child(1)");
    private ILocator _GLTaxableAmt => _iframe.Locator("div:nth-child(4) div:nth-child(1) tr:nth-child(2) > td:nth-child(5) > div:nth-child(1)");
    
    private ILocator _confirmPopupBtn => _iframe.Locator(".flex.justify-end.gap-2 button:nth-child(1)");
    private ILocator _discount => _iframe.Locator("div.k-pane.k-pane-static div:nth-child(3) div:nth-child(13) > div > span input");
    private ILocator _shipping => _iframe.Locator("div.k-pane.k-pane-static div:nth-child(3) div:nth-child(14) > div > span input");
    private ILocator _taxes => _iframe.Locator("div.k-pane.k-pane-static div:nth-child(3) div:nth-child(15) > div > span input");
    private ILocator _grandTotal => _iframe.Locator("div > main > div:nth-child(3) div:nth-child(16) > div > span input");
    private ILocator _relatedInvoiceNumber => _iframe.Locator("div:nth-child(1) > table > tbody > tr:nth-child(1) > td:nth-child(2) > button");
    private ILocator _documentType(int i) => _iframe.Locator(".p-3 .k-grid-table  tbody tr:nth-child("+i+") td:nth-child(3)");
    private ILocator _documentStatus(int i) =>_iframe.Locator(".p-3 .k-grid-table  tbody tr:nth-child("+i+") td:nth-child(7)");
    private ILocator _documentInput(int i) =>_iframe.Locator(".p-3 .k-grid-table  tbody tr:nth-child("+i+") td:nth-child(1)>input");
    private ILocator _rowsInRelated => _iframe.Locator(".p-3 .k-grid-table  tbody tr td:nth-child(3)");

    private ILocator _allRowsInRelated => _iframe.Locator(".p-3 .k-grid-table  tbody");
    private ILocator _relateddocNumber(int i)  =>_iframe.Locator(".p-3 .k-grid-table  tbody tr:nth-child("+i+") td:nth-child(2)");
    private ILocator _relateddocTest(int i)  =>_iframe.Locator(".p-3 .k-grid-table  tbody tr:nth-child("+i+") td:nth-child(4)");
    private ILocator _relateddocButton(int i)  =>_iframe.Locator("table > tbody > tr:nth-child("+i+") > td:nth-child(4) > div > div > button");
    private ILocator _poFilter => _iframe.Locator("#viewInvoiceTable th:nth-child(4) > span.k-cell-inner > div > span");
    private ILocator _createNewInvoice => _iframe.Locator("button:has-text('Create New')");
    private ILocator _clearFiltersBtn => _iframe.Locator(".flex.items-center.justify-between.w-full button:nth-child(1)");  
    private ILocator _showFilters => _iframe.Locator(".flex.items-center.justify-between.w-full span:nth-child(2) span span");
    private ILocator _showDeleted => _iframe.Locator(".k-list ul li:nth-child(8)");
    private ILocator _invoiceNumberFilterBtn => _iframe.Locator("div th:nth-child(5) span.k-cell-inner > div > span");
    private ILocator invoiceStatus => _iframe.Locator(".k-grid-table tr:nth-child(1) td:nth-child(6)");
    
    private ILocator showAllInvoices => _iframe.Locator(".k-list ul li:nth-child(2)");
    private ILocator okConfirmBtn => _iframe.Locator(".flex.justify-end.gap-2 button:nth-child(1)");
    private ILocator bulkCompleteDocBtn => _iframe.Locator("button:has-text('Complete Document')");
    private ILocator bulkDeleteBtn => _iframe.Locator("div.flex.justify-between.flex-wrap > div.flex.flex-wrap > div:nth-child(3) > button");
    private ILocator bulkUndeleteBtn => _iframe.Locator("button:has-text('Undelete')");
    private ILocator bulkClosePOBtn => _iframe.Locator("button:has-text('Close PO')");
    private ILocator _printInvoice => _iframe.Locator("button:has-text('Print')");
    private ILocator _showHistory => _iframe.Locator(".p-4 label:nth-child(1) input");
    private ILocator _showNotes => _iframe.Locator(".p-4 label:nth-child(2) input");
    private ILocator _includeAttachments => _iframe.Locator(".p-4 label:nth-child(3) input");
    
    private ILocator _savePDF => _iframe.Locator(".flex.flex-row.gap-2.justify-end a button");

    private ILocator assignMulti =>_iframe.Locator("button:has-text('Assign Multiple Periods')");

    private ILocator editPeriods =>_iframe.Locator("button:has-text('Edit Period')");

    private ILocator covertPeriods =>_iframe.Locator("button:has-text('Convert Periods')");

    private ILocator covertPeriod =>_iframe.Locator("button:has-text('Convert Period')");

    private ILocator covertToSingleBtn =>_iframe.Locator("button:has-text('Convert to Single Period')");

    private ILocator covertToMultiBtn =>_iframe.Locator("button:has-text('Convert to Multi Period Allocations')");

    private ILocator periodDropdown =>_iframe.Locator("#period-dropdown button");

    private ILocator accuntPer => _iframe.Locator(".k-widget >.k-pane >div>main> div:nth-child(3) div:nth-child(9)>span");

    private ILocator _expansionItem => _iframe.Locator("#invoiceGLSummaryGrid .k-grid-header-wrap thead>tr>th:first-child th >div>div");

    private ILocator glRows => _iframe.Locator("#invoiceGLSummaryGrid .k-grid-container tbody tr.k-master-row:not(:last-child)");

    private ILocator invoiceLineItems => _iframe.Locator("#invoiceLineItemGrid tbody tr");

    

    private ILocator invoiceLineItemsGl(string glCode) =>  invoiceLineItems.Locator("td:nth-child(10):has-text('"+glCode+"')").Nth(0).Locator("..");
    private ILocator glCodeTxt => _iframe.Locator("main>div:nth-child(3) .grid > div:nth-child(9)");

    private ILocator changePOSearch => _iframe.Locator(".k-window-content input[name='search-input']").Nth(0);


    private ILocator assignSingle =>_iframe.Locator("button:has-text('Assign Single Period')");

    private ILocator assignPOPeriods =>_iframe.Locator("button:has-text('Copy Multiple PO Periods')");

    private ILocator assignInvoicePeriods =>_iframe.Locator("button:has-text('Maintain Multiple Invoice Periods')");


    
    
    private ILocator cancelbtn => _iframe.Locator(".slide-pane__content button:has-text('Cancel')");
    
    private ILocator invoiceLink => _iframe.Locator(".k-grid-table tr:nth-child(1) td:nth-child(5) button");

    private ILocator invoiceDetailsInput => _iframe.Locator("main.flex >div:nth-child(3)>div:last-child input");

    private ILocator invoiceScan => _iframe.Locator(".scans-section-with-actions");
    private static readonly string DOCUMENTNUMBER = "1";
    int _rowCount = 0;
    
    //Expand Invoice Manager
    public async Task ExpandInvoicePage(){
        if(!await IsVisibleAsync(_invoiceManagerLink,1000,2)){
            Assert.Fail("Invoice Module is not visible for the user");
        }
        await _invoiceManagerLink.EvaluateAsync("node=>node.click()");
    }

    //Naviagte to Enter Invoices 
    public async Task EnterInvoicePage(){
         await _enterInvoiceLink.EvaluateAsync("node=>node.click()");
    }
    public async Task EnterDocumentNumberRand(string rand){
        if(await IsVisibleAsync(_closePopupBtn,1000,5)){
         await _closePopupBtn.ClickAsync();
        }
        await _documentNumber.FillAsync(rand);
        if(await IsVisibleAsync(_closePopupBtn,1000,5)){
         await _closePopupBtn.ClickAsync();
        }
    }
    //Enter Document number present on Enter Invoices form
    public async Task EnterDocumentNumber(){
        if(await IsVisibleAsync(_closePopupBtn,1000,5)){
         await _closePopupBtn.ClickAsync();
        }
        await _documentNumber.FillAsync(DOCUMENTNUMBER);
        if(await IsVisibleAsync(_closePopupBtn,1000,5)){
         await _closePopupBtn.ClickAsync();
        }
    }

 
    //Click Next button after Document number is entered
    public async Task ClickFirstNextButton(){
         await _nextBtn.ClickAsync();
         await _tableData.WaitForAsync();
    }

   //Handling Document number and click Next button in single function
    public async Task EnterDocumentNumberWithNext() 
    {
      await EnterDocumentNumber();
      await ClickFirstNextButton();
    }


    public async Task<bool> CheckForLinked(){
        var t = await _allRowsInRelated.InnerTextAsync()??"";
        return t.Contains("You must link a completed receipt");
    }
    // Open View Invoices form
    public async Task EnterViewInvoices()
    { 
     await _invoiceManagerLink.ClickAsync();
     await _viewInvoicesLink.ClickAsync();
    }

    private async Task reenterPo(string poNumber, int count){
        await _enterPO.ClearAsync();
        await _enterPO.FillAsync(poNumber);
        await _nextBtn.ClickAsync();
        if(await _tableData.CountAsync()==0 && count<10){
            Thread.Sleep(100);
            await _backBtn.ClickAsync();
            await reenterPo(poNumber,count++);
        }
    }
    //Verify PO grid present on Enter Invoices form
    public async Task VerifyPOInGrid(CommonContext commonContext)
    {
        await _closePopupBtn.ClickAsync();
        await _nextBtn.ClickAsync();
        await _closePopupBtn.ClickAsync();
        if(!string.IsNullOrEmpty(commonContext.PoNumber)){
            await _enterPO.FillAsync(commonContext.PoNumber);
        }
        await _nextBtn.ClickAsync();
        if(!string.IsNullOrEmpty(commonContext.PoNumber)){
            await _tableData.WaitForAsync();
        }
        await _closePopupBtn.ClickAsync();
    }

    //Handling Guide Me Popup's
    private async Task NavigatePages(CommonContext commonContext){
        await _nextBtn.ClickAsync();
        await _closePopupBtn.WaitForAsync();
        await _closePopupBtn.ClickAsync();
        await _nextBtn.WaitForAsync();
        await _nextBtn.ClickAsync();
        await continueWithoutattachemnt.ClickAsync();
        if(await IsVisibleAsync(assignMulti,1000,5)){
            await assignMulti.ClickAsync();
            commonContext.HasMulti = true;
        }else{
            commonContext.HasMulti = false;
        }

    }

    //Create Invoice 
    public async Task CreateFirstInvoice(List<string> quantities,CommonContext commonContext, bool changeGl = false)
    {
        await NavigatePages(commonContext);
        await CreateInvoiceCredit(quantities,0,commonContext,changeGl);
        await _firstDivOpen.ClickAsync();
    }

    //Create Invoices or Credit memos methos to use in Createanotherinvoice method  
    private async Task CreateInvoiceCredit(List<string> quantities, int idx,CommonContext commonContext, bool changeGl)
    {   
        
        //Console.WriteLine("Inv"+commonContext.HasMulti);
        bool hasMulti = commonContext.HasMulti;
        await _invPopupBtn.ClickAsync();
        await WaitUntilLoaderHidden();
        await _invoiceInput.FillAsync(commonContext.PoNumber+"-"+idx);
        string invoice_number = await _invoiceInput.InputValueAsync();
        commonContext.InvoiceNumber = invoice_number;
        await _receivedDate.ClickAsync();
        await _receivedDateSelect.ClickAsync();
        var index =0;
        foreach (var item in await invoiceLineItems.AllAsync())
        {
            if(await IsVisibleAsync(item.Locator("td:nth-child(5) #invoiceSwitch .k-switch-off"),1000,5)){
                await item.Locator("td:nth-child(5) #invoiceSwitch .k-switch-off").ClickAsync();
            }
        }

        commonContext.TaxEnabled = await IsVisibleAsync(_taxes,1000,5);
        if(!commonContext.TaxEnabled){
            await _taxes.FillAsync("0");
        }
        if(idx == 0){
            if(hasMulti){
                await _expansionItem.WaitForAsync();
                await _expansionItem.ClickAsync();
                if(await IsVisibleAsync(_expansionItem.Locator("svg[data-icon='circle-minus']"),1000,5)){
                    await _expansionItem.ClickAsync();
                }
                
            }
            foreach(var elem in await glRows.AllAsync())
            {
                var glCs = hasMulti?3:2;
                var glCode = await elem.Locator("td:nth-child("+glCs+")").TextContentAsync()??"";
                glCode=glCode.Split(" - ")[0].Trim();
                var amount = 0.0;
                var shipping = 0.0;
                var tax = 0.0;
                var discount = 0.0;
                List<string> filteredList = commonContext.PoItemDetails.Keys.Where(x => x.Contains(glCode)).ToList();
                
                foreach(var key in filteredList){
                    ItemDetails it = commonContext.PoItemDetails[key];
                    if(!hasMulti){
                       amount+=it.Amount;
                       shipping+=it.Shipping;
                       discount+=it.Discount;
                       tax+=it.Tax;
                    }else{
                        var period  = key.Split("#")[1];
                        if(period.IndexOf("(")!=-1){
                            period = period.Split('(', ')')[1];
                        }
                        if(await IsVisibleAsync(elem.Locator("+ tr #gl-distribution-table-detail"),1000,5)){
                            var par = elem.Locator("+ tr #gl-distribution-table-detail .k-grid-container tr:has-text('" + period + "')");
                            var smt = GetAmount(await par.Locator("td:nth-child(4)").TextContentAsync()??"0.00");
                            Assert.That(smt, Is.EqualTo(it.Amount));
                            var smt1 = GetAmount(await par.Locator("td:nth-child(8)").TextContentAsync()??"0.00");
                            //Assert.That(smt1, Is.EqualTo(it.Tax));
                            var smt2 = GetAmount(await par.Locator("td:nth-child(6)").TextContentAsync()??"0.00");
                            Assert.That(smt2, Is.EqualTo(it.Discount));
                            var smt3 = GetAmount(await par.Locator("td:nth-child(7)").TextContentAsync()??"0.00");
                            Assert.That(smt3, Is.EqualTo(it.Shipping));
                        }
                    }
                }
                if(!hasMulti){
                    var sm = GetAmount(await elem.Locator("> td:nth-child(4)").TextContentAsync()??"0.00");
                    Assert.That(sm, Is.EqualTo(amount));
                    var sm1 = GetAmount(await elem.Locator("> td:nth-child(6)").TextContentAsync()??"0.00");
                    Assert.That(sm1, Is.EqualTo(discount));
                    var sm2 = GetAmount(await elem.Locator("> td:nth-child(7)").TextContentAsync()??"0.00");
                    Assert.That(sm2, Is.EqualTo(shipping));
                    var sm3 = GetAmount(await elem.Locator("> td:nth-child(8)").TextContentAsync()??"0.00");
                    Assert.That(sm3, Is.EqualTo(tax));
                }    

            }
            if(hasMulti){
                await _expansionItem.ClickAsync();
            }
        }
        bool isThreeWay = await CheckForLinked();
        List<string> exempt = DateValidation.GetFilteredCodes(commonContext.GLCodesData,"Exempt",false);
        List<string> off = DateValidation.GetFilteredCodes(commonContext.GLCodesData,"Off",false);
        List<string> gcodes = exempt.Count>0?exempt:off.Count>0?off:DateValidation.GetFilteredCodes(commonContext.GLCodesData,"Required",false);
        foreach (var item in await invoiceLineItems.AllAsync())
        {
            var x0= isThreeWay?11:10;
            var GlCode = await item.Locator("td:nth-child("+x0+")").InnerTextAsync()??"";
            GlCode = GlCode.Split(" - ")[0];
            if(gcodes.Count>0 && !gcodes.Contains(GlCode)){
                await item.Locator("td:nth-child("+x0+")").ClickAsync();
                await _iframe.Locator(".k-list ul li:has-text('"+gcodes[0]+"')").Nth(0).ClickAsync();
            }
        }
        foreach (var input in quantities){
            if(await _itemQuantity.Nth(index).IsEditableAsync())
            {
                if(changeGl && index==0){
                    
                    var x0= isThreeWay?11:10;
                    await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child(1) td:nth-child("+x0+")").ClickAsync();
                    if(gcodes.Count>1){
                        await _iframe.Locator(".k-list ul li:has-text('"+gcodes[1]+"')").Nth(0).ClickAsync();
                    }else{
                        await _iframe.Locator(".k-list ul li:nth-child(1)").ClickAsync();
                    }
                }
                float itemQuantity = float.Parse(input);
                float calculatedValue = itemQuantity-commonContext.quantities[index];
                double varianceQtyPercentage = (double)(itemQuantity-commonContext.quantities[index])/commonContext.quantities[index]*100;
                await _itemQuantity.Nth(index).FillAsync(input);
                await Task.Delay(2000);
                index++;
            }
        }
        foreach (var input in await _itemDesc(commonContext.HasMulti?4:3).AllAsync())
        {
            await input.FillAsync(commonContext.PoNumber);
        }
        await _invDate.ClickAsync();
        await _receivedDateSelect.ClickAsync();
        var appId=await invoiceDetailsInput.Nth(3).InputValueAsync()??"";
        if(string.Empty == appId){
            await invoiceDetailsInput.Nth(3).FillAsync("123456");
        }
        var ext=await invoiceDetailsInput.Nth(4).InputValueAsync()??"";
        if(string.Empty == ext){
            await invoiceDetailsInput.Nth(4).FillAsync("123456");
        }
        
        // Update the property with the calculated value
        await _itemTotalInput.FillAsync(GetAmount(await _itemTotal(hasMulti?10:9).TextContentAsync()??string.Empty)+"");
        Thread.Sleep(2000);
        if(await _saveBtn.IsEnabledAsync()){
            await _saveBtn.ClickAsync();
        }
        if(!await invoiceScan.IsVisibleAsync()){
            await _showScans.ClickAsync();
        }
        await _uploadBtn.ClickAsync();
        await _uploadDoc.SetInputFilesAsync(UPLOADPDFFILE);
        await _uploadClose.ClickAsync();
        await _hideScans.ClickAsync();
        if(await _saveBtn.IsEnabledAsync()){   
            await _saveBtn.ClickAsync();
        }
    }

    private async void AddToComplted(CommonContext commonContext, string glCode, float amount, float ship,float disc, float tax, bool isApproval,int count){
        ItemDetails? value = new ItemDetails();
        bool hasValue = false;
        float quantity = 0;
        foreach(var row in await invoiceLineItemsGl(glCode.Split("#")[0]).Nth(0).AllAsync()){
            quantity+=GetAmount(await row.Locator("td:nth-child(6) input").InputValueAsync());
        }
        if(!isApproval){
            hasValue = commonContext.CompletedInvoiceItems.TryGetValue(glCode, out value);
        }

        quantity = quantity/count;
        if(hasValue && value!=null){
            value.Quantity+=quantity;
            value.Amount+=amount;
            value.Shipping+=ship;
            value.Discount+=disc;
            value.Tax+=tax;
            commonContext.CompletedInvoiceItems[glCode] = value;
        }else{
            if(isApproval){
                commonContext.SendForApproval.Add(glCode,new ItemDetails(amount,disc,ship, tax,quantity));
            }else{
                commonContext.CompletedInvoiceItems.Add(glCode,new ItemDetails(amount,disc,ship, tax,quantity));
            }
        }
        await Task.Run(()=>{});
        //Console.WriteLine("inv total amt"+commonContext.CompletedInvoiceItems[glCode].getTotalAmount());
    }

    public async Task InvoiceGLTotal(CommonContext commonContext)
    {
        await Task.Delay(3000);
        // Get all elements matching the selector
        var elements = await Page.QuerySelectorAllAsync("#gl-distribution-table-detail tbody td:nth-child(9)");
        foreach (var element in elements)
        {
            // Retrieve the text content of each element
            var textContent = await element.TextContentAsync() ?? "";
            var cleanedTextContent = Regex.Replace(textContent, @"[^\d.-]", "");
            if(float.TryParse(cleanedTextContent, NumberStyles.Any, CultureInfo.InvariantCulture, out float amount))
            {
                commonContext.InvoicePeriodsGLTotals.Add(amount);
                if (commonContext.PeriodsItemPrices.Count >= 2)
                {
                    //Console.WriteLine("First Period GL Total " + commonContext.InvoicePeriodsGLTotals[0]);
                    //Console.WriteLine("Second Period GL Total " + commonContext.InvoicePeriodsGLTotals[1]);
                }
                else
                {
                    //Console.WriteLine("Period added: " + amount);
                }
            }
        }
    }
    private async Task FillCommonContext(CommonContext commonContext,bool isApproval = false){
        int ids=1;
        bool hasMulti = commonContext.HasMulti;
        if(hasMulti){
            await _expansionItem.ClickAsync();
        }
        commonContext.SendForApproval.Clear();
        float quantity = 0;
        Thread.Sleep(1000);
        foreach(var row in await invoiceLineItems.AllAsync()){
            quantity+=GetAmount(await row.Locator("td:nth-child(6) input").InputValueAsync());
        }
        commonContext.QuantityInvoiceTotal+=quantity;
        commonContext.InvoiceTotal+=GetAmount(await _itemTotalInput.InputValueAsync()??"0");
        var j = hasMulti?3:2;
        foreach(var row in await _invoiceItems.AllAsync()){
            string glCode =await row.Locator(">td:nth-child("+j+")").TextContentAsync()??string.Empty;
            glCode=glCode.Split(" - ")[0].Trim();
            
            if(hasMulti){
                var par = row.Locator("+ tr #gl-distribution-table-detail .k-grid-container tr");
                var count =await par.CountAsync();
                foreach(var elem in await par.AllAsync()){
                    var period = await elem.Locator("td:nth-child(2)").TextContentAsync()??"";
                    period = period.Split('(', ')')[1];

                    var smt = GetAmount(await elem.Locator("td:nth-child(4)").TextContentAsync()??"0.00");
                    var tax = GetAmount(await elem.Locator("td:nth-child(8)").TextContentAsync()??"0.00");
                    var disc = GetAmount(await elem.Locator("td:nth-child(6)").TextContentAsync()??"0.00");
                    var ship = GetAmount(await elem.Locator("td:nth-child(7)").TextContentAsync()??"0.00");
                    AddToComplted(commonContext,glCode+"#"+period,smt,ship,disc,tax,isApproval,count);
                }
            }else{
                float amount = GetAmount(await _itemsAmt(ids,hasMulti?5:4).TextContentAsync()??"0");
                float ship =GetAmount(await _itemsShipping(ids,hasMulti?8:7).TextContentAsync()??"0");
                float disc =GetAmount(await _itemsDiscount(ids,hasMulti?7:6).TextContentAsync()??"0");
                float tax =GetAmount(await _itemsTax(ids,hasMulti?9:8).TextContentAsync()??"0");
                var pers = await glCodeTxt.GetAttributeAsync("title");
                AddToComplted(commonContext,glCode+"#"+pers,amount,ship,disc,tax,isApproval,1);
            }
            ids++;
        }
        if(hasMulti){
            await _expansionItem.ClickAsync();
        }
    }
    //Complete Invoice 
    public async Task CompleteInvoice(CommonContext commonContext){
        if(await _saveBtn.IsEnabledAsync()){
            await _saveBtn.ClickAsync();
        }
        Thread.Sleep(100);
        await FillCommonContext(commonContext);
        await IsVisibleAsync(_completeBtn,1000,5);
        if(!await IsVisibleAsync(_completeBtn,1000,5)){
            Assert.Fail("Complete button not visible please enable perm 743");
        }
        await _completeBtn.ClickAsync();  
        if(await IsVisibleAsync(_confirmPopupBtn,1000,5)){
            await _confirmPopupBtn.ClickAsync();
        }
        commonContext.TotalInvoiceAmounts+=GetAmount(await _itemtotal(commonContext.HasMulti?9:8).TextContentAsync()??string.Empty);
    }

    //Create Second Invoice or Credit memo
    public async Task CreateAnotherInvoice(List<string> quantities, int idx,CommonContext commonContext, bool complete = true){
        await _createNewDocument.EvaluateAsync("node=>node.click()");
        await _invoiceCreditBtn.ClickAsync();
        var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await continueWithoutattachemnt.ClickAsync();
            if(await IsVisibleAsync(assignMulti,1000,5)){
                commonContext.HasMulti = true;
                await assignMulti.ClickAsync();
            }else{
                commonContext.HasMulti = false;
            }
        });
        BringSecondPageToFront(newPage,commonContext);
        await CreateInvoiceCredit(quantities,idx,commonContext,false);
        if(complete){
            await ThreeWayTask(commonContext);
            await CompleteInvoice(commonContext);
            await BringBasePageToFront(commonContext);
            await _firstDivOpen.ClickAsync();
        }
    }

    public async Task CompleteNewInvoice(CommonContext commonContext){
        await CompleteInvoice(commonContext);
        await BringBasePageToFront(commonContext);
        await _firstDivOpen.ClickAsync();
    }
    public async Task UpdatePeriodInvoice(CommonContext commonContext){
        if(await IsVisibleAsync(periodDropdown,1000,5)){
            await periodDropdown.ClickAsync();
            var x = await _mpaAccountingPeriodOpened.Nth(0).TextContentAsync()??"";
            await _mpaAccountingPeriodOpened.Nth(0).ClickAsync();
            await _saveBtn.ClickAsync();
        }else{
           Assert.Fail("Enable 762 perm for this user"); 
        }
    }
    public async Task ConvertToMultiPeriod(CommonContext commonContext){
        if(await IsVisibleAsync(covertPeriod,5000)){
            await covertPeriod.ClickAsync();
            await covertToMultiBtn.ClickAsync();
            await ValidatePeriods(commonContext);
            commonContext.HasMulti = true;
            await _saveModel.ClickAsync();
        }else{
           Assert.Fail("disable 763 perm for this user"); 
        }
    }

    public async Task ConvertToSinglePeriodNUpdate(CommonContext commonContext,bool update = true){
        if(update){
            if(await IsVisibleAsync(editPeriods,5000)){
                await editPeriods.ClickAsync();
            }else if(await IsVisibleAsync(covertPeriod,5000)){
                await covertPeriod.ClickAsync();
            }else{
                Assert.Fail("disable 763 perm for this user enable 762 perm for this user"); 
            }
            if(await IsVisibleAsync(covertToSingleBtn,5000)){
                await covertToSingleBtn.ClickAsync();
            }
            
            if(await IsVisibleAsync(_saveModel,5000 ) && await _saveModel.IsEnabledAsync(new() { Timeout = 5000 })){
                commonContext.HasMulti = false;
                await _saveModel.ClickAsync();
            }else{
                await _CancelAppBtn.ClickAsync();
            }
            if(await IsVisibleAsync(periodDropdown,5000 )){
                await periodDropdown.ClickAsync();
                var x = await _mpaAccountingPeriodOpened.Nth(0).TextContentAsync()??"";
                await _mpaAccountingPeriodOpened.Nth(0).ClickAsync();
                var match = commonContext.Periods.FirstOrDefault(stringToCheck => stringToCheck.Contains(x));
                if(match != null){
                    commonContext.Periods.Add(x);
                }
                if(await _saveBtn.IsEnabledAsync()){
                    await _saveBtn.ClickAsync();
                }
            }
        }else{
            if(await IsVisibleAsync(covertPeriods,5000 )){
                await covertPeriods.ClickAsync();
                await covertToSingleBtn.ClickAsync();
                commonContext.HasMulti = false;
                await _saveModel.ClickAsync();
            }else{
                Assert.Fail("disable 763 perm for this user"); 
            }
        }
        
    }

    public async Task UpdateInvoiceMPA(CommonContext commonContext){
        if(commonContext.Roles!=null && commonContext.Roles.Count>0 && (commonContext.Roles.FirstOrDefault(stringToCheck => stringToCheck.Contains("EnableMpaPos"))==null)){
            Assert.Fail("EnableMpaPos perm for the company needs to be turned on");
        }
        if(await IsVisibleAsync(editPeriods,5000)){
            await editPeriods.ClickAsync();
            await ValidatePeriods(commonContext);
            await updateMPABase("1",commonContext,true);
            commonContext.HasMulti = true;
        }else{
            Assert.Fail("enable 763 perm for this user"); 
        }
    }
    //Verify Invoice status 
    public async Task VerifyInvoiceStatus(string status){
       await VerifyStatus(status);
    }

    //Reload Invoice page  
    public async Task RefeshPage(){
        await Page.ReloadAsync(); 
        await _invPopupBtn.ClickAsync();
    }

    //Close PO
    public async Task ClosePO(){
        if(await IsVisibleAsync(_completeBtn,1000,5)){ 
            await _completeBtn.ClickAsync();  
        }
        if(await IsVisibleAsync(_confirmBtn,1000,5)){
            await _confirmBtn.ClickAsync();
        }
        await RefeshPage();
        if(!await IsVisibleAsync(_closePOBtn,1000,5)){
            Assert.Fail("enable 698 perm for this user"); 
        }
        await _closePOBtn.ClickAsync();
        await _confirmBtn.WaitForAsync();
        await _confirmBtn.ClickAsync();
        await _confirmPopupBtn.WaitForAsync();
        await _confirmPopupBtn.ClickAsync();

    }

    //Verify Invoices present on Enter Invoices flow
    public async Task VerifyEnterInvocieTableGridColumns(string column)
    {
        await this.VerifyTitle(column);
    }

    //Verify PO grid titles on Enter Invocies flow
    public async Task POGridColumnsTitles(string column)
    {
        await this.VerifyTitle(column);
    }

    //Validate Blind receiving
    private async Task CommonStepsForBlindReceive()
    {
        Random random = new Random();  
        string rand = random.Next(2, 748474964)+random.Next(2, 748474964)+""; 
        await EnterDocumentNumberRand(rand);
        await _nextBtn.ClickAsync();
        await _closePopupBtn.WaitForAsync();
        await _closePopupBtn.ClickAsync();
        try{
            await _tableData.WaitForAsync();
        }catch(Exception){}
        await _nextBtn.ClickAsync();
        await _closePopupBtn.WaitForAsync();
        await _closePopupBtn.ClickAsync();
        await _nextBtn.ClickAsync();
        if(await IsVisibleAsync(_closePopupBtn,1000,5)){
            await _closePopupBtn.ClickAsync();
        }
        await _tableData.WaitForAsync();
        if(await IsVisibleAsync(_nextBtn,1000,5)){
            await _nextBtn.ClickAsync();
        }
    }

    // Create a PO from Enter INvoices
    public async Task CreatePOFromEnterInvoice()
    {
        await CommonStepsForBlindReceive();
        if(!await IsVisibleAsync(_createPO,1000,5)){
            throw  new Exception("Enable Blind receving perm for this user");
        }
        await _createPO.ClickAsync();
        await Assertions.Expect(Page).ToHaveURLAsync(Page.Url);
    }

    // Create a New Invoice
    public async Task CreateNewInvoiceWithoutPO(string company)
    {
        await CommonStepsForBlindReceive();
        await _searchSupplierName.WaitForAsync();
        await _searchSupplierName.FillAsync(company);

        await _selectSupplier.WaitForAsync();
        await _selectSupplier.ClickAsync();
        if(!await IsVisibleAsync(_createInvoiceBtn,1000,5)){
            throw  new Exception("disable 723 perm for this user");
        }
        await _createInvoiceBtn.ClickAsync();
        await continueWithoutattachemnt.ClickAsync();
        await _invPopupBtn.ClickAsync();
        await Assertions.Expect(Page).ToHaveURLAsync(Page.Url);
    }

    // Create New Supplier
    public async Task CreateNewSupplier()
    {
        await CommonStepsForBlindReceive();
        await _createSupplier.ClickAsync();
        await Assertions.Expect(Page).ToHaveURLAsync(Page.Url);
    }

    //Delete Invoice 
    public async Task DeleteButton()
    {
        if(!await IsVisibleAsync(_deleteBtn,1000,5)){
            throw  new Exception("enable 745 perm for this user");
        }
        await _deleteBtn.ClickAsync();
        await _confirmPopupBtn.ClickAsync();
    }

    //Undelete Invoice 
    public async Task UndeleteButton()
    {
        await _unDeleteBtn.ClickAsync();
    }

    //Submit for approval on Invoice Details form
    public async Task SubmitForApproval(CommonContext commonContext)
    {
        await FillCommonContext(commonContext,true);
        await submitForApproval1();
    }

    private async Task submitForApproval1()
    {
        if(!await IsVisibleAsync(_submitForApprovalBtn,1000,5)){
            throw  new Exception("enable 724 perm for this user");
        }
        await _submitForApprovalBtn.ClickAsync();
        await Task.Delay(3000);
        var totalCount = await _rowsInSubmitForApproval.CountAsync();
        for (int i = 1; i <= totalCount; ++i)
        {
            var contactName = await _submitApprovalGrid(i).InnerTextAsync();
            if (contactName == "Vice President")
            {
                await _submitApprovalGridselected(i).ClickAsync();
                await _sendForSubmitApproval.ClickAsync();
                await _confirmPopupBtn.ClickAsync();
                break;
            }
        }
    }

    //Recall Invoice 
    public async Task Recall()
    {
        await _recallBtn.ClickAsync();
        await _recallConfirm.ClickAsync();
        await _closeBtn.ClickAsync();
    }

    //Method to submit invoice for approval to VP 
    public async Task ActionRequiredDocument(CommonContext commonContext)
    {
        await _actionRequired.ClickAsync();
        await _invPopupBtn.WaitForAsync();
        await _invPopupBtn.ClickAsync();
        await _docFilter.ClickAsync();
        await _filterInput.FillAsync(commonContext.PoNumber+"-0");
        await _filterBtn.ClickAsync();
        var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await _selectDoc.ClickAsync();
        });
        BringSecondPageToFront(newPage,commonContext);
        await _closeBtnX.ClickAsync();
        await _approveBtn.ClickAsync();
        await _approveBtnConfirm.ClickAsync();
        await VerifyInvoiceStatus("completed by Vice President");
        await BringBasePageToFront(commonContext);
    }

    //Cancel Invoice 
    public async Task CancelInvoice()
    {
        await _cancelInvoiceBtn.ClickAsync();
        await _confirmPopupBtn.ClickAsync();
    }

    //Cancel PO
    public async Task CancelPO()
    {
        if(!await IsVisibleAsync(_cancelPOBtn,1000,5)){
            throw  new Exception("enable 736 perm for this user");
        }
        await _cancelPOBtn.ClickAsync();
    }

    public async Task VerifyPrintInvoice()
    {
        await _printInvoice.ClickAsync();
        await _showHistory.ClickAsync();
        await _showNotes.ClickAsync();
        await _includeAttachments.ClickAsync();
        await _savePDF.ClickAsync();
        string downloadPath = System.AppDomain.CurrentDomain.BaseDirectory;
        bool isDownloaded = await WaitForFileDownload(downloadPath, UPLOADPDFFILE, TimeSpan.FromSeconds(30));
        if (!isDownloaded)
        {
            Assert.Fail("File has not been downloaded");
        }
    }

    private async Task<bool> WaitForFileDownload(string downloadPath, string fileName, TimeSpan timeout)
    {
        var start = DateTime.Now;
        while (DateTime.Now - start < timeout)
        {
            var files = Directory.EnumerateFiles(downloadPath, fileName);
            if (files.Any()) return true;
            await Task.Delay(500); // Check every 500 milliseconds
        }
        return false;
    }

    //Change PO Assignment on Invoice details form
    public async Task ChangePOAssignment(CommonContext commonContext,string periodType="single")
    {
        if(!await IsVisibleAsync(_changePO,1000,5)){
            throw  new Exception("enable 756 perm for this user");
        }
        var firstTexts = await GetPeriodsFromElements();
        await _changePO.EvaluateAsync("node=>node.click()");
        await changePOSearch.FillAsync(commonContext.PoNumbers[0]);
        await _chnagePONext.EvaluateAsync("node=>node.click()");
        var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await _applyPO.ClickAsync();
            if(await IsVisibleAsync(assignSingle,1000,5)){
                if(commonContext.HasMulti){
                    await Assertions.Expect(assignInvoicePeriods).ToBeVisibleAsync();
                }
                if(commonContext.PrevPeriods.Count>1){
                    await Assertions.Expect(assignPOPeriods).ToBeVisibleAsync();
                }
                if(periodType == "fromPo"){
                    await assignPOPeriods.ClickAsync();
                }else if(periodType=="fromInvoice"){
                    await assignInvoicePeriods.ClickAsync();
                }else{
                    await assignSingle.ClickAsync();
                }
            }
        });
        BringSecondPageToFront(newPage,commonContext);
        await _closeBtnX.ClickAsync();
        var secondTexts = await GetPeriodsFromElements();
        if (periodType == "fromInvoice")
        {
            // Existing exception logic
            Assert.True(firstTexts.SequenceEqual(secondTexts), "The texts from both cases are not equal.");
        }
        else if (periodType == "single" && secondTexts.Count >= 2)
        {
            var expectedText = firstTexts[1];
            Assert.That(expectedText, Is.EqualTo(secondTexts[0]));
        }
        await VerifyInvoiceStatus("copy of po");
        await BringBasePageToFront(commonContext);
    }

    public async Task<List<string>> GetPeriodsFromElements()
    {
        await editPeriods.ClickAsync();
        Task.Delay(3000).Wait();
        var texts = new List<string>();
        // Assuming you have a way to access the element, e.g., using a Locator or Query Selector
        var elements = Page.FrameLocator("#v4Container").Locator("#invoiceBudgetPeriodGrid div:nth-child(3) tbody td:nth-child(3) span:nth-child(1) span:nth-child(1) span");
        int elementCount = await elements.CountAsync();
        for (int i = 0; i < elementCount; i++)
        {
            var element = elements.Nth(i);
            // Check if each individual element is visible
            if (await IsVisibleAsync(element, 1000, 5))
            {
                // Retrieve and store the text of each visible element
                var text = await element.TextContentAsync()??"";
                texts.Add(text);
            }
        }
        await cancelbtn.ClickAsync();
        return texts;
    }

    //Verify Line Items titles on Invoice Details form
    public async Task VerifyLineItemsTableGrid(string column)
    {
        await this.VerifyTitleInvoiceId(column);
    }

    public async Task AddNewRow(List<PoDetais> poDetails, CommonContext commonContext){
        if(!await IsVisibleAsync(_addLineItemBtn,1000,5)){
            throw  new Exception("enable 705 perm for this user");
        }
        bool isThreeWay = await CheckForLinked();
        List<string> exempt = DateValidation.GetFilteredCodes(commonContext.GLCodesData,"Exempt",false);
        List<string> off = DateValidation.GetFilteredCodes(commonContext.GLCodesData,"Off",false);
        List<string> gcodes = exempt.Count>0?exempt:off.Count>0?off:DateValidation.GetFilteredCodes(commonContext.GLCodesData,"Required",false);
        var count =await invoiceLineItems.CountAsync()+1;
        var n0=isThreeWay?9:8;
        var n2=isThreeWay?11:10;
        var n3=isThreeWay?10:9;
        var n4=isThreeWay?12:11;
        var n5=isThreeWay?13:12;
        foreach(var item in poDetails){
            await _addLineItemBtn.ClickAsync();
            
            await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child("+count+") td:nth-child("+n0+") input").WaitForAsync();
            await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child("+count+") td:nth-child(6) input").FillAsync(item.Quantity);
            await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child("+count+") td:nth-child("+n0+") input").FillAsync("Item "+count);
            await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child("+count+") td:nth-child("+n3+") input").FillAsync("Item "+count);
            await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child("+count+") td:nth-child("+n2+")").ClickAsync();
            var s = count+4;
            if(gcodes.Count>1){
                await _iframe.Locator(".k-list ul li:has-text('"+gcodes[2]+"')").Nth(0).ClickAsync();
            }else{
                await _iframe.Locator(".k-list ul li:nth-child(1)").ClickAsync();
            }
            
            await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child("+count+") td:nth-child("+n4+") input").FillAsync("CA");
            await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child("+count+") td:nth-child("+n5+") input").FillAsync(item.Amount);
            count++;
        }
        foreach (var input in await _itemDesc(commonContext.HasMulti?4:3).AllAsync())
        {
            await input.FillAsync(commonContext.PoNumber);
        }
        Thread.Sleep(2000);
        string newamt = await _itemTotal(commonContext.HasMulti?10:9).TextContentAsync()??string.Empty+"";
        //Console.WriteLine(newamt);
        await _itemTotalInput.FillAsync(GetAmount(newamt).ToString());
        
        await _saveBtn.ClickAsync();
        
        if(await _saveBtn.IsEnabledAsync()){
            await _saveBtn.ClickAsync();
        }
    }

    //Add Line items on Invoice Details form
    public async Task AddLineItems()
    {
        if(!await IsVisibleAsync(_addLineItemBtn,1000,5)){
            throw  new Exception("enable 705 perm for this user");
        }
        
        bool isThreeWay = await CheckForLinked();
        var n0=isThreeWay?9:8;
        var n2=isThreeWay?11:10;
        var n3=isThreeWay?10:9;
        var n4=isThreeWay?12:11;
        var n5=isThreeWay?13:12;
        var firstLine = await IsVisibleAsync(_iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child(1) td:nth-child("+n0+")"),1000,5);
        int startLine = firstLine ? 3 : 1;
        int i = startLine;
        int addedItems = 0;
        
        while(addedItems < 2)
        {
            await _addLineItemBtn.ClickAsync();
            await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child("+i+") td:nth-child("+n0+") input").WaitForAsync();
            await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child("+i+") td:nth-child("+n0+") input").FillAsync("Item Name");
            await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child("+i+") td:nth-child("+n3+") input").FillAsync("Item Description");
            await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child("+i+") td:nth-child("+n2+")").ClickAsync();
            var s = i+2;
            await _iframe.Locator(".k-list ul li:nth-child("+s+")").ClickAsync();
            await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child("+i+") td:nth-child("+n4+") input").FillAsync("AD");
            await _iframe.Locator("#invoiceLineItemGrid tbody tr:nth-child("+i+") td:nth-child("+n5+") input").FillAsync("10.00");
            i++;
            addedItems++;
        }
        if(await _saveBtn.IsEnabledAsync()){
            await _saveBtn.ClickAsync();
        }
        await Task.Run(()=>{});
    }

    //Verifying delete button in the GL Summary after delete the line items
    public async Task VerifyDeleteButtonInGLSummary()
    {
        var rows = await _iframe.Locator("div > main > div:nth-child(4) div > div > div.k-grid-container tbody tr").ElementHandlesAsync();
        if(rows.Count > 1)
        {
            Assert.Fail("Expected no records but found " + rows.Count  + " records");
        }
    }

    //Delete Line Item on Invoice Details form
    public async Task VerifyDeleteBtn()
    {  
        //await actionTitle.ScrollIntoViewIfNeededAsync();
        var totalCount = await _rowsInInvoice.CountAsync();
        var nextCnt = 1;            
        for(int i=0;i<totalCount;i++)
        {
            if(await IsVisibleAsync(_tbodyInInvoice.Locator("tr:nth-child("+nextCnt+") td:last-child button"),1000,5)){
                await _tbodyInInvoice.Locator("tr:nth-child("+nextCnt+") td:last-child button").ClickAsync();
                await _deleteOK.ClickAsync();
            }
            else
            {
                nextCnt++;
            }
        }
        //await _saveBtn.ClickAsync();  
    }

    //SetZero Button validation
    public async Task SetZeroToQty()
    {
        await _selectLineItem1.EvaluateAsync("node=>node.click()");
        await Assertions.Expect(_setZeroQtyBtn).ToBeEnabledAsync();
        await _setZeroQtyBtn.ClickAsync();
        //await _selectLineItem1.ClickAsync();
    }

    //Bulk Assign Functionality
    public async Task BulkAssignGL()
    {
        await _selectLineItem3.EvaluateAsync("node=>node.click()");
        await _selectLineItem4.EvaluateAsync("node=>node.click()");
        bool isThreeWay = await CheckForLinked();
        var n0=isThreeWay?11:10;
        if(await _bulkAssignBtn.IsEnabledAsync())
        {
            await _bulkAssignBtn.ClickAsync();
            await _bulkAssignBtnDropdwn.WaitForAsync();
            await _bulkAssignBtnDropdwn.ClickAsync();
            await _selectbulkGLCode.WaitForAsync();
            await _selectbulkGLCode.EvaluateAsync("node=>node.click()");
            await _selectedGLCode.WaitForAsync();
            string glcode=await _selectedGLCode.InnerTextAsync();
            await _assignSelectedGLCode.ClickAsync();
            await _saveBtn.ClickAsync();
            await Assertions.Expect(_newlineLitemGLCode1(n0)).ToContainTextAsync(glcode);
            await Assertions.Expect(_newlineLitemGLCode2(n0)).ToContainTextAsync(glcode);
        }
        
    }

    // Combining Two Line items having 0 qty
    public async Task CombineLineItemsForQtyZero(CommonContext commonContext)
    {
        await _selectLineItem3.EvaluateAsync("node=>node.click()");
        await _selectLineItem4.EvaluateAsync("node=>node.click()");
        if(await _combineLineItemsBtn.IsEnabledAsync())
        {
            await _combineLineItemsBtn.ClickAsync();
            string combineQty= await _combineLineItemsQty.InnerTextAsync();
            // Add the integers and convert the result to a string
            await _combineLineItemsQty.ClearAsync();
            await _combineLineItemsQty.FillAsync("1");
            await _combineLineItemsName.ClearAsync();
            await _combineLineItemsName.FillAsync("combineline");
            await _combineLineItemDesc.ClearAsync();
            await _combineLineItemDesc.FillAsync("combinedescription");
            await _combineLineItemGL.ClickAsync();
            await _combineLineItemSelectedGLCode.WaitForAsync();
            await _combineLineItemSelectedGLCode.EvaluateAsync("node=>node.click()");
            await _combineLineItemUOM.ClearAsync();
            await _combineLineItemUOM.FillAsync("CC");
            //await _combinePrice.ScrollIntoViewIfNeededAsync();
            await _combineLineItemPrice.ClearAsync();
            await _combineLineItemPrice.FillAsync("2");
            string getqty=await _combineLineItemsQty.InnerTextAsync();
            string getItemName=await _combineLineItemsName.InnerTextAsync();
            string getItemDesc=await _combineLineItemDesc.InnerTextAsync();
            string getGLCode=await _combineLineItemGL.InnerTextAsync();
            string getUOM=await _combineLineItemUOM.InnerTextAsync();
            string getPrice=await _combineLineItemPrice.InnerTextAsync();
            await _combineLineItemApplyBtn.ClickAsync();
            //await _invoiceNotes2(3).FillAsync("CombineTest");
            //await _saveBtn.ClickAsync();
            //await Assertions.Expect(savedToastMsg).ToContainTextAsync("Receipt saved successfully!");
            await Assertions.Expect(_combinedlineItem1Qty).ToContainTextAsync(getqty);
            bool isThreeWay = await CheckForLinked();
            var n0=isThreeWay?12:11;
            var n1=isThreeWay?9:8;
            var n2=isThreeWay?10:9;
            //await Assertions.Expect(_newlineLitemName1(n1)).ToContainTextAsync(getItemName);
            //await Assertions.Expect(_newlineLitemDesc1(n2)).ToContainTextAsync(getItemDesc);
            //await Assertions.Expect(_newlineLitemGLCode1).ToContainTextAsync(getGLCode);
            //await Assertions.Expect(_newlineLitem1UOM(n0)).ToContainTextAsync(getUOM);
           // await Assertions.Expect(_newlinePrice).ToContainTextAsync(getPrice);
        }
    }

    // Combining two line items having qty 1 and 0
    public async Task CombineLineItemsQtyOneAndZero()
    {
        await _selectLineItem2.EvaluateAsync("node=>node.click()");
        //await selectLineItem3.ClickAsync();
        if(await _combineLineItemsBtn.IsEnabledAsync())
        {
            await _combineLineItemsBtn.ClickAsync();
            string combineQty= await _combineLineItemsQty.InnerTextAsync();
            // Add the integers and convert the result to a string
            await _combineLineItemsQty.ClearAsync();
            await _combineLineItemsQty.FillAsync("2");
            await _combineLineItemGL.ClickAsync();
            await _combineLineItemSelectedGLCode.WaitForAsync();
            await _combineLineItemSelectedGLCode.EvaluateAsync("node=>node.click()");
            //await _combinePrice.ScrollIntoViewIfNeededAsync();
            await _combineLineItemPrice.ClearAsync();
            await _combineLineItemPrice.FillAsync("2");
            string getqty=await _combineLineItemsQty.InnerTextAsync();
            string getGLCode=await _combineLineItemGL.InnerTextAsync();
            string getPrice=await _combineLineItemPrice.InnerTextAsync();
            await _combineLineItemApplyBtn.ClickAsync();
            //await _saveBtn.ClickAsync();
            //await Assertions.Expect(savedToastMsg).ToContainTextAsync("Receipt saved successfully!");
            //await Assertions.Expect(_combinedlineItem2Qty).ToContainTextAsync(getqty);
            //await Assertions.Expect(_newlineLitemGLCode1).ToContainTextAsync(getGLCode);
            //await Assertions.Expect(_newlinePrice).ToContainTextAsync(getPrice);
        }
    }

    //Combining line items qty having 1 and 1
    public async Task CombineLineItemsQtyOneAndOne()
    {
        await _selectLineItem1.EvaluateAsync("node=>node.click()");
        //await selectLineItem2.ClickAsync();
       //await Assertions.Expect(_combineLineItemsBtn).ToBeDisabledAsync(); 
    }

    // Split By Percentage
    public async Task SplitByPercentage()
    {
        
        await _selectLineItem1.EvaluateAsync("node=>node.click()");
        if(!await IsVisibleAsync(_splitLineItemBtn,1000,5)){
            throw  new Exception("enable 706 perm for this user");
        }
        await _splitLineItemBtn.ClickAsync();
        await _splitLineItemGLCode2.ClickAsync();
        await _newlineLitemGLCodeSelect.WaitForAsync();
        await _newlineLitemGLCodeSelect.EvaluateAsync("node=>node.click()");
        await _splitLineItem1.FillAsync("50");
        await _splitLineItem2.FillAsync("50");
        await Assertions.Expect(_totalAmount).ToContainTextAsync("100.00%");
        await _confirmSplit.ClickAsync();
        //await _saveBtn.ClickAsync();
    }

    //Split by Quantity 
    public async Task SplitByQuantity()
    {
        await _selectLineItem2.EvaluateAsync("node=>node.click()");
        if(!await IsVisibleAsync(_splitLineItemBtn,1000,5)){
            throw  new Exception("enable 706 perm for this user");
        }
        await _splitLineItemBtn.ClickAsync();
        await _splitByDropdown.WaitForAsync();
        await _splitByDropdown.ClickAsync();
        await _splitByQuantity.ClickAsync();
        await _splitLineItemGLCode2.ClickAsync();
        await _newlineLitemGLCodeSelect.WaitForAsync();
        await _newlineLitemGLCodeSelect.EvaluateAsync("node=>node.click()");
        await _splitLineItem1.FillAsync("0.5");
        await _splitLineItem2.FillAsync("0.5");
        await Assertions.Expect(_totalAmount).ToContainTextAsync("1.00");
        string getGLCode1=await _splitLineItemGLCode1.InnerTextAsync();
        string getGLCode2=await _splitLineItemGLCode2.InnerTextAsync();
        string amount1=await _splitLineItem1.InnerTextAsync();
        string amount2=await _splitLineItem2.InnerTextAsync();
        await _confirmSplit.ClickAsync();
        await Assertions.Expect(_currentDocQtyItem2(6)).ToContainTextAsync(amount1);
        await Assertions.Expect(_currentDocQtyItem4(6)).ToContainTextAsync(amount2);
        await VerifyDeleteBtn();
    }
   
    // Verifying GL Subtotal column values by adding line items subtotal values
    public async Task VerifyGLDistributionGridColumns(string column)
    {
        await this.VerifyTitleGLSummary(column);
    }

    //GL Distribution on Invoice Details form
    public async Task GLDistribution(CommonContext commonContext)
    {
        //await AddLineItems();
        //await _priceColumn.ScrollIntoViewIfNeededAsync();
        var subtotalElements = _iframe.Locator("#invoiceLineItemGrid tbody tr td:nth-child(13)");
        var elements=await subtotalElements.AllAsync();
        decimal invoiceTotal  = 0;
        foreach (var element in elements)
        {
            string text = await element.InnerTextAsync();
            text = text.Replace("$", string.Empty).Trim();
            if (decimal.TryParse(text,out decimal value))
            {
                invoiceTotal += value;
            }
            else
            {
                Console.WriteLine("Parsing failed for: " + text);
            }
        }
        await Assertions.Expect(_GLSubTotal).ToContainTextAsync(invoiceTotal.ToString());
        if(commonContext.TaxEnabled){
            await Assertions.Expect(_GLTaxableAmt).ToContainTextAsync(invoiceTotal.ToString());
        }
    }

    //Verify Shipping 
    public async Task VerifyShippingValue(CommonContext commonContext)
    {
        await _shipping.FillAsync("1.00");
        string shippingValueText = await _shipping.InputValueAsync();
        shippingValueText = shippingValueText.Replace("$", "");
        if (string.IsNullOrWhiteSpace(shippingValueText) || !double.TryParse(shippingValueText, out double shippingValue))
        {
            Console.WriteLine($"Unable to convert '{shippingValueText}' to a double.");
            return;
        }
        var frame = Page.Frame(name: "v4Container");
        if(frame!=null){
            var GLSubTotals = await frame.QuerySelectorAllAsync("div:nth-child(4) div > div:nth-child(1) > table > tbody > tr > td:nth-child(4) > div");
            var GLSubTotal = await Task.WhenAll(GLSubTotals.Select(async element => await element.InnerTextAsync()));
            var values = GLSubTotal.ToArray();
            double[] finalValues = new double[values.Length];
            for (int i = 0; i < values.Length; i++)
            {
                string valueText = values[i].Replace("$", "");
                if (string.IsNullOrWhiteSpace(valueText) || !double.TryParse(valueText, out double elementValue))
                {
                    Console.WriteLine($"Unable to convert '{valueText}' to a double.");
                    continue;
                }
                finalValues[i] = elementValue / Convert.ToDouble(commonContext.InvoiceTotal) * shippingValue;
                var validationElements = await frame.QuerySelectorAllAsync("div:nth-child(4) div > div:nth-child(1) > table > tbody > tr > td:nth-child(7) > div");
                var validationValues = validationElements.ToArray();
                string validationText = await validationValues[i].InnerTextAsync();
                validationText = validationText.Replace("$", "");
                if (!double.TryParse(validationText, out double validationValue))
                {
                    Console.WriteLine($"Unable to convert '{validationText}' to a double.");
                    continue;
                }
                if (Math.Abs(finalValues[i] - validationValue) > 0.01)
                {
                    Console.WriteLine($"Validation failed for row {i + 1}: expected {finalValues[i]}, but got {validationValue}.");
                }
                else
                {
                    Console.WriteLine($"Validation passed for row {i + 1}: expected {finalValues[i]}, and got {validationValue}.");
                }
            }
        }
    }

    //Verifying discount column values in the GL Summary by calculating GL Subtotal/total*Invoice Discount Value
    public async Task VerifyDiscountValue(CommonContext commonContext)
    {
        await _discount.FillAsync("2.00");
        string discountValueText = await _discount.InputValueAsync();
        discountValueText = discountValueText.Replace("$", "");
        if (string.IsNullOrWhiteSpace(discountValueText) || !double.TryParse(discountValueText, out double discountValue))
        {
            Console.WriteLine($"Unable to convert '{discountValueText}' to a double.");
            return;
        }
        var frame = Page.Frame(name: "v4Container");
        if(frame!=null){
            var GLSubTotals = await frame.QuerySelectorAllAsync("div:nth-child(4) div > div:nth-child(1) > table > tbody > tr > td:nth-child(4) > div");
            var GLSubTotal = await Task.WhenAll(GLSubTotals.Select(async element => await element.InnerTextAsync()));
            var values = GLSubTotal.ToArray();
            double[] finalValues = new double[values.Length];
            for (int i = 0; i < values.Length; i++)
            {
                string valueText = values[i].Replace("$", "");
                if (string.IsNullOrWhiteSpace(valueText) || !double.TryParse(valueText, out double elementValue))
                {
                    Console.WriteLine($"Unable to convert '{valueText}' to a double.");
                    continue;
                }
                finalValues[i] = elementValue / Convert.ToDouble(commonContext.InvoiceTotal) * discountValue;
                var validationElements = await frame.QuerySelectorAllAsync("div:nth-child(4) div > div:nth-child(1) > table > tbody > tr > td:nth-child(6) > div");
                var validationValues = validationElements.ToArray();
                string validationText = await validationValues[i].InnerTextAsync();
                validationText = validationText.Replace("$", "");
                if (!double.TryParse(validationText, out double validationValue))
                {
                    Console.WriteLine($"Unable to convert '{validationText}' to a double.");
                    continue;
                }
                if (Math.Abs(finalValues[i] - validationValue) > 0.01)
                {
                    Console.WriteLine($"Validation failed for row {i + 1}: expected {finalValues[i]}, but got {validationValue}.");
                }
                else
                {
                    Console.WriteLine($"Validation passed for row {i + 1}: expected {finalValues[i]}, and got {validationValue}.");
                }
            }
        }
    }

    // Verifying Taxes column values in the GL Summary by calculating GL Subtotal/total*Invoice Taxes Value 
    public async Task VerifyTaxesValue(CommonContext commonContext)
    {
        await _taxes.FillAsync("2.00");
        string taxesValueText = await _taxes.InputValueAsync();
        taxesValueText = taxesValueText.Replace("$", "");
        if (string.IsNullOrWhiteSpace(taxesValueText) ||!double.TryParse(taxesValueText, out double taxesValue))
        {
            Console.WriteLine($"Unable to convert '{taxesValueText}' to a double.");
            return;
        }
        var frame = Page.Frame(name: "v4Container");
        if(frame!=null){
            var GLSubTotals = await frame.QuerySelectorAllAsync("div:nth-child(4) div > div:nth-child(1) > table > tbody > tr > td:nth-child(4) > div");

            var GLSubTotal = await Task.WhenAll(GLSubTotals.Select(async element => await element.InnerTextAsync()));
            var values = GLSubTotal.ToArray();
            double[] finalValues = new double[values.Length];
            for (int i = 0; i < values.Length; i++)
            {
                string valueText = values[i].Replace("$", "");
                if (string.IsNullOrWhiteSpace(valueText) || !double.TryParse(valueText, out double elementValue))
                {
                    Console.WriteLine($"Unable to convert '{valueText}' to a double.");
                    continue;
                }
                finalValues[i] = elementValue / Convert.ToDouble(commonContext.InvoiceTotal) * taxesValue;
                var validationElements = await frame.QuerySelectorAllAsync("div:nth-child(4) div > div:nth-child(1) > table > tbody > tr > td:nth-child(8) > div");
                var validationValues = validationElements.ToArray();
                string validationText = await validationValues[i].InnerTextAsync();
                validationText = validationText.Replace("$", "");
                if (!double.TryParse(validationText, out double validationValue))
                {
                    Console.WriteLine($"Unable to convert '{validationText}' to a double.");
                    continue;
                }
                if (Math.Abs(finalValues[i] - validationValue) > 0.01)
                {
                    Console.WriteLine($"Validation failed for row {i + 1}: expected {finalValues[i]}, but got {validationValue}.");
                }
                else
                {
                    Console.WriteLine($"Validation passed for row {i + 1}: expected {finalValues[i]}, and got {validationValue}.");
                }
            }
        }    
    }

    // Verifying grand total column values by calculating sum of(GL Subtotal,Shipping,Taxes)-discount value and print that value in the grandtotal text field
    public async Task VerifyGrandTotalValue()
    {
        var frame=Page.Frame(name: "v4Container");
        if(frame!=null){
            var GLSubTotals = await frame.QuerySelectorAllAsync("div:nth-child(4) div > div:nth-child(1) > table > tbody > tr > td:nth-child(4) > div");
            var shippingValues=await frame.QuerySelectorAllAsync("div:nth-child(4) div > div:nth-child(1) > table > tbody > tr > td:nth-child(7) > div");
            var taxValues=await frame.QuerySelectorAllAsync("div:nth-child(4) div > div:nth-child(1) > table > tbody > tr > td:nth-child(8) > div");
            var discountValues=await frame.QuerySelectorAllAsync("div:nth-child(4) div > div:nth-child(1) > table > tbody > tr > td:nth-child(6) > div");
            var grandTotalValues=await frame.QuerySelectorAllAsync("div:nth-child(4) div > div:nth-child(1) > table > tbody > tr > td:nth-child(9) > div");

            var GLSubTotal = await Task.WhenAll(GLSubTotals.Select(async element => double.Parse((await element.InnerTextAsync()).Replace("$", ""))));
            var shippingvalue=await Task.WhenAll(shippingValues.Select(async element => double.Parse((await element.InnerTextAsync()).Replace("$", ""))));
            var taxValue=await Task.WhenAll(taxValues.Select(async element => double.Parse((await element.InnerTextAsync()).Replace("$", ""))));
            var discountValue=await Task.WhenAll(discountValues.Select(async element => double.Parse((await element.InnerTextAsync()).Replace("$", ""))));
            var grandTotalValue=await Task.WhenAll(grandTotalValues.Select(async element => double.Parse((await element.InnerTextAsync()).Replace("$", ""))));
            for(int i=0;i<GLSubTotal.Length;i++)
            {
                double calculatedValue=GLSubTotal[i]+shippingvalue[i]+taxValue[i]-discountValue[i];
                if(Math.Abs(calculatedValue - grandTotalValue[i]) > 0.01)
                {
                    Console.WriteLine($"Validation failed for row {i + 1}: expected {calculatedValue}, but got {grandTotalValue[i]}");
                }
                else
                {
                    Console.WriteLine($"Validation passed for row {i + 1}: expected {calculatedValue}, and got {grandTotalValue[i]}");
                }
            }
            await _grandTotal.FillAsync(grandTotalValue[grandTotalValue.Length-1].ToString());
            await _saveBtn.ClickAsync();
        }
    }
 
    //Related Document titles verification on Invoice Details form
    public async Task VerifyInvoiceRelatedDocsGridColumns(string column)
    {
        await this.VerifyTitle(column);
    }

    //Verify Current Invoice Number present under Related Documents
    public async Task VerifyCurrentInvoiceNumberIsShowingInRelatedDocGrid()
    {
        var _invoiceNumber= await _invoiceInput.InnerTextAsync();
        await Assertions.Expect(_relatedInvoiceNumber).ToContainTextAsync(_invoiceNumber);
    }

    //Navigating back to base page
    public async Task ReopenPage(CommonContext commonContext,string receiptNo)
    {
        commonContext.NewReceiptNo = receiptNo;
        await BringBasePageToFront(commonContext);
        await _firstDivOpen.ClickAsync();
    }

    //Create New Receipt from related documents present on Invoice details form
    public async Task<IPage> CreateNewReceipt(CommonContext commonContext)
    {

        await _createNewDocument.EvaluateAsync("node=>node.click()");
        if(await IsVisibleAsync(_createReceiptBtn,1000,5)){
            await _createReceiptBtn.ClickAsync();
        }
        else
        {
            Assert.Fail("Receipt button is not visible because Receiving Permission is off");
        }

        IPage newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await continueWithoutattachemnt.ClickAsync();
        });
        BringSecondPageToFront(newPage,commonContext);
        return Page;
    }

    //Receipt button should not visible if Receiving Permission is off
    public async Task CreateNewReceiptBtn()
    {
        await _createNewDocument.ClickAsync();
    }

   //Create New Invoice /Creditmemo from related documents present on Invoice details form
    public async Task CreateNewInvoice(CommonContext commonContext)
    {
        await _createNewDocument.EvaluateAsync("node=>node.click()");
        if(await IsVisibleAsync(_invoiceCreditBtn,1000,5)){
            await _invoiceCreditBtn.ClickAsync();
        }
        else
        {
            Assert.Fail("Invoice button is not visible because Invoicing Permission is off");
        }
        var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await continueWithoutattachemnt.ClickAsync();
            if(await IsVisibleAsync(assignMulti,1000,5)){
                commonContext.HasMulti = true;
                await assignMulti.ClickAsync();
            }else{
                commonContext.HasMulti = false;
            }
        });
        BringSecondPageToFront(newPage,commonContext);
        await CreateInvoiceCredit(new List<string>(),1,commonContext,false);
        await BringBasePageToFront(commonContext);
        await RefeshPage();
        await _firstDivOpen.ClickAsync();
        //await Assertions.Expect(relatedNewInvoiceNumber).ToContainTextAsync(invoiceNo);
    }

    // Verify Link to Selected functionality
    public async Task LinkToSelected(CommonContext commonContext)
    {
        await RefeshPage();
        await _firstDivOpen.ClickAsync();
        var totalCount=await _rowsInRelated.CountAsync();
        for(int i=1;i<=(totalCount-_rowCount);i++)
        {
            string fullTextInLinkedDoc=(await _relateddocNumber(i).InnerTextAsync()??"").Trim();
            var documentType=await _documentType(i).InnerTextAsync();
            if(documentType=="Receipt" && fullTextInLinkedDoc==commonContext.NewReceiptNo)
            {
                await _documentInput(i).CheckAsync();
                await _linkToSelectedBtn.ClickAsync();
                await VerifyInvoiceStatus("linked to invoice");
                break;
            }
        }
        await _firstDivOpen.ClickAsync();
    }

    //Expand Related Documents section
    public async Task OpenFirstDiv(){
        await _firstDivOpen.ClickAsync();
    }

    // Verify Unlink Receipt functionality
    public async Task UnlinkReceipt(CommonContext commonContext)
    {
        var totalCount=await _rowsInRelated.CountAsync();
        for(int i=1;i<=totalCount;i++)
        {
            string fullTextInLinkedDoc=await _relateddocTest(i).InnerTextAsync();
            string[] texts = fullTextInLinkedDoc.Split('\n');
            string linkedDocNumber=texts[0];
            var status=await _documentStatus(i).InnerTextAsync();
            if(linkedDocNumber==commonContext.NewReceiptNo && status=="In Progress")
            {
                await _relateddocButton(i).ClickAsync();
                await VerifyInvoiceStatus("linked to invoice changed");
                break;
            }
        }
        await _firstDivOpen.ClickAsync();
    }

    //Cancelling a document from Related Documents present on Invoice details form
    public async Task CancelSelectedDoc()
    {
        var totalCount=await _rowsInRelated.CountAsync();
        for(int i=1;i<=totalCount;i++)
        {
            string status=await _documentStatus(i).InnerTextAsync();
            if("In Progress".Equals(status))
            {
                await _documentInput(i).CheckAsync();
                if(await _cancelSelectedDocumentBtn.IsEditableAsync()){
                    await _cancelSelectedDocumentBtn.ClickAsync();
                    await _approveBtnConfirm.ClickAsync();
                    await VerifyInvoiceStatus("Cancelled");
                    break;
                }
                
            }
            await _documentInput(i).CheckAsync();
        }
        await _firstDivOpen.ClickAsync();
    }
    private async Task ThreeWayTask(CommonContext commonContext){
        bool isThreeWay = await CheckForLinked();
        if(isThreeWay){
            ReceiptPage rp = new ReceiptPage(await CreateNewReceipt(commonContext));
            string rcNo = await rp.CreateReceipt(new List<string>());
            await rp.CompleteReceipt();
            rcNo = rcNo.Trim();
            await ReopenPage(commonContext,rcNo);
            await LinkToSelected(commonContext);
        }
    }
    // Bulk Actions on View Invoices page
    public async Task CreateBulkInvoices(CommonContext commonContext)
    {
        await Page.ReloadAsync(); 
        await _navHeader.First.WaitForAsync();
        if(await IsVisibleAsync(_company,1000,5)){
            await _company.ClickAsync();
            await _companySelect.ClickAsync();
        }
        int idx = 1;
        for(int i=0; i<2;i++)
        {
            /*if(i==1){
                
            }*/
            await _poFilter.ClickAsync();
            await _filterInput.ClearAsync();
            await _filterInput.FillAsync(commonContext.PoNumbers[i]);
            await _filterBtn.ClickAsync();
            var newPage=await Page.Context.RunAndWaitForPageAsync(async () =>
            {
                await _createNewInvoice.ClickAsync();
                commonContext.HasMulti = false;
            });
            BringSecondPageToFront(newPage,commonContext);
            await CreateInvoiceCredit(new List<string>(), idx, commonContext,false);
            await ThreeWayTask(commonContext);
            idx++;

            await BringBasePageToFront(commonContext);
        } 
    }

    //Filter PO on View invoices form
    private async Task FilterPO(string poNumber)
    {
        await _invoiceNumberFilterBtn.ClickAsync();
        await _filterInput.ClearAsync();
        await _filterInput.FillAsync(poNumber+"-");
        await _filterBtn.ClickAsync();
        await _iframe.Locator(".k-grid-table tr:nth-child(1) td:nth-child(1) input").ClickAsync();
        await _iframe.Locator(".k-grid-table tr:nth-child(2) td:nth-child(1) input").ClickAsync();
    }

    //Select Filtered PO's on View Invoices form
    private async Task SelectPos(CommonContext commonContext){
        await FilterPO(commonContext.PoNumber);
    }

    //Bulk Delete on View Invoices form
    public async Task BulkDelete(CommonContext commonContext)
    {
        await _clearFiltersBtn.ClickAsync();
        await SelectPos(commonContext);
        if(await bulkDeleteBtn.IsEnabledAsync()){
            await bulkDeleteBtn.WaitForAsync();
            await bulkDeleteBtn.ClickAsync();
           if(await IsVisibleAsync(okConfirmBtn,1000,5)){
                await okConfirmBtn.ClickAsync();
            }
        }
    }

   //Bulk Undelete on View invoices 
    public async Task BulkUndelete(CommonContext commonContext)
    {
        await _clearFiltersBtn.ClickAsync();
        await _showFilters.ClickAsync();
        await _showDeleted.ClickAsync();
        await SelectPos(commonContext);
         
        if(await bulkUndeleteBtn.IsEnabledAsync()){
            await bulkUndeleteBtn.WaitForAsync();
            await bulkUndeleteBtn.ClickAsync();
            if(await IsVisibleAsync(okConfirmBtn,1000,5)){
                await okConfirmBtn.ClickAsync();
            }
        }
    }
    

    //Verify Invoice History for actions on View Invoices 
    private async Task VerifyInvoiceHistoryStatus(string invoiceNumbers, string status,string status1, CommonContext commonContext)
    {
        await _invoiceNumberFilterBtn.ClickAsync();
        await _filterInput.ClearAsync();
        await _filterInput.FillAsync(invoiceNumbers);
        await _filterBtn.ClickAsync();
        await invoiceStatus.WaitForAsync();
    }

    //Verify Bulk Deleted record statuses on Invoices details
    public async Task VerifyBulkDelStatus(CommonContext commonContext)
    {
        await _showFilters.ClickAsync();
        await _showDeleted.ClickAsync();
        await VerifyInvoiceHistoryStatus(commonContext.PoNumber+"-1","Deleted","Deleted",commonContext);
        await VerifyInvoiceHistoryStatus(commonContext.PoNumber+"-2","Deleted","Deleted",commonContext);
    }

    //Verify Bulk UnDeleted record statuses on Invoices details
    public async Task VerifyBulkUndelStatus(CommonContext commonContext)
    {
        await _showFilters.ClickAsync();
        await showAllInvoices.ClickAsync();
        await VerifyInvoiceHistoryStatus(commonContext.PoNumber+"-1","In Progress","Undeleted",commonContext);
        await VerifyInvoiceHistoryStatus(commonContext.PoNumber+"-2","In Progress","Undeleted",commonContext);
    }

   //Verify Bulk Close PO record statuses on Invoices details
    public async Task BulkClosePO(CommonContext commonContext)
    {
        await _clearFiltersBtn.ClickAsync();
        await SelectPos(commonContext);
        if(await bulkClosePOBtn.IsEnabledAsync()){
            await bulkClosePOBtn.WaitForAsync();
            await bulkClosePOBtn.ClickAsync();
            await okConfirmBtn.ClickAsync();
            await okConfirmBtn.ClickAsync();
        }
    }

    // Verify Bulk Close PO 
    public async Task VerifyBulkClosePOStatus(CommonContext commonContext)
    {
        await _showFilters.ClickAsync();
        await showAllInvoices.ClickAsync();
        await VerifyInvoiceHistoryStatus(commonContext.PoNumber+"-1","Completed","PO Closed",commonContext);
        await VerifyInvoiceHistoryStatus(commonContext.PoNumber+"-2","Completed","PO Closed",commonContext);
    }

    //Verify Bulk Complete records
    public async Task BulkCompleteDocument(CommonContext commonContext)
    {
        await _clearFiltersBtn.ClickAsync();
        await SelectPos(commonContext);
        if(await bulkCompleteDocBtn.IsEnabledAsync()){
            await bulkCompleteDocBtn.WaitForAsync();
            await bulkCompleteDocBtn.ClickAsync();
            await okConfirmBtn.ClickAsync();
            await okConfirmBtn.ClickAsync();
        }
    }

    // Verify Bulk Complete Status
    public async Task VerifyBulkCompleteStatus(CommonContext commonContext)
    {
       await _showFilters.ClickAsync();
       await showAllInvoices.ClickAsync();
       await VerifyInvoiceHistoryStatus(commonContext.PoNumber+"-1","Completed","Complete",commonContext);
       await VerifyInvoiceHistoryStatus(commonContext.PoNumber+"-2","Completed","Complete",commonContext);
    }

    //Update Taxes on Line items present on Invoice details
    public async Task UpdateTaxes(List<PoTaxDetails> txes, CommonContext commonContext){
        float totalTaxes = 0;
        float totalDiscounts = 0;
        float totalShipping = 0;
        for(int i=0;i<txes.Count;i++){
            PoTaxDetails texs = txes[i];
            totalDiscounts+=float.Parse(texs.Discount, CultureInfo.InvariantCulture.NumberFormat);
            if(commonContext.TaxEnabled){
                totalTaxes+=float.Parse(texs.Tax, CultureInfo.InvariantCulture.NumberFormat);
            }
            totalShipping+=float.Parse(texs.Shipping, CultureInfo.InvariantCulture.NumberFormat);
        }
        //await _discount.ScrollIntoViewIfNeededAsync();
        await _discount.ClearAsync();
        await _discount.FillAsync(totalDiscounts.ToString("n2"));
        await _taxes.ClearAsync();
        await _taxes.FillAsync(totalTaxes.ToString("n2"));
        await _shipping.ClearAsync();
        await _shipping.FillAsync(totalShipping.ToString("n2"));
        Thread.Sleep(500);
        await _itemTotalInput.FillAsync(GetAmount(await _itemTotal(commonContext.HasMulti?10:9).TextContentAsync()??string.Empty)+"");
        await _saveBtn.ClickAsync();
    }

    public async Task SearchInvoice(CommonContext commonContext)
    {
        var originalPage = Page;
        await _invoiceNumberFilterBtn.ClickAsync();
        await _filterInput.ClearAsync();
        await _filterInput.FillAsync(commonContext.InvoiceNumber);
        await _filterBtn.ClickAsync();
        var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await invoiceLink.ClickAsync();
        });
        BringSecondPageToFront(newPage,commonContext);
        await _invPopupBtn.ClickAsync();
        await editPeriods.ClickAsync();
        if (!commonContext.HasMulti)
        {
            await ValidatePeriods(commonContext);
            await updateMPABase("1", commonContext, true);
            commonContext.HasMulti = true; // Set the flag to indicate that this part has been executed
        }
        else
        {
            // Steps to execute only on subsequent runs
            await covertToSingleBtn.ClickAsync();
            await _saveModel.ClickAsync();
        }
        await newPage.CloseAsync();
        Page = originalPage;
    }

    //Verify Credit Memo
    public async Task VerifyCreditMemo(CommonContext commonContext)
    {
        var index =0;
        await _createNewDocument.EvaluateAsync("node=>node.click()");
        if(await IsVisibleAsync(_invoiceCreditBtn,1000,5)){
            await _invoiceCreditBtn.ClickAsync();
        }
        else
        {
            Assert.Fail("Invoice button is not visible because Invoicing Permission is off");
        }
        var newPage = await Page.Context.RunAndWaitForPageAsync(async () =>
        {
            await continueWithoutattachemnt.ClickAsync();
            if(await IsVisibleAsync(assignMulti,1000,5)){
                await assignMulti.ClickAsync();
                commonContext.HasMulti = true;
            }else{
                commonContext.HasMulti = false;
            }
        });
        BringSecondPageToFront(newPage,commonContext);
        await _invPopupBtn.ClickAsync();
        await _invoiceInput.FillAsync(commonContext.PoNumber+"-1");
        await _receivedDate.ClickAsync();
        await _receivedDateSelect.ClickAsync();
        await Task.Delay(2000);
        await _itemQuantity.Nth(index).ClearAsync();
        await _itemQuantity.Nth(index).FillAsync("-0.5");
        await _grandTotal.ClearAsync();
        await _grandTotal.FillAsync("-0.5");
        await _invoiceNotes1(commonContext.HasMulti?4:3).FillAsync("credit");
        if(await _saveBtn.IsEnabledAsync()){
            await _saveBtn.ClickAsync();
        }
         if(!await invoiceScan.IsVisibleAsync()){
            await _showScans.ClickAsync();
        }
        await _uploadBtn.ClickAsync();
        await _uploadDoc.SetInputFilesAsync(UPLOADPDFFILE);
        await _uploadClose.ClickAsync();
        await _hideScans.ClickAsync();   
        
        if(await _saveBtn.IsEnabledAsync()){
            await _saveBtn.ClickAsync();
        }
        await ThreeWayTask(commonContext);
        await _completeBtn.ClickAsync();  
        if(await IsVisibleAsync(_confirmPopupBtn,1000,5)){
            await _confirmPopupBtn.ClickAsync();
        }
        await _closePOBtn.WaitForAsync();
        await _closePOBtn.ClickAsync();
        await _confirmBtn.WaitForAsync();
        await _confirmBtn.ClickAsync();
        await _confirmPopupBtn.WaitForAsync();
        await _confirmPopupBtn.ClickAsync();
        await BringBasePageToFront(commonContext);
    }    
}