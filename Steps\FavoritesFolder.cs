using TechTalk.SpecFlow;
using SpecFlowProject.Pom.Pages;
using SpecFlowProject.Hooks;
using System.Collections;
using SpecFlowProject.Utils;
using SpecFlowProject.BusinessObjects;

namespace SpecFlowProject.Steps
{
    [Binding]
    public class FavoritesFolder
    {
        readonly Context _context;
        readonly Favorites _favorites;
        private ScenarioContext _scenarioContext;
        private readonly LoggerService _logger;

        public FavoritesFolder(Context context, ScenarioContext scenarioContext)
        {
            _context = context;
            _favorites = new Favorites(_context.Page!);
            _scenarioContext = scenarioContext;
            _logger = LoggerService.Instance;
            _logger.Information("FavoritesFolder step definitions initialized");
        }

        [Then(@"Click on Favorites Products link")]
        public async Task ClickFavoritesProducts()
        {
            _logger.Information("Starting: Click on Favorites Products link");
            try
            {
                await _favorites.OpenFavorites((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Successfully clicked on Favorites Products link");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error clicking on Favorites Products link");
                throw;
            }
        }

        [Then(@"Create Buyers Owned Folder")]
        public async Task AddNewOwnedFolder()
        {
            _logger.Information("Starting: Create Buyers Owned Folder");
            try
            {
                await _favorites.AddNewOwnedFolder((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Successfully created Buyers Owned Folder");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error creating Buyers Owned Folder");
                throw;
            }
        }

        [Then(@"Create Shared Folder")]
        public async Task AddNewSharedFolder()
        {
            _logger.Information("Starting: Create Shared Folder");
            try
            {
                await _favorites.AddNewSharedFolder((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Successfully created Shared Folder");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error creating Shared Folder");
                throw;
            }
        }

        [Then(@"Add Products in to the Owned Folder")]
        public async Task AddProductsToOwnedFolder()
        {
            _logger.Information("Starting: Add Products to the Owned Folder");
            try
            {
                CommonContext commonContext = (CommonContext)_scenarioContext["commonContext"];
                await _favorites.AddProductsToOwnedFolder(commonContext);
                _logger.Information("Successfully added products to Owned Folder. Supplier: {SupplierName}", commonContext.SupplierName);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error adding products to Owned Folder");
                throw;
            }
        }

        [Then(@"Add Products in to the Shared Folder")]
        public async Task AddProductsToSharedFolder()
        {
            _logger.Information("Starting: Add Products to the Shared Folder");
            try
            {
                CommonContext commonContext = (CommonContext)_scenarioContext["commonContext"];
                await _favorites.AddProductsToSharedFolder(commonContext);
                _logger.Information("Successfully added products to Shared Folder. Supplier: {SupplierName}", commonContext.SupplierName);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error adding products to Shared Folder");
                throw;
            }
        }

        [Then(@"Verify GL Code from the Favourites")]
        public async Task VerifyGLCode()
        {
            _logger.Information("Starting: Verify GL Code from the Favourites");
            try
            {
                CommonContext commonContext = (CommonContext)_scenarioContext["commonContext"];
                await _favorites.VerifyGLCodeFromFavorites(commonContext);
                _logger.Information("Successfully verified GL Code from Favourites. GL Code: {GLCode}",
                    commonContext.AddedProductsToOwnedFolder ? commonContext.OwnedGLCode :
                    (!string.IsNullOrWhiteSpace(commonContext.SpecificGL) ? commonContext.SpecificGL : commonContext.OwnedGLCode));
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error verifying GL Code from Favourites");
                throw;
            }
        }

        [Then(@"Verify Quantity")]
        public async Task VerifyQuantity()
        {
            _logger.Information("Starting: Verify Quantity");
            try
            {
                CommonContext commonContext = (CommonContext)_scenarioContext["commonContext"];
                await _favorites.VerifyGLCodeFromFavorites(commonContext, true);
                _logger.Information("Successfully verified Quantity. Expected Quantity: {Quantity}", commonContext.QuantityTotal);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error verifying Quantity");
                throw;
            }
        }

        [Then(@"Verify UOM")]
        public async Task VerifyUOM()
        {
            _logger.Information("Starting: Verify UOM");
            try
            {
                CommonContext commonContext = (CommonContext)_scenarioContext["commonContext"];
                await _favorites.VerifyGLCodeFromFavorites(commonContext, true);
                _logger.Information("Successfully verified UOM");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error verifying UOM");
                throw;
            }
        }

        [Then(@"I update the PDF two ITEM# field with the CommonContext item number")]
        public void ThenIUpdateThePDFTwoITEMFieldWithTheCommonContextItemNumber()
        {
            _logger.Information("Starting: Update the PDF two ITEM# field with the CommonContext item number");
            try
            {
                // Get the CommonContext from the ScenarioContext
                CommonContext commonContext = (CommonContext)_scenarioContext["commonContext"];

                // Add a sample item number to the CommonContext if not already present
                if (commonContext.ItemNumbers.Count == 0)
                {
                    commonContext.ItemNumbers.Add("COMMON-ITEM-123");
                    _logger.Information("Added default item number: {ItemNumber}", commonContext.ItemNumbers[0]);
                }

                // Define input and output file paths
                string inputPdfPath = Path.Combine("UploadFiles", "TestPDF.pdf");
                string outputPdfPath = Path.Combine("UploadFiles", "UpdatedTestPDF.pdf");
                _logger.Information("Input PDF path: {InputPath}, Output PDF path: {OutputPath}",
                    Path.GetFullPath(inputPdfPath), Path.GetFullPath(outputPdfPath));

                // Create a new PDF with two line items using the same item number
                bool success = PdfGenerator.CreateInvoicePdfWithTwoItems(outputPdfPath, commonContext);
                _logger.Information("PDF generation result: {Success}", success);

                // Assert that the operation was successful
                Assert.IsTrue(success, "PDF creation operation with two line items failed");

                // Assert that the output file exists
                Assert.IsTrue(File.Exists(outputPdfPath), $"Output file was not created: {outputPdfPath}");

                _logger.Information("PDF with two line items created successfully!");
                _logger.Information("Item number used for both lines: {ItemNumber}", commonContext.ItemNumbers[0]);
                _logger.Information("Total quantity: {Quantity}", commonContext.QuantityTotal);
                _logger.Information("PDF saved to: {Path}", Path.GetFullPath(outputPdfPath));

                Console.WriteLine("PDF with two line items created successfully!");
                Console.WriteLine($"Item number used for both lines: {commonContext.ItemNumbers[0]}");
                Console.WriteLine($"Total quantity (2 + 2 = {commonContext.QuantityTotal}): {commonContext.QuantityTotal}");
                Console.WriteLine($"PDF saved to: {Path.GetFullPath(outputPdfPath)}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error updating PDF with two ITEM# fields");
                throw;
            }
        }

        [Then(@"I update the PDF ITEM# field with the CommonContext item number")]
        public void WhenIUpdateThePDFITEMFieldWithTheCommonContextItemNumber()
        {
            _logger.Information("Starting: Update the PDF ITEM# field with the CommonContext item number");
            try
            {
                // Get the CommonContext from the ScenarioContext
                CommonContext commonContext = (CommonContext)_scenarioContext["commonContext"];

                // Add a sample item number to the CommonContext if not already present
                if (commonContext.ItemNumbers.Count == 0)
                {
                    commonContext.ItemNumbers.Add("COMMON-ITEM-123");
                    _logger.Information("Added default item number: {ItemNumber}", commonContext.ItemNumbers[0]);
                }

                // Define input and output file paths
                string inputPdfPath = Path.Combine("UploadFiles", "TestPDF.pdf");
                string outputPdfPath = Path.Combine("UploadFiles", "UpdatedTestPDF.pdf");
                _logger.Information("Input PDF path: {InputPath}, Output PDF path: {OutputPath}",
                    Path.GetFullPath(inputPdfPath), Path.GetFullPath(outputPdfPath));

                // Create a new PDF with the custom item number
                bool success = PdfGenerator.CreateInvoicePdf(outputPdfPath, commonContext);
                _logger.Information("PDF generation result: {Success}", success);

                // Assert that the operation was successful
                Assert.IsTrue(success, "PDF creation operation failed");

                // Assert that the output file exists
                Assert.IsTrue(File.Exists(outputPdfPath), $"Output file was not created: {outputPdfPath}");

                _logger.Information("PDF created successfully!");
                _logger.Information("Item number used: {ItemNumber}", commonContext.ItemNumbers[0]);
                _logger.Information("PDF saved to: {Path}", Path.GetFullPath(outputPdfPath));

                Console.WriteLine("PDF created successfully!");
                Console.WriteLine($"Item number used: {commonContext.ItemNumbers[0]}");
                Console.WriteLine($"PDF saved to: {Path.GetFullPath(outputPdfPath)}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error updating PDF with ITEM# field");
                throw;
            }
        }

        [Then(@"I update the PDF two ITEM# field with the CommonContext item number and UOM Empty")]
        public void ThenIUpdateThePDFTwoITEMFieldWithTheCommonContextItemNumberAndUOMEmpty()
        {
            _logger.Information("Starting: Update the PDF two ITEM# field with the CommonContext item number and UOM Empty");
            try
            {
                // Get the CommonContext from the ScenarioContext
                CommonContext commonContext = (CommonContext)_scenarioContext["commonContext"];

                // Add a sample item number to the CommonContext if not already present
                if (commonContext.ItemNumbers.Count == 0)
                {
                    commonContext.ItemNumbers.Add("COMMON-ITEM-123");
                    _logger.Information("Added default item number: {ItemNumber}", commonContext.ItemNumbers[0]);
                }

                // Define input and output file paths
                string inputPdfPath = Path.Combine("UploadFiles", "TestPDF.pdf");
                string outputPdfPath = Path.Combine("UploadFiles", "UpdatedTestPDF.pdf");
                _logger.Information("Input PDF path: {InputPath}, Output PDF path: {OutputPath}",
                    Path.GetFullPath(inputPdfPath), Path.GetFullPath(outputPdfPath));

                // Create a new PDF with two line items using the same item number
                bool success = PdfGenerator.CreateInvoicePdfWithEmptyUOM(outputPdfPath, commonContext);
                _logger.Information("PDF generation with empty UOM result: {Success}", success);

                // Assert that the operation was successful
                Assert.IsTrue(success, "PDF creation operation with two line items failed");

                // Assert that the output file exists
                Assert.IsTrue(File.Exists(outputPdfPath), $"Output file was not created: {outputPdfPath}");

                _logger.Information("PDF with two line items and empty UOM created successfully!");
                _logger.Information("Item number used for both lines: {ItemNumber}", commonContext.ItemNumbers[0]);
                _logger.Information("Total quantity: {Quantity}", commonContext.QuantityTotal);
                _logger.Information("PDF saved to: {Path}", Path.GetFullPath(outputPdfPath));

                Console.WriteLine("PDF with two line items created successfully!");
                Console.WriteLine($"Item number used for both lines: {commonContext.ItemNumbers[0]}");
                Console.WriteLine($"Total quantity (2 + 2 = {commonContext.QuantityTotal}): {commonContext.QuantityTotal}");
                Console.WriteLine($"PDF saved to: {Path.GetFullPath(outputPdfPath)}");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error updating PDF with two ITEM# fields and empty UOM");
                throw;
            }
        }

        [Then(@"Verify Cancelled Invoice Line Item present in the New Invoice Line Items")]
        public async Task VerifyCancelledInvoiceLineItem()
        {
            _logger.Information("Starting: Verify Cancelled Invoice Line Item present in the New Invoice Line Items");
            try
            {
                await _favorites.VerifyLineItem((CommonContext)_scenarioContext["commonContext"]);
                _logger.Information("Successfully verified cancelled invoice line item in new invoice line items");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error verifying cancelled invoice line item");
                throw;
            }
        }
        [Then(@"Enter PO Number")]
        public async Task ThenEnterPONumber()
        {
            await _favorites.EnterPONumber((CommonContext)_scenarioContext["commonContext"]);
        }
        [Then(@"Select Supplier")]
        public async Task ThenSelectSupplier()
        {
            await _favorites.SelectSupplier((CommonContext)_scenarioContext["commonContext"]);
        }
    }
}
