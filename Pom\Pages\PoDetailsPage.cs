using Microsoft.Playwright;
using SpecFlowProject.BusinessObjects;
using System.Globalization;
using System.Text.RegularExpressions;

namespace SpecFlowProject.Pom.Pages;

using DocumentFormat.OpenXml.EMMA;
using SpecFlowProject.BusinessObjects;
using SpecFlowProject.Utils;
using System.Diagnostics.Metrics;
using TechTalk.SpecFlow.Infrastructure;

public class PoDetailsPage : Base
{
    // Class name for logging context
    private const string _className = nameof(PoDetailsPage);
    private ILocator _poInputTable => Page.Locator("#tblPOLineItems > tbody > tr").First;
    private ILocator _poSummaryTable => Page.Locator("#POShippingSummary > div > table > tbody");
    private ILocator _clickInvoice => Page.Locator("div.ui-button:has-text('Invoice/Credit and Log Only')");
    private ILocator _discountDistributionButton => _poSummaryTable.Locator("tr:nth-child(2) > td:nth-child(3) > span");
    private ILocator _salesTaxDistributionButton => _poSummaryTable.Locator("tr:nth-child(3) > td:nth-child(3) > span");
    private ILocator _shippingDistributionButton => _poSummaryTable.Locator("tr:nth-child(4) > td:nth-child(3) > span");
    private ILocator _subTotal => Page.Locator("#subTotal");
    private ILocator notNowButton => Page.Locator("button:has-text('Not Now')");
    private ILocator _grandTotal => Page.Locator("#grandTotal");
    private ILocator _discountInput => Page.Locator("input#DiscountAmt");
    private ILocator _discountTypeSelect => Page.Locator("select#DicsountTypeID");
    private ILocator _salesTaxTypeSelect => Page.Locator("select#SalesTaxTypeID");
    private ILocator _shippingTypeSelect => Page.Locator("select#ShippingTypeID");
    private ILocator _salesTaxInput => Page.Locator("input#SalesTaxAmt");
    private ILocator _shippingInput => Page.Locator("input#ShippingAmt");
    private ILocator _poSaveButton => Page.Locator("#poHeaderIcons > .headerIconsLeft > span:nth-child(1)");
    private ILocator _submitAndLogButton => Page.Locator("#actionButtonContainer div:has-text('Submit and Log Only')");
    private ILocator _acceptButton => Page.Locator("#actionButtonContainer div:has-text('Accept')");
    private ILocator _addLineItemButton => Page.Locator(".poLineItemsIcons > .iconifiedPOLineItem_SaveNew");
    private ILocator _poSaveCompletedButton => Page.Locator("#poHeaderIcons > .headerIconsLeft > .iconifiedPOSave");
    private ILocator _submitContinuePo => Page.Locator("button:has-text('Continue & Submit PO')");
    private ILocator _continuePoBtn => Page.GetByRole(AriaRole.Button, new PageGetByRoleOptions { Name = "Continue" });
    private ILocator _autoDistributeBtn => Page.GetByRole(AriaRole.Button, new PageGetByRoleOptions { Name = "Auto Distribute" });
    private ILocator _saveAndCloseBtn => Page.GetByRole(AriaRole.Button, new PageGetByRoleOptions { Name = "Save & Close" });
    private ILocator _okBtn => Page.GetByRole(AriaRole.Button, new() { Name = "OK" });
    private ILocator _poLineItem(int itemNumber) => Page.Locator("#tblPOLineItems > tbody > tr:nth-child(" + itemNumber + ")");
    private ILocator _price(int itemNumber) => _poLineItem(itemNumber).Locator(".price > input");
    private ILocator _itemNumber(int itemNumber) => _poLineItem(itemNumber).Locator(".itemNumber > .itemNumberContainer > input");
    private ILocator _qty(int itemNumber) => _poLineItem(itemNumber).Locator(".quantity > input");
    private ILocator _weight(int itemNumber) => _poLineItem(itemNumber).Locator(".weight > input");
    private ILocator _description(int itemNumber) => _poLineItem(itemNumber).Locator(".description > .descriptionContainer > input");
    private ILocator _uom(int itemNumber) => _poLineItem(itemNumber).Locator("select.selUnitCodes");
    private ILocator _glCode(int itemNumber) => _poLineItem(itemNumber).Locator("#AssignedGLCode_" + itemNumber + "_chosen > a");
    private ILocator _glCodeEnterInput(int itemNumber, int dropDownNum) => _poLineItem(itemNumber).Locator("#AssignedGLCode_" + itemNumber + "_chosen > .chosen-drop > .chosen-results > li:nth-child(" + dropDownNum + ")");
    private ILocator _glCodeEnterInputEnter(int itemNumber) => _poLineItem(itemNumber).Locator("#AssignedGLCode_" + itemNumber + "_chosen > .chosen-drop > .chosen-search >input");
    private ILocator _poNumber => Page.Locator("#PONumber");
    private ILocator _glSummary => Page.Locator("#glSummary tbody>tr");
    private ILocator _glSummaryItemCode(int i) => Page.Locator("#glSummary tbody>tr:nth-child(" + i + ") td:nth-child(1)");
    private ILocator _glSummaryItem(int i) => Page.Locator("#glSummary tbody>tr:nth-child(" + i + ") td:nth-child(3)");
    private ILocator _glSummaryItemDesc(int i) => Page.Locator("#glSummary tbody>tr:nth-child(" + i + ") td:nth-child(4)");
    private ILocator _glSummaryItemShip(int i) => Page.Locator("#glSummary tbody>tr:nth-child(" + i + ") td:nth-child(5)");
    private ILocator _glSummaryItemTax(int i) => Page.Locator("#glSummary tbody>tr:nth-child(" + i + ") td:nth-child(6)");
    private ILocator _glSummaryItemGlCode(int i) => Page.Locator("#glSummary tbody>tr:nth-child(" + i + ") td:nth-child(1)");
    private ILocator _glSummaryItemGLStartPeriod(int i) => Page.Locator("#glSummary tbody>tr:nth-child(" + i + ") td:nth-child(2)");
    private ILocator _glSummaryItemGL(int i) => Page.Locator("#glSummary tbody>tr:nth-child(" + i + ") td:nth-child(5)");
    private ILocator _glSummaryItemGlDesc(int i) => Page.Locator("#glSummary tbody>tr:nth-child(" + i + ") td:nth-child(6)");
    private ILocator _glSummaryItemGlShip(int i) => Page.Locator("#glSummary tbody>tr:nth-child(" + i + ") td:nth-child(7)");
    private ILocator _glSummaryItemGlTax(int i) => Page.Locator("#glSummary tbody>tr:nth-child(" + i + ") td:nth-child(8)");
    private ILocator _poCopyButton => Page.Locator("#poHeaderIcons > .headerIconsLeft > .iconifiedPOCopy");
    private ILocator _okandOpen => Page.GetByRole(AriaRole.Button, new PageGetByRoleOptions { Name = "OK & Open" });
    private ILocator accpetedPO => Page.Locator("#offlinePONavItems li:nth-child(7)");
    private ILocator filterAcceptedPO => Page.Locator("#po-search-box");
    private ILocator filterSearch => Page.Locator("#apply-search-box");
    private ILocator poClick => Page.Locator(".k-grid-content >table >tbody>tr:nth-child(1)>td:nth-child(2)");
    private ILocator _editPeriodsButton => Page.Locator("div.actionButton:has-text('Edit Periods')");
    private ILocator _deletePoItems => Page.Locator(".poLineItemsIcons .iconifiedPOLineItem_DeleteAll");
    private ILocator _deletePoRows => Page.Locator("#tblPOLineItems > tbody > .existingPOLines");
    private ILocator _btnConfirm => Page.Locator("#btnConfirm");

    private ILocator _submittedPage => Page.Locator("#poHistoryConatiner > span:nth-child(1)");

    private ILocator _isApproval => Page.Locator("#chkRequireApproval");
    private ILocator _rowsInSubmitForApproval => Page.Locator("#dialog-validation-results #gview_treegridCompanies tr.ui-widget-content");
    private ILocator _rowsInUserSubmitForApproval => Page.Locator("#dialog-validation-results #gview_treegridUsers tr.ui-widget-content");

    private ILocator _submitApprovalCompanyGrid(int i) => Page.Locator("#dialog-validation-results #gview_treegridCompanies tr.ui-widget-content:nth-child(" + i + ")");
    private ILocator _submitApprovalGrid(int i) => Page.Locator("#dialog-validation-results #gview_treegridUsers tr.ui-widget-content:nth-child(" + i + ") td:nth-child(2)");
    private ILocator _submitForApprovalBtn => Page.Locator("button:has-text('Submit For Approval')");
    private ILocator _sendForSubmitApproval => Page.GetByRole(AriaRole.Button, new() { Name = "Submit" });
    private ILocator _submitApprovalGridselected(int i) => Page.Locator("#dialog-validation-results #gview_treegridUsers tr.ui-widget-content:nth-child(" + i + ")");
    private ILocator _confirmPopupBtn => Page.Locator(".flex.justify-end.gap-2 button:nth-child(1)");
    private ILocator _approver => Page.Locator("#treegridUsers tr:nth-child(2)");
    private ILocator _submitApproval => Page.GetByRole(AriaRole.Button, new() { Name = "Submit" });

    private ILocator _lastChildInput => Page.Locator("#tblPOLineItems tbody > tr.existingPOLines:nth-child(1) .rowChk input");

    private ILocator _approveButton => Page.Locator("#actionButtonContainer div:has-text('Approve')");

    private ILocator _poInputTableGl => Page.Locator("#tblPOLineItems > tbody > tr.existingPOLines");
    private ILocator _gLCodeExempt => Page.Locator("td.assignedGL");
    private ILocator _selectglcodeexempt => Page.Locator("//div[@id='AssignedGLCode_1_chosen']//input[@type='text']");

    private ILocator glPeriod1 => Page.Locator("#GLSummary #glBeginingPeriod");
    private ILocator glPeriod2 => Page.Locator("#GLSummary #glEndingPeriod");
    private ILocator glBudgetPrev => Page.Locator("#glBudgetPrev span");
    private ILocator glBudgetNext => Page.Locator("#glBudgetNext span");
    private ILocator _periodrow1 => Page.Locator("tr >td:nth-child(3) span[class=k-input-inner]");
    private ILocator _epandedPeriod => Page.Locator("ul.k-treeview-lines li[aria-expanded=true]");
    private ILocator _lastYearperiod => Page.Locator("ul.k-treeview-lines li[aria-expanded=false]:last-child");
    private ILocator _lastPeriod => Page.Locator("ul.k-treeview-lines li[aria-expanded=true] li:last-child");
    //private ILocator _editPeriodBtn => Page.GetByText("Edit Periods", new() { Exact = true });

    private ILocator glPeriod3 => Page.Locator("#GLSummary #glBeginingPeriod1");
    private ILocator glPeriod4 => Page.Locator("#GLSummary #glEndingPeriod1");
    private ILocator _buttonCancel => Page.Locator(".ui-dialog").GetByRole(AriaRole.Button, new() { Name = "Cancel", Exact = true });
    private ILocator hsPopup => Page.Locator(".ui-dialog-title:has-text('Please Correct Purchase Order Error')");
    private ILocator hassubmitPopup => Page.Locator(".ui-dialog-title:has-text('Submit Purchase Order')");
    private ILocator hsPopupClose => Page.GetByRole(AriaRole.Button, new() { Name = "Close", Exact = true });

    private ILocator hsPopupsaveNotes => Page.Locator(".ui-dialog-buttonset #btnSaveNotes");

    private ILocator notesClick => Page.Locator("span[caption='View Purchase Order Notes']");

    private ILocator notesData => Page.Locator("#activeCommentsID");
    private ILocator SetTaxesGlCode => Page.Locator("#GLDistributeDiscDialogue .tblGLLines #AssignedGLCode");

    private ILocator SetTaxesGlCodeEnter(string text) => Page.Locator("#GLDistributeDiscDialogue .tblGLLines #AssignedGLCode option:has-text('" + text + "')");

    private ILocator proxyLogOffButton => Page.Locator("a[href='/Account/RemoveProxyUser']");
    private ILocator overBudgetIcon => Page.Locator("#glSummary td .showCaption");
    private ILocator budgetRemainingBalance => Page.Locator(".begBug.currency");
    private ILocator budgetEndingBalance => Page.Locator(".endBug.currency");
    private ILocator forecastRemainingBalance => Page.Locator(".begBal.currency");
    private ILocator forecastEndingBalance => Page.Locator(".endBal.currency");
    private ILocator changeUserFirstUser(string user) => Page.FrameLocator("#classicAspContainer>iframe").Locator("form table:nth-child(3) font:has-text('"+user+"') input").Nth(0);

    private ILocator hasLevels => Page.FrameLocator("#classicAspContainer>iframe").Locator("input[type='radio']:checked ~ a");

    private ILocator changeUserBtn => Page.FrameLocator("#classicAspContainer>iframe").Locator("#btnUser");
    private ILocator workArea => Page.Locator("a[href='/Buyer/Home/WorkArea']");
    private ILocator PeriodStartDate => Page.Locator("#glBeginingPeriod");
    private ILocator PeriodEndDate => Page.Locator("#glEndingPeriod");
    private ILocator _filterText => Page.Locator("#FilterText");
    private ILocator _filterBtn1 => Page.Locator("#btnApplyFilter");
    private ILocator _poDetailsGLTotal => Page.Locator("#glSummary tbody td:nth-child(7)");
    private ILocator _actionRequiredStartDate => Page.Locator("#glSummary .ui-widget-content td:nth-child(3)");
    private ILocator _actionRequiredEndDate => Page.Locator("#glSummary .ui-widget-content td:nth-child(4)");
    private ILocator _actionRequiredPOGLTotal => Page.Locator("#glSummary .ui-widget-content td:nth-child(5)");
    private ILocator _actionRequiredBudgetRemaining => Page.Locator("#glSummary .ui-widget-content td:nth-child(6)");
    private ILocator _actionRequiredForecastRemaining => Page.Locator("#glSummary .ui-widget-content td:nth-child(7) div");
    private ILocator _selectPO => Page.Locator("#poList input[type='checkbox']");
    private ILocator _AcceptPO => Page.Locator("#btnApprove");
    private ILocator _confirmAccept => Page.Locator("#btnConfirm");
    private ILocator _overSpendingIcon => Page.Locator("#POShippingSummary .purchaseOrderTotalsFooter .showCaption");
    private ILocator _actionReqoverSpendingIcon => Page.Locator(".iconifiedPOStatusFilterOverSpend");
    private ILocator _actionReqOverBudgetIcon => Page.Locator(".iconifiedPOStatusFilterOverBudget");
    private ILocator _statusDate => Page.Locator("#poList_status_date");
    private ILocator _submitForFurtherApproval => Page.Locator("button:has-text('Submit For Further Approval')");
    private ILocator _closeFurtherApprovalPopup => Page.Locator("div .ui-dialog-buttonset button").Nth(5);
    private ILocator _ActionReqNotInAValidPeriod => Page.Locator(".iconifiedPOStatusFilterBudgetPeriodNotValid");
    private ILocator _ActionReqNotInCurrentPeriod => Page.Locator(".iconifiedPOStatusFilterNotCurrentBudgetPeriod");
    private ILocator _notNowButton => Page.Locator("button:has-text('Not Now')");
    public static readonly string ITEM = "item";
    public PoDetailsPage(IPage page) : base(page)
    {
        _logger.Debug("{ClassName} initialized", _className);
    }
    
    //Open Create PO form present under marketplace
    public async Task OpenCreatePoPage()
    {
        _logger.Debug("{ClassName}.OpenCreatePoPage - Starting", _className);
        await _marketPlaceLink.ClickAsync();
        _logger.Debug("{ClassName}.OpenCreatePoPage - Clicked marketplace link", _className);
        await _createPoLink.ClickAsync();
        _logger.Debug("{ClassName}.OpenCreatePoPage - Clicked create PO link", _className);
        if (await IsVisibleAsync(_notNowButton, 1000, 5))
        {
            _logger.Debug("{ClassName}.OpenCreatePoPage - Not Now button is visible, clicking it", _className);
            await _notNowButton.ClickAsync();
        }
        _logger.Debug("{ClassName}.OpenCreatePoPage - Completed", _className);
    }

    //Getting PO number
    public async Task CopyPONumber(CommonContext commonContext, ISpecFlowOutputHelper _specFlowOutputHelper)
    {
        _logger.Debug("{ClassName}.CopyPONumber - Starting", _className);
        await accpetedPO.ClickAsync();
        _logger.Debug("{ClassName}.CopyPONumber - Clicked accepted PO", _className);
        string poNumber = await poClick.TextContentAsync() ?? "";
        poNumber = poNumber.Trim();
        _logger.Information("{ClassName}.CopyPONumber - Retrieved PO number: {PONumber}", _className, poNumber);
        commonContext.PoNumber = poNumber;
        _specFlowOutputHelper.WriteLine("PONumber=" + poNumber);
        commonContext.PoNumbers.Add(poNumber);
        _logger.Debug("{ClassName}.CopyPONumber - Completed", _className);
    }

    //Copy PO from already created po
    public async Task CopyPO(CommonContext commonContext, ISpecFlowOutputHelper _specFlowOutputHelper, bool accept = true)
    {
        _logger.Debug("{ClassName}.CopyPO - Starting with accept={Accept}", _className, accept);
        await accpetedPO.ClickAsync();
        _logger.Debug("{ClassName}.CopyPO - Clicked accepted PO", _className);
        if (!string.IsNullOrEmpty(commonContext.PoNumber))
        {
            _logger.Debug("{ClassName}.CopyPO - Filtering by PO number: {PONumber}", _className, commonContext.PoNumber);
            await filterAcceptedPO.WaitForAsync();
            await filterAcceptedPO.FillAsync(commonContext.PoNumber);
            await filterSearch.ClickAsync();
        }
        commonContext.HasMulti = false;
        if (commonContext.Periods.Count > 0)
        {
            commonContext.PrevPeriods = commonContext.Periods.GetRange(0, commonContext.Periods.Count);
            commonContext.Periods.Clear();
        }
        await poClick.ClickAsync();
        await _poCopyButton.ClickAsync();
        await _okandOpen.ClickAsync();
        string poNum = await _poNumber.InputValueAsync();
        commonContext.PoNumber = poNum;
        _specFlowOutputHelper.WriteLine("PONumber=" + poNum);
        commonContext.PoNumbers.Add(poNum);
        bool present = false;
        bool changed = false;
        List<string> exempt = DateValidation.GetFilteredCodes(commonContext.GLCodesData);
        List<string> off = DateValidation.GetFilteredCodes(commonContext.GLCodesData, "Off");
        List<string> gcodes = exempt.Count > 0 ? exempt : off.Count > 0 ? off : DateValidation.GetFilteredCodes(commonContext.GLCodesData, "Required");
        foreach (var row in await _deletePoRows.AllAsync())
        {
            if (await row.Locator("td.quantity input").InputValueAsync() == "0")
            {
                await row.Locator("td:nth-child(2) .checkboxContainer input").ClickAsync();
                present = true;
            }
            else
            {
                await row.Locator("td.quantity input").FillAsync("1");
                var gCode = await row.Locator("td.assignedGL .chosen-single-with-deselect > span").TextContentAsync() ?? String.Empty;
                gCode = gCode.Split(" - ")[0];
                if (gcodes.Count > 0 && !gcodes.Contains(gCode))
                {
                    changed = true;
                    await row.Locator("td.assignedGL .chosen-single-with-deselect").ClickAsync();
                    await row.Locator("td.assignedGL .chosen-drop > .chosen-search >input").TypeAsync(gcodes[0]);
                    await Page.Keyboard.PressAsync("Enter");
                    gCode = gcodes[0];
                }
                if (commonContext.GlCodes.IndexOf(gCode) == -1)
                {
                    commonContext.GlCodes.Add(gCode);
                }
            }
        }
        await EnterNotes();
        if (present)
        {
            await _deletePoItems.ClickAsync();
            await _btnConfirm.ClickAsync();
        }
        else if (changed)
        {
            await _poSaveButton.ClickAsync();
            //await VerifyOverBudgetIcon();
        }
        if (accept)
        {
            await SubmitAndLogPo(commonContext);
            await AcceptPo(commonContext);
        }
    }

    //Create PO Line items
    public async Task CreateLineItems(Dictionary<int, PoDetais> poDetails, CommonContext commonContext, bool flag, ISpecFlowOutputHelper _specFlowOutputHelper,bool specificGL=false)
    {
        _logger.Debug("{ClassName}.CreateLineItems - Starting with {Count} items, flag={Flag}, specificGL={SpecificGL}", 
            _className, poDetails.Count, flag, specificGL);
        string poNum = await _poNumber.InputValueAsync();
        _logger.Information("{ClassName}.CreateLineItems - Retrieved PO number: {PONumber}", _className, poNum);
        commonContext.PoNumbers.Add(poNum);
        commonContext.PoNumber = poNum;
        var lineItemNumber = poDetails.Count;
        await _poInputTable.WaitForAsync();
        
        // Add additional line items if needed
        if (poDetails.Count > 1) {
            _logger.Debug("{ClassName}.CreateLineItems - Adding {Count} additional line items", _className, poDetails.Count - 1);
            for (var i = 1; i < poDetails.Count; i++)
            {
                await _addLineItemButton.ClickAsync();
            }
        }
        List<string> exempt = DateValidation.GetFilteredCodes(commonContext.GLCodesData);
        List<string> off = DateValidation.GetFilteredCodes(commonContext.GLCodesData, "Off");
        List<string> required = DateValidation.GetFilteredCodes(commonContext.GLCodesData, "Required");
        List<string> gcodes = exempt.Count > 0 ? exempt : off.Count > 0 ? off : required;
        await _poInputTable.WaitForAsync();
        for (var i = 0; i < lineItemNumber; i++)
        {
            var pcount = i + 1;
            PoDetais poDetail = poDetails.ElementAt(i).Value;
            string passglcode = "Exempt";
            await _glCode(pcount).ClickAsync();
            var gCode = "";
            if(!string.IsNullOrEmpty(poDetail.GlCode)){
                passglcode = poDetail.GlCode;
            }
            var d = flag ? 0 : i < gcodes.Count - 1 ? i : (i == 0 ? 0 : (gcodes.Count - 1) % i);
            if (specificGL)
            {
                // If specificGL is true and commonContext.SpecificGL is set, use it
                // Otherwise, select the first GL code from the dropdown
                if (!string.IsNullOrEmpty(commonContext.SpecificGL))
                {
                    gCode = commonContext.SpecificGL;
                }
                else if (gcodes.Count > 0)
                {
                    // Select the first GL code from the dropdown
                    gCode = gcodes[0];
                    
                    // Store the selected GL code in commonContext.SpecificGL for future use
                    commonContext.SpecificGL = gCode;
                }
            }
            else if (!string.IsNullOrEmpty(passglcode))
            { 
                switch (passglcode)
                {
                    case "Exempt":
                        gCode = exempt.Count > 0 ? exempt[d] : gcodes[d];
                        break;

                    case "Required":
                        var s = flag ? 0 : i < required.Count - 1 ? i : (i == 0 ? 0 : (required.Count - 1) % i);
                        gCode = required.Count > 0 ? required[s] : gcodes[d];
                        break;

                    case "OFF": 
                        var k = flag ? 0 : i < off.Count - 1 ? i : (i == 0 ? 0 : (off.Count - 1) % i);
                        gCode = off.Count > 0 ? off[k] : gcodes[d];
                        break;

                    default:
                        gCode = gcodes.Count > 0 ? gcodes[d] : string.Empty;
                    break;

                }  
            }
            else{
                gCode = gcodes.Count > 0 ? gcodes[d] : string.Empty;
            }
            
            // Only type and press Enter if we have a valid GL code
            if (!string.IsNullOrEmpty(gCode))
            {
                await _glCodeEnterInputEnter(pcount).TypeAsync(gCode);
                await Page.Keyboard.PressAsync("Enter");
            }
            else if (specificGL)
            {
                // If specificGL is true but we couldn't find a GL code, 
                // select the first item in the dropdown by clicking on it
                await _glCodeEnterInput(pcount, 1).ClickAsync();
                
                // Get the selected GL code text and store it
                var selectedGLCode = await _glCode(pcount).Locator("span").TextContentAsync() ?? "";
                if (!string.IsNullOrEmpty(selectedGLCode))
                {
                    selectedGLCode = selectedGLCode.Split(" - ")[0].Trim();
                    commonContext.SpecificGL = selectedGLCode;
                    gCode = selectedGLCode;
                }
            }
            await _price(pcount).ClearAsync();
            await _price(pcount).FillAsync(poDetail.Amount.ToString()!);
            var price = await _price(pcount).InputValueAsync();
            commonContext.ItemPrices.Add((float)decimal.Parse(price));
            await _qty(pcount).ClearAsync();
            await _qty(pcount).FillAsync(poDetail.Quantity.ToString()!);
            commonContext.quantities.Add(int.Parse(poDetail.Quantity));
            // Always fill item number and description, even if ItemNumber is null
            string itemNumberValue = poDetail.ItemNumber ?? $"{poNum}-Item{pcount}";
            await _itemNumber(pcount).FillAsync(itemNumberValue);
            commonContext.ItemNumbers.Add(itemNumberValue);
            await _description(pcount).FillAsync(ITEM + pcount);
            if (!string.IsNullOrEmpty(poDetail.UOM) && poDetail.UOM == "LB")
            {
                await _uom(lineItemNumber).SelectOptionAsync(poDetail.UOM);
                await _weight(lineItemNumber).FillAsync(poDetail.Weight.ToString()!);
            }
            if (commonContext.GlCodes.IndexOf(gCode) == -1)
            {
                commonContext.GlCodes.Add(gCode);
            }

        }
        await EnterNotes();
        _specFlowOutputHelper.WriteLine("PONumber=" + poNum);
    }

    //Adding GL Distributions for PO
    public async Task AddTaxDiscountShipping(PoTaxDetails poTaxDetais, CommonContext commonContext)
    {
        _logger.Debug("{ClassName}.AddTaxDiscountShipping - Starting", _className);
        
        if (!string.IsNullOrEmpty(poTaxDetais.Discount))
        {
            _logger.Debug("{ClassName}.AddTaxDiscountShipping - Setting discount: {Discount}, type: {DiscountType}", 
                _className, poTaxDetais.Discount, poTaxDetais.DiscountType);
            await _discountInput.FillAsync(poTaxDetais.Discount.ToString(CultureInfo.InvariantCulture));
            if (poTaxDetais.DiscountType != "percentage")
            {
                await _discountTypeSelect.SelectOptionAsync("0");
                _logger.Debug("{ClassName}.AddTaxDiscountShipping - Set discount type to fixed amount", _className);
            }
        }
        
        if (!string.IsNullOrEmpty(poTaxDetais.Shipping))
        {
            _logger.Debug("{ClassName}.AddTaxDiscountShipping - Setting shipping: {Shipping}, type: {ShippingType}", 
                _className, poTaxDetais.Shipping, poTaxDetais.ShippingType);
            await _shippingInput.FillAsync(poTaxDetais.Shipping.ToString(CultureInfo.InvariantCulture));
            if (poTaxDetais.ShippingType != "percentage")
            {
                await _shippingTypeSelect.SelectOptionAsync("0");
                _logger.Debug("{ClassName}.AddTaxDiscountShipping - Set shipping type to fixed amount", _className);
            }
        }
        
        if (!string.IsNullOrEmpty(poTaxDetais.Tax))
        {
            _logger.Debug("{ClassName}.AddTaxDiscountShipping - Setting tax: {Tax}, type: {TaxType}", 
                _className, poTaxDetais.Tax, poTaxDetais.TaxType);
            await _salesTaxInput.FillAsync(poTaxDetais.Tax.ToString(CultureInfo.InvariantCulture));
            if (poTaxDetais.TaxType != "percentage")
            {
                await _salesTaxTypeSelect.SelectOptionAsync("0");
                _logger.Debug("{ClassName}.AddTaxDiscountShipping - Set tax type to fixed amount", _className);
            }
        }
        
        _logger.Debug("{ClassName}.AddTaxDiscountShipping - Saving PO details", _className);
        await SavePoDetails(commonContext);
        _logger.Debug("{ClassName}.AddTaxDiscountShipping - Completed", _className);
    }

    //Auto Distribute Taxes ,shipping and discount on po Details form
    private async Task CaliculateTaxDescShip(ILocator element, CommonContext commonContext)
    {
        _logger.Debug("{ClassName}.CaliculateTaxDescShip - Starting", _className);
        await element.ClickAsync();
        _logger.Debug("{ClassName}.CaliculateTaxDescShip - Clicked distribution element", _className);
        
        //await SetTaxesGlCode.SelectOptionAsync(new SelectOptionValue { Label = commonContext.GlCodes[0] });
        _logger.Debug("{ClassName}.CaliculateTaxDescShip - Setting GL code: {GLCode}", _className, commonContext.GlCodes[0]);
        await SetTaxesGlCode.PressSequentiallyAsync(commonContext.GlCodes[0]);
        
        if(await IsVisibleAsync(_saveAndCloseBtn,1000,5)){
            _logger.Debug("{ClassName}.CaliculateTaxDescShip - Clicking Save & Close button", _className);
            await _saveAndCloseBtn.ClickAsync();
        }else{
            _logger.Debug("{ClassName}.CaliculateTaxDescShip - Save & Close not found, clicking Cancel button", _className);
            await _buttonCancel.ClickAsync();
        }
        _logger.Debug("{ClassName}.CaliculateTaxDescShip - Completed", _className);
    }

    //Save PO Details
    public async Task SavePoDetails(CommonContext commonContext)
    {
        _logger.Debug("{ClassName}.SavePoDetails - Starting", _className);
        await _poSaveButton.ClickAsync();
        _logger.Debug("{ClassName}.SavePoDetails - Clicked save button", _className);
        
        float shipping = float.Parse(await _shippingInput.InputValueAsync() == string.Empty ? "0" : await _shippingInput.InputValueAsync() ?? "0", CultureInfo.InvariantCulture.NumberFormat);
        if (shipping != 0)
        {
            _logger.Debug("{ClassName}.SavePoDetails - Processing shipping amount: {Shipping}", _className, shipping);
            await CaliculateTaxDescShip(_shippingDistributionButton, commonContext);
        }
        
        float discount = float.Parse(await _discountInput.InputValueAsync() == string.Empty ? "0" : await _discountInput.InputValueAsync() ?? "0", CultureInfo.InvariantCulture.NumberFormat);
        if (discount != 0)
        {
            _logger.Debug("{ClassName}.SavePoDetails - Processing discount amount: {Discount}", _className, discount);
            await CaliculateTaxDescShip(_discountDistributionButton, commonContext);
        }
        
        float tax = float.Parse(await _salesTaxInput.InputValueAsync() == string.Empty ? "0" : await _salesTaxInput.InputValueAsync() ?? "0", CultureInfo.InvariantCulture.NumberFormat);
        if (tax != 0)
        {
            _logger.Debug("{ClassName}.SavePoDetails - Processing tax amount: {Tax}", _className, tax);
            await CaliculateTaxDescShip(_salesTaxDistributionButton, commonContext);
        }
        
        _logger.Debug("{ClassName}.SavePoDetails - Completed", _className);
    }

    //Create Invoice from PO Details form
    public async Task CreateInvoice(CommonContext commonContext)
    {
        _logger.Debug("{ClassName}.CreateInvoice - Starting", _className);
        await WaitUntilRequestComplete(_poSaveButton, "UpdatePurchaseOrderLineItems");
        _logger.Debug("{ClassName}.CreateInvoice - Request completed", _className);
        
        if(!await IsVisibleAsync(_clickInvoice,1000,5)){
            _logger.Error("{ClassName}.CreateInvoice - Invoice Log Button not visible, 701 permission required", _className);
            Assert.Fail("Invoice Log Button not visible enable 701 permission");
        }
        
        //await VerifyOverBudgetIcon();
        _logger.Debug("{ClassName}.CreateInvoice - Clicking Invoice/Credit and Log Only button", _className);
        await _clickInvoice.ClickAsync();
        
        _logger.Debug("{ClassName}.CreateInvoice - Clicking Continue & Submit PO button", _className);
        await _submitContinuePo.ClickAsync();
        
        _logger.Debug("{ClassName}.CreateInvoice - Clicking Continue button", _className);
        await _continuePoBtn.EvaluateAsync("node=>node.click()");
        
        _logger.Debug("{ClassName}.CreateInvoice - Completed", _className);
    }

    public async Task CopyPOUrl(CommonContext commonContext)
    {
        _logger.Debug("{ClassName}.CopyPOUrl - Starting", _className);
        
        if (commonContext.PoUrl == null)
        {
            _logger.Debug("{ClassName}.CopyPOUrl - PoUrl is null, navigating to get URL", _className);
            await _offLinePos.ClickAsync();
            await _offLinePosActionRequired.ClickAsync();
            await _approvalPOLink.ClickAsync();
            commonContext.PoUrl = Page.Url;
            _logger.Information("{ClassName}.CopyPOUrl - Saved PO URL: {URL}", _className, commonContext.PoUrl);
        }
        else
        {
            _logger.Debug("{ClassName}.CopyPOUrl - PoUrl already exists: {URL}", _className, commonContext.PoUrl);
        }
        
        await Task.Run(()=>{});
        _logger.Debug("{ClassName}.CopyPOUrl - Completed", _className);
    }
    public async Task DeleteAndApprovaPO(CommonContext commonContext)
    {
        _logger.Debug("{ClassName}.DeleteAndApprovaPO - Starting", _className);
        
        if (commonContext.PoUrl != null)
        {
            _logger.Debug("{ClassName}.DeleteAndApprovaPO - Navigating to PO URL: {URL}", _className, commonContext.PoUrl);
            await Page.GotoAsync(commonContext.PoUrl, GetPgOptions());
        }
        else
        {
            _logger.Warning("{ClassName}.DeleteAndApprovaPO - No PO URL available", _className);
        }
        
        await Page.WaitForTimeoutAsync(1000);
        
        _logger.Debug("{ClassName}.DeleteAndApprovaPO - Clicking last child input", _className);
        await _lastChildInput.ClickAsync();
        
        _logger.Debug("{ClassName}.DeleteAndApprovaPO - Clicking delete PO items button", _className);
        await _deletePoItems.ClickAsync();
        
        _logger.Debug("{ClassName}.DeleteAndApprovaPO - Confirming deletion", _className);
        await _btnConfirm.ClickAsync();
        
        _logger.Debug("{ClassName}.DeleteAndApprovaPO - Clicking approve button", _className);
        await _approveButton.ClickAsync();
        
        var glCode = commonContext.GlCodes.FirstOrDefault() ?? "";
        _logger.Debug("{ClassName}.DeleteAndApprovaPO - Removing GL code: {GLCode}", _className, glCode);
        commonContext.GlCodes.RemoveAt(0);
        commonContext.PoItemDetails.Remove(glCode);
        
        _logger.Debug("{ClassName}.DeleteAndApprovaPO - Clicking continue button", _className);
        await _continuePoBtn.EvaluateAsync("node=>node.click()");
        
        _logger.Debug("{ClassName}.DeleteAndApprovaPO - Accepting PO", _className);
        await AcceptPo(commonContext);
        
        _logger.Debug("{ClassName}.DeleteAndApprovaPO - Completed", _className);
    }
    //Approve PO
    public async Task ApprovaPO(CommonContext commonContext)
    {
        _logger.Debug("{ClassName}.ApprovaPO - Starting", _className);
        
        if (commonContext.PoUrl != null)
        {
            _logger.Debug("{ClassName}.ApprovaPO - Navigating to PO URL: {URL}", _className, commonContext.PoUrl);
            await Page.GotoAsync(commonContext.PoUrl, GetPgOptions());
        }
        else
        {
            _logger.Warning("{ClassName}.ApprovaPO - No PO URL available", _className);
        }
        
        await Page.WaitForTimeoutAsync(1000);
        
        _logger.Debug("{ClassName}.ApprovaPO - Clicking approve button", _className);
        await _approveButton.ClickAsync();
        
        _logger.Debug("{ClassName}.ApprovaPO - Clicking continue button", _className);
        await _continuePoBtn.EvaluateAsync("node=>node.click()");
        
        _logger.Debug("{ClassName}.ApprovaPO - Accepting PO", _className);
        await AcceptPo(commonContext);
        
        _logger.Debug("{ClassName}.ApprovaPO - Completed", _className);
    }
    private async Task EnterNotes(){
        _logger.Debug("{ClassName}.EnterNotes - Starting", _className);
        
        _logger.Debug("{ClassName}.EnterNotes - Clicking notes button", _className);
        await notesClick.EvaluateAsync("node=>node.click()");
        
        _logger.Debug("{ClassName}.EnterNotes - Waiting for notes data field", _className);
        await notesData.WaitForAsync();
        
        _logger.Debug("{ClassName}.EnterNotes - Filling notes data", _className);
        await notesData.FillAsync("sss");
        
        _logger.Debug("{ClassName}.EnterNotes - Saving notes", _className);
        await hsPopupsaveNotes.EvaluateAsync("node=>node.click()");
        
        _logger.Debug("{ClassName}.EnterNotes - Completed", _className);
    }
    private string getFormatedDate(string period)
    {
        _logger.Debug("{ClassName}.getFormatedDate - Starting with period: {Period}", _className, period);
        
        period = period.Trim();
        var p = period.Split("/");
        var m = p[0].Length < 2 ? "0" + p[0] : p[0];
        var d = p[1].Length < 2 ? "0" + p[1] : p[1];
        string result = m + "/" + d + "/" + p[2].Substring(p[2].Length - 2);
        
        _logger.Debug("{ClassName}.getFormatedDate - Formatted date: {FormattedDate}", _className, result);
        return result;
    }
    //Submit and Log only Button on PO details form
    public async Task SubmitAndLogPo(CommonContext commonContext, bool isPeriods = false, bool isApproval = false)
    {
        await _subTotal.WaitForAsync();
        string subtotal = await _subTotal.TextContentAsync() ?? "0";
        string grandTotal = await _grandTotal.TextContentAsync() ?? "0";
        commonContext.SubTotal = GetAmount(grandTotal) > GetAmount(subtotal) ? GetAmount(grandTotal) : GetAmount(subtotal);
        int ids = 1;
        Dictionary<string, float> glCodes = new Dictionary<string, float>();
        float totalQty = 0;
        foreach (var row in await _poInputTableGl.AllAsync())
        {
            var glCode = await row.Locator(".assignedGL .chosen-container-single span").InnerTextAsync() ?? "";
            glCode = glCode.Trim().Split(" - ")[0];
            float qty = GetAmount(await row.Locator(".quantity input").InputValueAsync());
            if (glCodes.ContainsKey(glCode))
            {
                glCodes[glCode] += qty;
            }
            else
            {
                glCodes.Add(glCode, qty);
            }
            totalQty += qty;
        }
        /*foreach(KeyValuePair<string,float> entry in glCodes){
            Console.WriteLine("key:"+entry.Key+"value:"+entry.Value);
        }*/
        commonContext.QuantityTotal = totalQty;
        var singleP = "";
        if (commonContext.Periods.Count == 0)
        {
            //var glbegin = await _editPeriodsButton.CountAsync()>0 ? (await glPeriod1.InnerTextAsync() ?? "").Trim() : (await glPeriod3.InnerTextAsync() ?? "").Trim();
            var glbegin = await glPeriod1.InnerTextAsync() ?? "";
            glbegin = getFormatedDate(glbegin);
            //var glbegin1 = await _editPeriodsButton.CountAsync()>0 ? (await glPeriod2.InnerTextAsync() ?? "").Trim() : (await glPeriod4.InnerTextAsync() ?? "").Trim();
            var glbegin1 = await glPeriod2.InnerTextAsync() ?? "";
            glbegin1 = getFormatedDate(glbegin1);
            singleP = glbegin + " - " + glbegin1;
            commonContext.Periods.Add(singleP);
        }
        var count = commonContext.Periods.Count;
        if (commonContext.PoItemDetails.Count > 0)
        {
            commonContext.PoItemDetails.Clear();
        }
        foreach (var row in await _glSummary.AllAsync())
        {
            float amount = 0;
            float ship = 0;
            float tax = 0;
            float disc = 0;
            var glCode = await _glSummaryItemGlCode(ids).TextContentAsync() ?? "";
            glCode = glCode.Trim();
            var qty = glCodes[glCode] / count;
            if (commonContext.Periods.Count == 1)
            {
                amount = GetAmount(await _glSummaryItem(ids).TextContentAsync() ?? "$0");
                ship = GetAmount(await _glSummaryItemShip(ids).TextContentAsync() ?? "$0");
                disc = Math.Abs(GetAmount(await _glSummaryItemDesc(ids).TextContentAsync() ?? "$0"));
                tax = GetAmount(await _glSummaryItemTax(ids).TextContentAsync() ?? "$0");
                commonContext.PoItemDetails.Add(glCode + "#" + singleP, new ItemDetails(amount, disc, ship, tax, qty));
            }
            else
            {
                var period = (await _glSummaryItemGLStartPeriod(ids).TextContentAsync() ?? "").Trim();
                amount = GetAmount(await _glSummaryItemGL(ids).TextContentAsync() ?? "$0");
                ship = GetAmount(await _glSummaryItemGlShip(ids).TextContentAsync() ?? "$0");
                disc = Math.Abs(GetAmount(await _glSummaryItemGlDesc(ids).TextContentAsync() ?? "$0"));
                tax = GetAmount(await _glSummaryItemGlTax(ids).TextContentAsync() ?? "$0");
                period = getFormatedDate(period);
                #pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
                string s = commonContext.Periods.SingleOrDefault(p => p.Contains(period));
                #pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.
                if (s == null)
                {
                    s = period;
                }
                if (s.IndexOf("(") != -1)
                {
                    s = s.Split('(', ')')[1];
                }
                commonContext.PoItemDetails.Add(glCode + "#" + s, new ItemDetails(amount, disc, ship, tax, qty));
            }
            ids++;
        }
        if (await IsVisibleAsync(_approveButton,1000,5))
        {
            await _approveButton.ClickAsync();
        }
        if (await IsVisibleAsync(_submitAndLogButton,1000,5))
        {
            await _submitAndLogButton.ClickAsync();
            await _submitContinuePo.EvaluateAsync("node=>node.click()");
            
            // Note: CheckRequireApprovalCheckbox() should be called explicitly when needed
            // It is not automatically called here
            
            if(await IsVisibleAsync(_continuePoBtn, 1000, 5)){
                await _continuePoBtn.EvaluateAsync("node=>node.click()");
            }
        }
        
        if (isApproval)
        {
            commonContext.PoUrl = Page.Url;
            if (await IsVisibleAsync(_isApproval,1000,10))
            {
                await _isApproval.EvaluateAsync("node=>node.click()");
            }

            if (await IsVisibleAsync(_submitForApprovalBtn,1000,5))
            {
                await _submitForApprovalBtn.EvaluateAsync("node=>node.click()");
            }
        }
        if (await IsVisibleAsync(_isApproval,1000,5))
        {
            await _continuePoBtn.EvaluateAsync("node=>node.click()");
        }
        if (isApproval)
        {    
            if(await IsVisibleAsync(proxyLogOffButton,1000,5)){
                await _submitApproval.EvaluateAsync("node=>node.click()");
            }else{
                await submitForApproval1();
            }
        }
    }
    public async Task CheckIfPOApproved(CommonContext commonContext){
        _logger.Debug("{ClassName}.CheckIfPOApproved - Starting", _className);
        
        if(commonContext.PoUrl!=null){
            _logger.Debug("{ClassName}.CheckIfPOApproved - Navigating to PO URL: {URL}", _className, commonContext.PoUrl);
            await Page.GotoAsync(commonContext.PoUrl, GetPgOptions());
            
            if (await IsVisibleAsync(_acceptButton,1000,5))
            {
                _logger.Debug("{ClassName}.CheckIfPOApproved - Accept button is visible, clicking it", _className);
                await _acceptButton.ClickAsync();
                await _okBtn.ClickAsync();
                _logger.Information("{ClassName}.CheckIfPOApproved - PO accepted", _className);
            }
            else
            {
                _logger.Debug("{ClassName}.CheckIfPOApproved - Accept button not visible, PO may already be approved", _className);
            }
        }
        else
        {
            _logger.Warning("{ClassName}.CheckIfPOApproved - No PO URL available", _className);
        }
        
        await Task.Run(()=>{});
        _logger.Debug("{ClassName}.CheckIfPOApproved - Completed", _className);
    }
    //Accept PO which is submitted
    public async Task AcceptPo(CommonContext commonContext,bool directApproval = false)
    {
        await Page.WaitForTimeoutAsync(1000);
        string res = await _submittedPage.InnerTextAsync()??"";
        if(res.IndexOf("Submitted")!=-1 && commonContext.ChangeUser!=null && !await IsVisibleAsync(proxyLogOffButton,1000,5)){
            await _homeLink.ClickAsync();
            await workArea.ClickAsync();
            await changeUserBtn.WaitForAsync();
            if(await IsVisibleAsync(hasLevels,1000,5)){
                    await hasLevels.ClickAsync();
            }
            await changeUserFirstUser(commonContext.ChangeUser).ClickAsync();
            await changeUserBtn.EvaluateAsync("node=>node.click()");
            await notNowButton.EvaluateAsync("node=>node.click()");
            if (commonContext.PoUrl != null)
            {
                await Page.WaitForTimeoutAsync(1000);
                await Page.GotoAsync(commonContext.PoUrl,GetPgOptions());
            }
        }
        if (await IsVisibleAsync(_acceptButton,1000,5))
        {
            var checkTr =commonContext.ChangeUser!=null && !directApproval;
            await _acceptButton.ClickAsync();
            await _okBtn.ClickAsync();
            if(checkTr){
                if(await IsVisibleAsync(proxyLogOffButton,1000,5)){
                    await proxyLogOffButton.EvaluateAsync("node=>node.click()");
                    await notNowButton.EvaluateAsync("node=>node.click()");
                }
            }
        }
        await Task.Run(()=>{});
    }

    private async Task submitForApproval1()
    {
        _logger.Debug("{ClassName}.submitForApproval1 - Starting", _className);
        
        //await _submitForApprovalBtn.ClickAsync();
        _logger.Debug("{ClassName}.submitForApproval1 - Waiting for approval grid to load", _className);
        await Task.Delay(3000);
        
        var totalCount = await _rowsInSubmitForApproval.CountAsync();
        _logger.Debug("{ClassName}.submitForApproval1 - Found {Count} companies in approval grid", _className, totalCount);
        
        bool isApprovalSubmitted = false;
        for (int i = 1; i <= totalCount; ++i)
        {
            if (isApprovalSubmitted) break;
            
            _logger.Debug("{ClassName}.submitForApproval1 - Clicking company grid row {Index}", _className, i + 1);
            await _submitApprovalCompanyGrid(i + 1).ClickAsync();
            
            var totalUsersCount = await _rowsInUserSubmitForApproval.CountAsync();
            _logger.Debug("{ClassName}.submitForApproval1 - Found {Count} users for approval", _className, totalUsersCount);

            for (int j = 1; j <= totalUsersCount; ++j)
            {
                var contactName = await _submitApprovalGrid(j + 1).InnerTextAsync();
                _logger.Debug("{ClassName}.submitForApproval1 - Checking user: {ContactName}", _className, contactName);
                
                if (contactName == "Vice President")
                {
                    _logger.Debug("{ClassName}.submitForApproval1 - Found Vice President, selecting for approval", _className);
                    await _submitApprovalGridselected(j + 1).ClickAsync();
                    
                    _logger.Debug("{ClassName}.submitForApproval1 - Clicking submit button", _className);
                    await _sendForSubmitApproval.EvaluateAsync("node=>node.click()");
                    
                    isApprovalSubmitted = true;
                    _logger.Information("{ClassName}.submitForApproval1 - Approval submitted to Vice President", _className);
                    break;
                }
            }
        }
        
        if (!isApprovalSubmitted) {
            _logger.Warning("{ClassName}.submitForApproval1 - No approval was submitted, Vice President not found", _className);
        }
        
        _logger.Debug("{ClassName}.submitForApproval1 - Completed", _className);
    }

    //Click Edit periods button on PO Details form
    public async Task ClickEditPeriods()
    {
        _logger.Debug("{ClassName}.ClickEditPeriods - Starting", _className);
        
        if(!await IsVisibleAsync(_editPeriodsButton,1000,10)){
            _logger.Error("{ClassName}.ClickEditPeriods - Edit Periods button not visible, 757 permission required", _className);
            Assert.Fail("Enable 757 permission for the user");
        }
        
        _logger.Debug("{ClassName}.ClickEditPeriods - Clicking Edit Periods button", _className);
        await _editPeriodsButton.ClickAsync();
        
        _logger.Debug("{ClassName}.ClickEditPeriods - Completed", _className);
    }
    public async Task SelectExemptGLCode()
    {
        _logger.Debug("{ClassName}.SelectExemptGLCode - Starting", _className);
        
        _logger.Debug("{ClassName}.SelectExemptGLCode - Clicking GL Code Exempt field", _className);
        await _gLCodeExempt.ClickAsync();
        
        _logger.Debug("{ClassName}.SelectExemptGLCode - Filling with 'baseGl'", _className);
        await _selectglcodeexempt.FillAsync("baseGl");
        
        _logger.Debug("{ClassName}.SelectExemptGLCode - Completed", _className);
    }

    private decimal ParseCurrency(string currency)
    {
        _logger.Debug("{ClassName}.ParseCurrency - Parsing currency string: {Currency}", _className, currency);
        
        bool isNegative = currency.Contains("(") && currency.Contains(")");
        string numericString = Regex.Replace(currency, @"[^\d.-]", "");
        
        _logger.Debug("{ClassName}.ParseCurrency - Extracted numeric string: {NumericString}, isNegative: {IsNegative}", 
            _className, numericString, isNegative);
        
        decimal parsedValue = decimal.Parse(numericString, CultureInfo.InvariantCulture);
        decimal result = isNegative ? -parsedValue : parsedValue;
        
        _logger.Debug("{ClassName}.ParseCurrency - Parsed value: {Result}", _className, result);
        return result;
    }
    public async Task POdetailsGLData(CommonContext commonContext)
    {
        _logger.Debug("{ClassName}.POdetailsGLData - Starting", _className);
        
        _logger.Debug("{ClassName}.POdetailsGLData - Getting forecast remaining beginning balance", _className);
        var forecastRemainingBeginingBalance = await forecastRemainingBalance.TextContentAsync() ?? "";
        
        _logger.Debug("{ClassName}.POdetailsGLData - Getting forecast remaining ending balance", _className);
        var forecastRemainingEndingBalance = await forecastEndingBalance.TextContentAsync() ?? "";
        
        bool isBudgetEndingBalVisible = await IsVisibleAsync(budgetEndingBalance, 1000, 5);
        _logger.Debug("{ClassName}.POdetailsGLData - Budget ending balance visible: {IsVisible}", _className, isBudgetEndingBalVisible);
        
        decimal budEndBalance = 0;
        if(isBudgetEndingBalVisible)
        {
            var budEndingBalance = await budgetEndingBalance.TextContentAsync();
            if(!string.IsNullOrWhiteSpace(budEndingBalance))
            {
                budEndBalance = ParseCurrency(budEndingBalance);
                _logger.Debug("{ClassName}.POdetailsGLData - Budget ending balance: {Balance}", _className, budEndBalance);
                commonContext.BudgetRemBalance = budEndBalance;
            }
        }
        
        _logger.Debug("{ClassName}.POdetailsGLData - Getting PO GL total", _className);
        var POGLTotal = await _poDetailsGLTotal.TextContentAsync() ?? "$0.00"; 
        
        _logger.Debug("{ClassName}.POdetailsGLData - Getting period start date", _className);
        var startDate = await PeriodStartDate.TextContentAsync();
        
        _logger.Debug("{ClassName}.POdetailsGLData - Getting period end date", _className);
        var endDate = await PeriodEndDate.TextContentAsync();
        
        _logger.Debug("{ClassName}.POdetailsGLData - Parsing dates - startDate: {StartDate}, endDate: {EndDate}", 
            _className, startDate, endDate);
        string[] formats = { "M/d/yyyy h:mm:ss tt", "M/d/yyyy", "MM/dd/yyyy" }; // Handles both cases

        if (!DateTime.TryParseExact(startDate?.Trim(), formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime start))
        {
            _logger.Error("{ClassName}.POdetailsGLData - Invalid start date format: {StartDate}", _className, startDate);
            throw new FormatException($"Invalid start date format: {startDate}");
        }

        if (!DateTime.TryParseExact(endDate?.Trim(), formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTime end))
        {
            _logger.Error("{ClassName}.POdetailsGLData - Invalid end date format: {EndDate}", _className, endDate);
            throw new FormatException($"Invalid end date format: {endDate}");
        }
        
        start = start.Date;
        end = end.Date;
        
        _logger.Debug("{ClassName}.POdetailsGLData - Parsing currency values", _className);
        decimal beginingBalance = ParseCurrency(forecastRemainingBeginingBalance);
        decimal endingBalance = ParseCurrency(forecastRemainingEndingBalance);
        decimal poGl = ParseCurrency(POGLTotal);
        
        _logger.Debug("{ClassName}.POdetailsGLData - Setting context values - ForecastRemBalance: {EndingBalance}, GlTotal: {PoGl}", 
            _className, endingBalance, poGl);
        commonContext.ForecastRemBalance = endingBalance;
        commonContext.GlTotal = poGl;
        
        _logger.Debug("{ClassName}.POdetailsGLData - Formatting dates for context", _className);
        commonContext.StartDate = start.ToString(start.Day < 10 ? "M/d/yyyy" : "MM/dd/yyyy", CultureInfo.InvariantCulture);
        commonContext.EndDate = end.ToString(end.Day < 10 ? "M/d/yyyy" : "MM/dd/yyyy", CultureInfo.InvariantCulture);
        
        _logger.Debug("{ClassName}.POdetailsGLData - Completed", _className);
    }

    public async Task ValidateActionReqGLData(CommonContext commonContext)
    {
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Starting", _className);
        
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Clicking offline POs action required", _className);
        await _offLinePosActionRequired.ClickAsync();
        
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Filtering by PO number: {PONumber}", _className, commonContext.PoNumber);
        await _filterText.FillAsync(commonContext.PoNumber);
        await Page.Locator("#btnApplyFilter").ClickAsync();
        
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Waiting for filter results", _className);
        await Task.Delay(3000);
        
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Getting action required data", _className);
        var startDate = await _actionRequiredStartDate.TextContentAsync() ?? "";
        var endDate = await _actionRequiredEndDate.TextContentAsync() ?? "";
        var poGlTotal = await _actionRequiredPOGLTotal.TextContentAsync() ?? "";
        
        bool isBudgetRemainingBalVisible = await IsVisibleAsync(_actionRequiredBudgetRemaining, 1000, 5);
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Budget remaining balance visible: {IsVisible}", _className, isBudgetRemainingBalVisible);
        
        decimal actionBudEndBalance = 0; 
        if(isBudgetRemainingBalVisible)
        {
            var budRemainingBalance = await _actionRequiredBudgetRemaining.TextContentAsync();
            if(!string.IsNullOrWhiteSpace(budRemainingBalance))
            {
                actionBudEndBalance = ParseCurrency(budRemainingBalance);
                _logger.Debug("{ClassName}.ValidateActionReqGLData - Action budget end balance: {Balance}, expected: {Expected}", 
                    _className, actionBudEndBalance, commonContext.BudgetRemBalance);
                Assert.That(actionBudEndBalance, Is.EqualTo(commonContext.BudgetRemBalance));
            }
        }
        
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Getting forecast remaining balance", _className);
        var forecastRemainingBalance = await _actionRequiredForecastRemaining.TextContentAsync() ?? "$0.00";
        decimal forecatEndBal = ParseCurrency(forecastRemainingBalance);
        
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Parsing dates for comparison", _className);
        DateTime start = DateTime.Parse(startDate, CultureInfo.InvariantCulture);
        DateTime end = DateTime.Parse(endDate, CultureInfo.InvariantCulture);
        DateTime commonContextStartDate = DateTime.Parse(commonContext.StartDate, CultureInfo.InvariantCulture);
        DateTime commonContextEndDate = DateTime.Parse(commonContext.EndDate, CultureInfo.InvariantCulture);
        
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Parsing PO GL total", _className);
        decimal poGl = ParseCurrency(poGlTotal);
        
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Validating start date - actual: {Actual}, expected: {Expected}", 
            _className, start, commonContextStartDate);
        Assert.That(start, Is.EqualTo(commonContextStartDate));
        
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Validating end date - actual: {Actual}, expected: {Expected}", 
            _className, end, commonContextEndDate);
        Assert.That(end, Is.EqualTo(commonContextEndDate));
        
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Validating PO GL total - actual: {Actual}, expected: {Expected}", 
            _className, poGl, commonContext.GlTotal);
        Assert.That(poGl, Is.EqualTo(commonContext.GlTotal));
        
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Validating forecast end balance - actual: {Actual}, expected: {Expected}", 
            _className, forecatEndBal, commonContext.ForecastRemBalance);
        Assert.That(forecatEndBal, Is.EqualTo(commonContext.ForecastRemBalance));
        
        _logger.Debug("{ClassName}.ValidateActionReqGLData - Completed", _className);
    }
    public async Task OverBudgetIconDisplay()
    {
        _logger.Debug("{ClassName}.OverBudgetIconDisplay - Starting", _className);
        
        _logger.Debug("{ClassName}.OverBudgetIconDisplay - Clicking save button", _className);
        await _poSaveButton.ClickAsync();
        
        _logger.Debug("{ClassName}.OverBudgetIconDisplay - Getting forecast balances", _className);
        var forecastRemainingBeginingBalance = await forecastRemainingBalance.TextContentAsync() ?? "";
        var forecastRemainingEndingBalance = await forecastEndingBalance.TextContentAsync() ?? "";
        
        _logger.Debug("{ClassName}.OverBudgetIconDisplay - Parsing balances", _className);
        decimal beginingBalance = ParseCurrency(forecastRemainingBeginingBalance);
        decimal endingBalance = ParseCurrency(forecastRemainingEndingBalance);
        
        _logger.Debug("{ClassName}.OverBudgetIconDisplay - Comparing balances - beginning: {Beginning}, ending: {Ending}", 
            _className, beginingBalance, endingBalance);
        
        if(endingBalance > beginingBalance)
        {
            _logger.Debug("{ClassName}.OverBudgetIconDisplay - Ending balance exceeds beginning balance, checking for over budget icon", _className);
            if(await IsVisibleAsync(overBudgetIcon, 1000, 5)){
                _logger.Debug("{ClassName}.OverBudgetIconDisplay - Over budget icon is visible", _className);
                Assert.That(await overBudgetIcon.CountAsync() > 0);
            } else {
                _logger.Warning("{ClassName}.OverBudgetIconDisplay - Over budget icon not displayed, permission may be OFF", _className);
                Assert.Fail("Over Budget Icon is not displayed because of Over Budget Permission is OFF");
            }
        }
        else
        {
            _logger.Debug("{ClassName}.OverBudgetIconDisplay - Ending balance does not exceed beginning balance, no icon expected", _className);
        }
        
        _logger.Debug("{ClassName}.OverBudgetIconDisplay - Completed", _className);
    }
    public async Task OverSpendingLimitIcon()
    {
        _logger.Debug("{ClassName}.OverSpendingLimitIcon - Starting", _className);
        
        _logger.Debug("{ClassName}.OverSpendingLimitIcon - Clicking save button", _className);
        await _poSaveButton.ClickAsync();
        
        _logger.Debug("{ClassName}.OverSpendingLimitIcon - Locating spending limit text", _className);
        var spendingLimitLocator = Page.Locator("span", new PageLocatorOptions { HasTextString = "Spending Limit:" });
        
        _logger.Debug("{ClassName}.OverSpendingLimitIcon - Getting text color", _className);
        var color = await spendingLimitLocator.EvaluateAsync<string>("element => getComputedStyle(element).color");
        
        _logger.Debug("{ClassName}.OverSpendingLimitIcon - Waiting for spending limit text", _className);
        await spendingLimitLocator.WaitForAsync(new() { Timeout = 3000 });
        
        _logger.Debug("{ClassName}.OverSpendingLimitIcon - Checking text color: {Color}", _className, color);
        if (color == "rgb(255, 0, 0)") // Red color in RGB
        {
            _logger.Debug("{ClassName}.OverSpendingLimitIcon - Text is red, checking for over spending icon", _className);
            Assert.That(await _overSpendingIcon.CountAsync() > 0);
            _logger.Information("{ClassName}.OverSpendingLimitIcon - Over spending icon is visible", _className);
        }
        else
        {
            _logger.Debug("{ClassName}.OverSpendingLimitIcon - Text is not red, no icon expected", _className);
        }
        
        _logger.Debug("{ClassName}.OverSpendingLimitIcon - Completed", _className);
    }
    private async Task ValidateIconVisibilityAsync(ILocator iconLocator, string iconName, bool isPermissionEnabled)
    {
        _logger.Debug("{ClassName}.ValidateIconVisibilityAsync - Starting for {IconName}", _className, iconName);
        
        bool isVisible = await IsVisibleAsync(iconLocator, 1000, 5);
        _logger.Debug("{ClassName}.ValidateIconVisibilityAsync - Icon {IconName} visible: {IsVisible}", _className, iconName, isVisible);

        if (isPermissionEnabled)
        {
            _logger.Debug("{ClassName}.ValidateIconVisibilityAsync - {IconName} visibility skipped, parent has no limit", _className, iconName);
            Console.WriteLine($"{iconName} visibility skipped parent has no limit");
        }
        else
        {
            _logger.Debug("{ClassName}.ValidateIconVisibilityAsync - {IconName} visibility skipped due to permission being disabled", _className, iconName);
            Console.WriteLine($"{iconName} visibility skipped due to permission being disabled.");
        }
        
        _logger.Debug("{ClassName}.ValidateIconVisibilityAsync - Completed for {IconName}", _className, iconName);
    }
    public async Task ValidateOverSpendingIconAsync(bool isPermissionEnabled)
    {
        _logger.Debug("{ClassName}.ValidateOverSpendingIconAsync - Starting with isPermissionEnabled={IsEnabled}", _className, isPermissionEnabled);
        await ValidateIconVisibilityAsync(_actionReqoverSpendingIcon, "Over Spending Icon", isPermissionEnabled);
        _logger.Debug("{ClassName}.ValidateOverSpendingIconAsync - Completed", _className);
    }

    public async Task ValidateOverBudgetIconAsync(bool isPermissionEnabled)
    {
        _logger.Debug("{ClassName}.ValidateOverBudgetIconAsync - Starting with isPermissionEnabled={IsEnabled}", _className, isPermissionEnabled);
        await ValidateIconVisibilityAsync(_actionReqOverBudgetIcon, "Over Budget Icon", isPermissionEnabled);
        _logger.Debug("{ClassName}.ValidateOverBudgetIconAsync - Completed", _className);
    }

    public async Task ValidateNotInAValidPeriodIconAsync(bool isPermissionEnabled)
    {
        _logger.Debug("{ClassName}.ValidateNotInAValidPeriodIconAsync - Starting with isPermissionEnabled={IsEnabled}", _className, isPermissionEnabled);
        await ValidateIconVisibilityAsync(_ActionReqNotInCurrentPeriod, "Not In A Valid Period Icon", isPermissionEnabled);
        _logger.Debug("{ClassName}.ValidateNotInAValidPeriodIconAsync - Completed", _className);
    }

    public async Task ValidateNotInCurrentPeriodIconAsync(bool isPermissionEnabled)
    {
        _logger.Debug("{ClassName}.ValidateNotInCurrentPeriodIconAsync - Starting with isPermissionEnabled={IsEnabled}", _className, isPermissionEnabled);
        await ValidateIconVisibilityAsync(_ActionReqNotInCurrentPeriod, "Not In Current Period Icon", isPermissionEnabled);
        _logger.Debug("{ClassName}.ValidateNotInCurrentPeriodIconAsync - Completed", _className);
    }
    public async Task NotAValidBudgetPeriod()
    {   
        _logger.Debug("{ClassName}.NotAValidBudgetPeriod - Starting", _className);
        
        _logger.Debug("{ClassName}.NotAValidBudgetPeriod - Clicking budget next button", _className);
        await glBudgetNext.ClickAsync();
        
        _logger.Debug("{ClassName}.NotAValidBudgetPeriod - Clicking save button", _className);
        await _poSaveButton.ClickAsync();
        
        _logger.Debug("{ClassName}.NotAValidBudgetPeriod - Completed", _className);
    }
    
    // Helper methods for approval process
    public async Task CheckRequireApprovalCheckbox()
    {
        _logger.Debug("{ClassName}.CheckRequireApprovalCheckbox - Starting", _className);
        
        var isApprovalCheckbox = Page.Locator("#chkRequireApproval");
        bool isVisible = await IsVisibleAsync(isApprovalCheckbox, 1000, 5);
        _logger.Debug("{ClassName}.CheckRequireApprovalCheckbox - Approval checkbox visible: {IsVisible}", _className, isVisible);
        
        if (isVisible)
        {
            bool isChecked = await isApprovalCheckbox.IsCheckedAsync();
            _logger.Debug("{ClassName}.CheckRequireApprovalCheckbox - Approval checkbox is checked: {IsChecked}", _className, isChecked);
            
            if (!isChecked)
            {
                _logger.Debug("{ClassName}.CheckRequireApprovalCheckbox - Checking approval checkbox", _className);
                await isApprovalCheckbox.CheckAsync();
                Console.WriteLine("Checked 'Require Approval' checkbox");
            }
        }
        
        _logger.Debug("{ClassName}.CheckRequireApprovalCheckbox - Completed", _className);
    }
    
    // Method to check if the approval checkbox is visible and check it if needed
    // This should be called after SubmitAndLogPo when approval is needed
    public async Task CheckApprovalCheckboxIfVisible()
    {
        _logger.Debug("{ClassName}.CheckApprovalCheckboxIfVisible - Starting", _className);
        
        // Wait a moment for the checkbox to appear after the Continue & Submit PO button is clicked
        _logger.Debug("{ClassName}.CheckApprovalCheckboxIfVisible - Waiting for checkbox to appear", _className);
        await Page.WaitForTimeoutAsync(1000);
        
        // Check the "Require Approval" checkbox if it appears
        _logger.Debug("{ClassName}.CheckApprovalCheckboxIfVisible - Calling CheckRequireApprovalCheckbox", _className);
        await CheckRequireApprovalCheckbox();
        
        _logger.Debug("{ClassName}.CheckApprovalCheckboxIfVisible - Completed", _className);
    }
    
    public async Task ClickSubmitForApprovalButton()
    {
        _logger.Debug("{ClassName}.ClickSubmitForApprovalButton - Starting", _className);
        
        var submitForApprovalBtn = Page.Locator("button:has-text('Submit For Approval')");
        bool isVisible = await IsVisibleAsync(submitForApprovalBtn, 1000, 5);
        _logger.Debug("{ClassName}.ClickSubmitForApprovalButton - Submit For Approval button visible: {IsVisible}", _className, isVisible);
        
        if (isVisible)
        {
            _logger.Debug("{ClassName}.ClickSubmitForApprovalButton - Clicking Submit For Approval button", _className);
            await submitForApprovalBtn.ClickAsync();
            Console.WriteLine("Clicked Submit For Approval button");
        }
        
        _logger.Debug("{ClassName}.ClickSubmitForApprovalButton - Completed", _className);
    }
    
    // Method to send approval to Jason - Stackular - Manager
    public async Task SubmitForJasonStackularManagerApproval()
    {
        _logger.Debug("{ClassName}.SubmitForJasonStackularManagerApproval - Starting", _className);
        
        _logger.Debug("{ClassName}.SubmitForJasonStackularManagerApproval - Waiting for approval grid to load", _className);
        await Task.Delay(3000);
        
        _logger.Debug("{ClassName}.SubmitForJasonStackularManagerApproval - Locating Jason - Stackular - Manager element", _className);
        var element = Page.Locator("td[role='gridcell'][title='Jason - Stackular - Manager'] > span.cell-wrapper");
        await Page.WaitForSelectorAsync("td[role='gridcell'][title='Jason - Stackular - Manager'] > span.cell-wrapper");
        
        _logger.Debug("{ClassName}.SubmitForJasonStackularManagerApproval - Highlighting element", _className);
        await element.HighlightAsync();
        
        _logger.Debug("{ClassName}.SubmitForJasonStackularManagerApproval - Clicking element", _className);
        await element.ClickAsync();
        
        _logger.Debug("{ClassName}.SubmitForJasonStackularManagerApproval - Clicking submit button", _className);
        await _sendForSubmitApproval.EvaluateAsync("node => node.click()");
        
        _logger.Debug("{ClassName}.SubmitForJasonStackularManagerApproval - Completed", _className);
    }
    
    // Method to submit a PO for approval to Jason-Stackular Manager
    public async Task SubmitPOForJasonStackularManagerApproval(CommonContext commonContext)
    {
        // First gather all the PO data
        await _subTotal.WaitForAsync();
        string subtotal = await _subTotal.TextContentAsync() ?? "0";
        string grandTotal = await _grandTotal.TextContentAsync() ?? "0";
        commonContext.SubTotal = GetAmount(grandTotal) > GetAmount(subtotal) ? GetAmount(grandTotal) : GetAmount(subtotal);
        
        // Calculate GL codes and quantities
        int ids = 1;
        Dictionary<string, float> glCodes = new Dictionary<string, float>();
        float totalQty = 0;
        foreach (var row in await _poInputTableGl.AllAsync())
        {
            var glCode = await row.Locator(".assignedGL .chosen-container-single span").InnerTextAsync() ?? "";
            glCode = glCode.Trim().Split(" - ")[0];
            float qty = GetAmount(await row.Locator(".quantity input").InputValueAsync());
            if (glCodes.ContainsKey(glCode))
            {
                glCodes[glCode] += qty;
            }
            else
            {
                glCodes.Add(glCode, qty);
            }
            totalQty += qty;
        }
        
        commonContext.QuantityTotal = totalQty;
        
        // Handle periods
        var singleP = "";
        if (commonContext.Periods.Count == 0)
        {
            var glbegin = await glPeriod1.InnerTextAsync() ?? "";
            glbegin = getFormatedDate(glbegin);
            var glbegin1 = await glPeriod2.InnerTextAsync() ?? "";
            glbegin1 = getFormatedDate(glbegin1);
            singleP = glbegin + " - " + glbegin1;
            commonContext.Periods.Add(singleP);
        }
        
        // Process GL summary data
        var count = commonContext.Periods.Count;
        if (commonContext.PoItemDetails.Count > 0)
        {
            commonContext.PoItemDetails.Clear();
        }
        
        foreach (var row in await _glSummary.AllAsync())
        {
            float amount = 0;
            float ship = 0;
            float tax = 0;
            float disc = 0;
            var glCode = await _glSummaryItemGlCode(ids).TextContentAsync() ?? "";
            glCode = glCode.Trim();
            var qty = glCodes[glCode] / count;
            
            if (commonContext.Periods.Count == 1)
            {
                amount = GetAmount(await _glSummaryItem(ids).TextContentAsync() ?? "$0");
                ship = GetAmount(await _glSummaryItemShip(ids).TextContentAsync() ?? "$0");
                disc = Math.Abs(GetAmount(await _glSummaryItemDesc(ids).TextContentAsync() ?? "$0"));
                tax = GetAmount(await _glSummaryItemTax(ids).TextContentAsync() ?? "$0");
                commonContext.PoItemDetails.Add(glCode + "#" + singleP, new ItemDetails(amount, disc, ship, tax, qty));
            }
            else
            {
                var period = (await _glSummaryItemGLStartPeriod(ids).TextContentAsync() ?? "").Trim();
                amount = GetAmount(await _glSummaryItemGL(ids).TextContentAsync() ?? "$0");
                ship = GetAmount(await _glSummaryItemGlShip(ids).TextContentAsync() ?? "$0");
                disc = Math.Abs(GetAmount(await _glSummaryItemGlDesc(ids).TextContentAsync() ?? "$0"));
                tax = GetAmount(await _glSummaryItemGlTax(ids).TextContentAsync() ?? "$0");
                period = getFormatedDate(period);
                #pragma warning disable CS8600 // Converting null literal or possible null value to non-nullable type.
                string s = commonContext.Periods.SingleOrDefault(p => p.Contains(period));
                #pragma warning restore CS8600 // Converting null literal or possible null value to non-nullable type.
                if (s == null)
                {
                    s = period;
                }
                if (s.IndexOf("(") != -1)
                {
                    s = s.Split('(', ')')[1];
                }
                commonContext.PoItemDetails.Add(glCode + "#" + s, new ItemDetails(amount, disc, ship, tax, qty));
            }
            ids++;
        }
        
        // Click the Submit and Log Only button
        if (await IsVisibleAsync(_submitAndLogButton, 1000, 5))
        {
            await _submitAndLogButton.ClickAsync();
            
            // Click the Continue & Submit PO button
            await _submitContinuePo.EvaluateAsync("node=>node.click()");
            
            // Check the "Require Approval" checkbox
            await CheckRequireApprovalCheckbox();
            
            // Click the Continue button after checking the checkbox
            if (await IsVisibleAsync(_continuePoBtn, 1000, 5)) {
                await _continuePoBtn.EvaluateAsync("node=>node.click()");
                Console.WriteLine("Clicked Continue button after checking Require Approval checkbox");
            }
            
            // Click the Submit For Approval button
            await ClickSubmitForApprovalButton();
            
            // Select Jason-Stackular Manager as the approver
            await SubmitForJasonStackularManagerApproval();
        }
    }
    public async Task AcceptApprovalPO()
    {
        _logger.Debug("{ClassName}.AcceptApprovalPO - Starting", _className);
        
        _logger.Debug("{ClassName}.AcceptApprovalPO - Selecting PO", _className);
        await _selectPO.ClickAsync();
        
        _logger.Debug("{ClassName}.AcceptApprovalPO - Calling AcceptSubmittedPO", _className);
        await AcceptSubmittedPO();
        
        _logger.Debug("{ClassName}.AcceptApprovalPO - Completed", _className);
    }

    public async Task AcceptmultiplePOs(CommonContext commonContext)
    {
        _logger.Debug("{ClassName}.AcceptmultiplePOs - Starting", _className);
        
        _logger.Debug("{ClassName}.AcceptmultiplePOs - Clicking offline POs action required", _className);
        await _offLinePosActionRequired.ClickAsync();
        
        _logger.Debug("{ClassName}.AcceptmultiplePOs - Clicking status date", _className);
        await _statusDate.ClickAsync();
        
        _logger.Debug("{ClassName}.AcceptmultiplePOs - Waiting for PO list to load", _className);
        await Task.Delay(5000);
        
        _logger.Debug("{ClassName}.AcceptmultiplePOs - Getting all PO rows", _className);
        var rows = await Page.Locator("#poList tbody tr.ui-widget-content").AllAsync();
        
        var poNumbers = new HashSet<string> { commonContext.PoNumbers[0], commonContext.PoNumbers[1] };
        _logger.Debug("{ClassName}.AcceptmultiplePOs - Looking for PO numbers: {PONumbers}", 
            _className, string.Join(", ", poNumbers));
        
        foreach (var row in rows)
        {
            var poElement = row.Locator("td:nth-child(11) a");

            if (await IsVisibleAsync(poElement, 1000, 5))
            {
                var poText = (await poElement.TextContentAsync() ?? "").Trim();
                _logger.Debug("{ClassName}.AcceptmultiplePOs - Found PO: {PONumber}", _className, poText);

                if (poNumbers != null && poNumbers.Contains(poText))
                {
                    _logger.Debug("{ClassName}.AcceptmultiplePOs - Selecting PO: {PONumber}", _className, poText);
                    await row.ClickAsync();
                }
            }
        }
        
        _logger.Debug("{ClassName}.AcceptmultiplePOs - Calling AcceptSubmittedPO", _className);
        await AcceptSubmittedPO();
        
        _logger.Debug("{ClassName}.AcceptmultiplePOs - Completed", _className);
    }
    private async Task AcceptSubmittedPO()
    {
        _logger.Debug("{ClassName}.AcceptSubmittedPO - Starting", _className);
        
        _logger.Debug("{ClassName}.AcceptSubmittedPO - Clicking Accept PO button", _className);
        await _AcceptPO.ClickAsync();
        
        _logger.Debug("{ClassName}.AcceptSubmittedPO - Clicking confirm accept button", _className);
        await _confirmAccept.ClickAsync();
        
        bool isSubmitforFurtherApproval = await IsVisibleAsync(_submitForFurtherApproval, 1000, 5);
        _logger.Debug("{ClassName}.AcceptSubmittedPO - Submit for further approval visible: {IsVisible}", 
            _className, isSubmitforFurtherApproval);
        
        if(isSubmitforFurtherApproval)
        {
            _logger.Debug("{ClassName}.AcceptSubmittedPO - Closing further approval popup", _className);
            await _closeFurtherApprovalPopup.ClickAsync();
        }
        
        _logger.Debug("{ClassName}.AcceptSubmittedPO - Completed", _className);
    }
}
