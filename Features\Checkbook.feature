@Checkbook
Feature: Manage View/Edit Checkbook Page
@checkbookGrid
Scenario Outline: 60_vaidate page review buttons and grid present
	Given Open the Web url 
	Then Login to site with username and password
	Then HomePage Loaded
    Then Open checkbook
	Then Select Department and ShowAll Filter And Verify Data
	Then Verify Grid fields for PeriodView
		| columns | 	
		| Code |
		| Description |
		| Forecast | 
		| Planned | 
		| Committed  | 
		| Actualized  | 
		| $ |
		| % |
		| Transaction Log |
	 	| Fixed Code |
	 	| Active Code |
	Then Validate and filter fields
     | columns               | filterValues | indexValues |
     | code                  | 7            | 3           |
     | description           | L            | 4           |
     | budgetAmount          | 0            | 5           |
     | plannedAmt            | 0            | 6           |
     | committedAmt           | 0            | 7           |
	 | actualizedAmt           | 0            | 8           |
	Then Download Excel and Verify Checkbook excel grid fields
     | columns           |
	 | Department      |
     | Company Name      |
     | GL Code           |
     | Description       |
     | Forecast Amt     |
     | Planned Amt       |
     | Committed Amt      |
	 | Actualized Amt      |
     | Remaining Balance$ |
	 | Remaining Balance% |
	 | Fixed Code |
	 | Active Code | 
	Then Click Year View
	Then Verify Grid fields for YearView
		| columns | 	
		| Code |
		| Description |
	 	| Fixed Code |
	 	| Active Code |

@DeleteUndeleteActionsInPeriodView
Scenario Outline: 61_Add/Edit and delete/undelete GLCode increase Forcast amount 
	Given Open the Web url 
	Then Login to site with username and password
	Then HomePage Loaded
    Then Open checkbook With GlMasks
	Then Add GLCodes and save
		|forcast amount|Fixed|active|
        | 1 | no | yes |
	Then Update/Verify Adjust Forecast Amount by %
	Then Add/Verify journal entry	
	Then Edit and Verify GLCode Description, Forecast Amount, Active and Fixed Codes
	Then Delete/verify GLCodes
	Then Undelete/Verify GLCodes

@DeleteUndeleteActionsInYearView
Scenario Outline: 62_Add/Edit and delete/undelete GLCode period view increase Forcast amount 
	Given Open the Web url 
	Then Login to site with username and password
	Then HomePage Loaded
    Then Open checkbook With GlMasks
	Then Click Year View
	Then Add GLCodes and save with period copyacross
		|forcast amount|budget amount |Fixed|active|
        | 1 | 1 | no | yes |
	Then Edit and Verify GLCode Description, Forecast Amount, Active and Fixed Codes(yearview)
	Then Delete/verify GLCodes(yearview)
	Then Undelete/Verify GLCodes(yearview)

@OFFGLCODECONVERTINTOEXEMPTRequiredforPOWorklfows
Scenario Outline: Verify Exempt functionality in PO Approval column
	Given Open the Web url 
	Then Login to site with username and password
	Then HomePage Loaded
    Then Open checkbook
	Then Verify Exempt Assign users in PO Workflows
@OFFGLCODECONVERTINTOExemptRequiredforInvoiceWorkflows
Scenario Outline: Verify Exempt functionality in Invoice Approval column
	Given Open the Web url 
	Then Login to site with username and password
	Then HomePage Loaded
    Then Open checkbook
	Then Verify Exempt Assign users in Invoice Workflows